
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "max-age=0,no-cache,no-store,must-revalidate"

[[headers]]
  for = "/manifest.json"
  [headers.values]
    Content-Type = "application/manifest+json"

# General rate limiting for all API endpoints
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200
  force = true
  [redirects.headers]
    X-Rate-Limit = "120"  # 120 requests per minute
    X-Rate-Limit-Period = "60s"

# Stricter rate limiting for authentication endpoints
[[redirects]]
  from = "/auth/*"
  to = "/.netlify/functions/auth/:splat"
  status = 200
  force = true
  [redirects.headers]
    X-Rate-Limit = "20"  # 20 requests per minute
    X-Rate-Limit-Period = "60s"

