
-- Fix message_reactions and message_delivery_status policies to avoid infinite recursion
-- Use DROP POLICY IF EXISTS to handle cases where policies might already exist

-- Drop and recreate message_reactions policies
DROP POLICY IF EXISTS "Users can view reactions on direct messages they participate in" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can add reactions to direct messages they participate in" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can remove their own reactions" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can view all message reactions" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can add their own reactions" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can delete their own reactions" ON public.message_reactions;

-- Create simpler policies that don't cause recursion
CREATE POLICY "Users can view all message reactions"
  ON public.message_reactions FOR SELECT
  USING (true);

CREATE POLICY "Users can add their own reactions"
  ON public.message_reactions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reactions"
  ON public.message_reactions FOR DELETE
  USING (auth.uid() = user_id);

-- Drop and recreate message_delivery_status policies
DROP POLICY IF EXISTS "Users can view delivery status for direct messages they sent" ON public.message_delivery_status;
DROP POLICY IF EXISTS "Users can update their own delivery status for direct messages" ON public.message_delivery_status;
DROP POLICY IF EXISTS "Users can view delivery status for their messages" ON public.message_delivery_status;
DROP POLICY IF EXISTS "Users can update their own delivery status" ON public.message_delivery_status;

CREATE POLICY "Users can view delivery status for their messages"
  ON public.message_delivery_status FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own delivery status"
  ON public.message_delivery_status FOR ALL
  USING (auth.uid() = user_id);
