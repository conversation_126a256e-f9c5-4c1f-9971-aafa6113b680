-- Create function to handle promotion subscription updates
CREATE OR REPLACE FUNCTION public.handle_promotion_subscription_update(
  user_email text,
  stripe_customer_id text,
  subscription_status text,
  current_period_end bigint
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  target_user_id uuid;
BEGIN
  -- Find user by email
  SELECT auth.users.id INTO target_user_id
  FROM auth.users
  WHERE auth.users.email = user_email;
  
  IF target_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found with email: %', user_email;
  END IF;
  
  -- Update the user's promotion status
  IF subscription_status = 'active' THEN
    UPDATE public.profiles
    SET 
      is_promoted = true,
      promoted_until = to_timestamp(current_period_end),
      stripe_customer_id = handle_promotion_subscription_update.stripe_customer_id,
      updated_at = now()
    WHERE id = target_user_id;
  ELSE
    UPDATE public.profiles
    SET 
      is_promoted = false,
      promoted_until = null,
      updated_at = now()
    WHERE id = target_user_id;
  END IF;
END;
$$;