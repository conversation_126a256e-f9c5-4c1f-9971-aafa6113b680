
-- Create the message-attachments storage bucket (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'message-attachments') THEN
        INSERT INTO storage.buckets (id, name, public)
        VALUES ('message-attachments', 'message-attachments', true);
    END IF;
END $$;

-- Drop existing storage policies if they exist and recreate them
DROP POLICY IF EXISTS "Authenticated users can upload message attachments" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can view message attachments" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own message attachments" ON storage.objects;

-- Create RLS policies for the storage bucket
CREATE POLICY "Authenticated users can upload message attachments"
ON storage.objects FOR INSERT 
WITH CHECK (bucket_id = 'message-attachments' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view message attachments"
ON storage.objects FOR SELECT 
USING (bucket_id = 'message-attachments' AND auth.role() = 'authenticated');

CREATE POLICY "Users can delete their own message attachments"
ON storage.objects FOR DELETE 
USING (bucket_id = 'message-attachments' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Fix message_reactions and message_delivery_status policies to avoid infinite recursion
DROP POLICY IF EXISTS "Users can view reactions on direct messages they participate in" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can add reactions to direct messages they participate in" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can remove their own reactions" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can view all message reactions" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can add their own reactions" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can delete their own reactions" ON public.message_reactions;

-- Create simpler policies that don't cause recursion
CREATE POLICY "Users can view all message reactions"
  ON public.message_reactions FOR SELECT
  USING (true);

CREATE POLICY "Users can add their own reactions"
  ON public.message_reactions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reactions"
  ON public.message_reactions FOR DELETE
  USING (auth.uid() = user_id);

-- Drop and recreate message_delivery_status policies
DROP POLICY IF EXISTS "Users can view delivery status for direct messages they sent" ON public.message_delivery_status;
DROP POLICY IF EXISTS "Users can update their own delivery status for direct messages" ON public.message_delivery_status;
DROP POLICY IF EXISTS "Users can view delivery status for their messages" ON public.message_delivery_status;
DROP POLICY IF EXISTS "Users can update their own delivery status" ON public.message_delivery_status;

CREATE POLICY "Users can view delivery status for their messages"
  ON public.message_delivery_status FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own delivery status"
  ON public.message_delivery_status FOR ALL
  USING (auth.uid() = user_id);

-- Fix the infinite recursion in group_chat_members policies
DROP POLICY IF EXISTS "Members can view group membership" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group owners can add members" ON public.group_chat_members;
DROP POLICY IF EXISTS "Users can leave groups" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group owners can remove members" ON public.group_chat_members;

-- Create simpler policies that use the security definer functions
CREATE POLICY "Members can view group membership"
  ON public.group_chat_members FOR SELECT
  USING (
    user_id = auth.uid() OR 
    public.check_group_membership(group_chat_id, auth.uid()) OR
    public.check_group_ownership(group_chat_id, auth.uid())
  );

CREATE POLICY "Group owners can add members"
  ON public.group_chat_members FOR INSERT
  WITH CHECK (public.check_group_ownership(group_chat_id, auth.uid()));

CREATE POLICY "Users can leave groups"
  ON public.group_chat_members FOR DELETE
  USING (auth.uid() = user_id);

CREATE POLICY "Group owners can remove members"
  ON public.group_chat_members FOR DELETE
  USING (public.check_group_ownership(group_chat_id, auth.uid()));
