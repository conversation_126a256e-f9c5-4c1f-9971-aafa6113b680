
-- Enable group chats by fixing RLS policies (drop existing ones first)

-- Drop all existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view group chats they are members of" ON public.group_chats;
DROP POLICY IF EXISTS "Users can create group chats" ON public.group_chats;
DROP POLICY IF EXISTS "Users can update their own group chats" ON public.group_chats;
DROP POLICY IF EXISTS "Users can view their own group memberships" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group creators can manage memberships" ON public.group_chat_members;
DROP POLICY IF EXISTS "Users can leave groups" ON public.group_chat_members;
DROP POLICY IF EXISTS "Users can view group messages" ON public.messages;
DROP POLICY IF EXISTS "Users can send group messages" ON public.messages;

-- Create policies for group messages using the security definer functions
CREATE POLICY "Users can view group messages"
  ON public.messages FOR SELECT
  USING (
    message_type = 'group' AND 
    group_chat_id IS NOT NULL AND
    public.check_group_membership(group_chat_id, auth.uid())
  );

CREATE POLICY "Users can send group messages"
  ON public.messages FOR INSERT
  WITH CHECK (
    message_type = 'group' AND 
    group_chat_id IS NOT NULL AND
    sender_id = auth.uid() AND
    public.check_group_membership(group_chat_id, auth.uid())
  );

-- Create group chat policies
CREATE POLICY "Users can view group chats they are members of"
  ON public.group_chats FOR SELECT
  USING (public.check_group_membership(id, auth.uid()));

CREATE POLICY "Users can create group chats"
  ON public.group_chats FOR INSERT
  WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own group chats"
  ON public.group_chats FOR UPDATE
  USING (auth.uid() = created_by);

-- Create group_chat_members policies
CREATE POLICY "Users can view their own group memberships"
  ON public.group_chat_members FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Group creators can manage memberships"
  ON public.group_chat_members FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.group_chats 
      WHERE id = group_chat_id AND created_by = auth.uid()
    )
  );

CREATE POLICY "Users can leave groups"
  ON public.group_chat_members FOR DELETE
  USING (auth.uid() = user_id);
