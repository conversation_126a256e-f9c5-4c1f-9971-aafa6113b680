
-- Create messages table for direct and group messages
CREATE TABLE public.messages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  receiver_id UUID NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  group_chat_id UUID NULL,
  content TEXT NOT NULL,
  message_type TEXT NOT NULL DEFAULT 'direct' CHECK (message_type IN ('direct', 'group')),
  read BOOLEAN NOT NULL DEFAULT false,
  edited BOOLEAN NOT NULL DEFAULT false,
  edited_at TIMESTAMP WITH TIME ZONE NULL,
  attachments JSONB DEFAULT '[]'::jsonb,
  reply_to_message_id UUID NULL REFERENCES public.messages(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create group_chats table
CREATE TABLE public.group_chats (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NULL,
  avatar_url TEXT NULL,
  created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create group_chat_members table
CREATE TABLE public.group_chat_members (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  group_chat_id UUID NOT NULL REFERENCES public.group_chats(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'member')),
  joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(group_chat_id, user_id)
);

-- Create message_reactions table for emoji reactions
CREATE TABLE public.message_reactions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  emoji TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(message_id, user_id, emoji)
);

-- Create message_delivery_status table for read receipts
CREATE TABLE public.message_delivery_status (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read')),
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(message_id, user_id)
);

-- Add foreign key constraint for group_chat_id in messages
ALTER TABLE public.messages 
ADD CONSTRAINT fk_messages_group_chat 
FOREIGN KEY (group_chat_id) REFERENCES public.group_chats(id) ON DELETE CASCADE;

-- Enable Row Level Security
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_chat_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_delivery_status ENABLE ROW LEVEL SECURITY;

-- RLS Policies for messages
CREATE POLICY "Users can view messages they sent or received" ON public.messages
  FOR SELECT USING (
    auth.uid() = sender_id OR 
    auth.uid() = receiver_id OR 
    (group_chat_id IS NOT NULL AND EXISTS (
      SELECT 1 FROM public.group_chat_members 
      WHERE group_chat_id = messages.group_chat_id AND user_id = auth.uid()
    ))
  );

CREATE POLICY "Users can insert messages" ON public.messages
  FOR INSERT WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "Users can update their own messages" ON public.messages
  FOR UPDATE USING (auth.uid() = sender_id);

CREATE POLICY "Users can delete their own messages" ON public.messages
  FOR DELETE USING (auth.uid() = sender_id);

-- RLS Policies for group_chats
CREATE POLICY "Users can view group chats they are members of" ON public.group_chats
  FOR SELECT USING (EXISTS (
    SELECT 1 FROM public.group_chat_members 
    WHERE group_chat_id = id AND user_id = auth.uid()
  ));

CREATE POLICY "Users can create group chats" ON public.group_chats
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Group admins can update group chats" ON public.group_chats
  FOR UPDATE USING (EXISTS (
    SELECT 1 FROM public.group_chat_members 
    WHERE group_chat_id = id AND user_id = auth.uid() AND role = 'admin'
  ));

-- RLS Policies for group_chat_members
CREATE POLICY "Users can view group members of groups they belong to" ON public.group_chat_members
  FOR SELECT USING (EXISTS (
    SELECT 1 FROM public.group_chat_members gcm2 
    WHERE gcm2.group_chat_id = group_chat_members.group_chat_id AND gcm2.user_id = auth.uid()
  ));

CREATE POLICY "Group admins can manage members" ON public.group_chat_members
  FOR ALL USING (EXISTS (
    SELECT 1 FROM public.group_chat_members 
    WHERE group_chat_id = group_chat_members.group_chat_id AND user_id = auth.uid() AND role = 'admin'
  ));

CREATE POLICY "Users can leave groups" ON public.group_chat_members
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for message_reactions
CREATE POLICY "Users can view reactions on messages they can see" ON public.message_reactions
  FOR SELECT USING (EXISTS (
    SELECT 1 FROM public.messages 
    WHERE id = message_id AND (
      auth.uid() = sender_id OR 
      auth.uid() = receiver_id OR 
      (group_chat_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM public.group_chat_members 
        WHERE group_chat_id = messages.group_chat_id AND user_id = auth.uid()
      ))
    )
  ));

CREATE POLICY "Users can add reactions" ON public.message_reactions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can remove their own reactions" ON public.message_reactions
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for message_delivery_status
CREATE POLICY "Users can view delivery status for their messages" ON public.message_delivery_status
  FOR SELECT USING (EXISTS (
    SELECT 1 FROM public.messages 
    WHERE id = message_id AND auth.uid() = sender_id
  ));

CREATE POLICY "Users can update delivery status" ON public.message_delivery_status
  FOR ALL USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX idx_messages_sender_receiver ON public.messages(sender_id, receiver_id);
CREATE INDEX idx_messages_group_chat ON public.messages(group_chat_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at);
CREATE INDEX idx_group_chat_members_group_user ON public.group_chat_members(group_chat_id, user_id);
CREATE INDEX idx_message_reactions_message ON public.message_reactions(message_id);
CREATE INDEX idx_message_delivery_status_message ON public.message_delivery_status(message_id);

-- Enable realtime for all messaging tables
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;
ALTER PUBLICATION supabase_realtime ADD TABLE public.group_chats;
ALTER PUBLICATION supabase_realtime ADD TABLE public.group_chat_members;
ALTER PUBLICATION supabase_realtime ADD TABLE public.message_reactions;
ALTER PUBLICATION supabase_realtime ADD TABLE public.message_delivery_status;

-- Create function to automatically create delivery status entries
CREATE OR REPLACE FUNCTION create_message_delivery_status()
RETURNS TRIGGER AS $$
BEGIN
  -- For direct messages, create delivery status for receiver
  IF NEW.message_type = 'direct' AND NEW.receiver_id IS NOT NULL THEN
    INSERT INTO public.message_delivery_status (message_id, user_id, status)
    VALUES (NEW.id, NEW.receiver_id, 'delivered');
  END IF;
  
  -- For group messages, create delivery status for all group members except sender
  IF NEW.message_type = 'group' AND NEW.group_chat_id IS NOT NULL THEN
    INSERT INTO public.message_delivery_status (message_id, user_id, status)
    SELECT NEW.id, gcm.user_id, 'delivered'
    FROM public.group_chat_members gcm
    WHERE gcm.group_chat_id = NEW.group_chat_id 
    AND gcm.user_id != NEW.sender_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic delivery status creation
CREATE TRIGGER trigger_create_message_delivery_status
  AFTER INSERT ON public.messages
  FOR EACH ROW
  EXECUTE FUNCTION create_message_delivery_status();

-- Create function to update message timestamps
CREATE OR REPLACE FUNCTION update_message_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updating message timestamps
CREATE TRIGGER trigger_update_message_timestamp
  BEFORE UPDATE ON public.messages
  FOR EACH ROW
  EXECUTE FUNCTION update_message_timestamp();

CREATE TRIGGER trigger_update_group_chat_timestamp
  BEFORE UPDATE ON public.group_chats
  FOR EACH ROW
  EXECUTE FUNCTION update_message_timestamp();
