
-- First, let's check if there are any problematic policies on message_reactions table
-- and create proper ones that don't cause infinite recursion

-- Drop existing policies that might be causing issues
DROP POLICY IF EXISTS "Users can manage their own reactions" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can view message reactions" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can add reactions" ON public.message_reactions;
DROP POLICY IF EXISTS "Users can remove reactions" ON public.message_reactions;

-- Create a security definer function to safely check message access
CREATE OR REPLACE FUNCTION public.can_access_message(message_id_param uuid, user_id_param uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
DECLARE
  message_exists boolean;
BEGIN
  -- Check if user can access the message (either sender, receiver, or group member)
  SELECT EXISTS (
    SELECT 1 FROM public.messages m
    WHERE m.id = message_id_param
    AND (
      m.sender_id = user_id_param
      OR m.receiver_id = user_id_param
      OR (
        m.group_chat_id IS NOT NULL
        AND EXISTS (
          SELECT 1 FROM public.group_chat_members gcm
          WHERE gcm.group_chat_id = m.group_chat_id
          AND gcm.user_id = user_id_param
        )
      )
    )
  ) INTO message_exists;
  
  RETURN message_exists;
END;
$$;

-- Create new RLS policies using the security definer function
CREATE POLICY "Users can view reactions on accessible messages"
ON public.message_reactions
FOR SELECT
USING (public.can_access_message(message_id, auth.uid()));

CREATE POLICY "Users can add reactions to accessible messages"
ON public.message_reactions
FOR INSERT
WITH CHECK (
  auth.uid() = user_id
  AND public.can_access_message(message_id, auth.uid())
);

CREATE POLICY "Users can remove their own reactions"
ON public.message_reactions
FOR DELETE
USING (
  auth.uid() = user_id
  AND public.can_access_message(message_id, auth.uid())
);

-- Enable RLS on the table if not already enabled
ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;
