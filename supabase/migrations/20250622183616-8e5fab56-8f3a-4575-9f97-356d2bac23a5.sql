
-- Create admin helper functions that bypass <PERSON><PERSON> for admin operations
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE id = user_id
    AND badges IS NOT NULL
    AND 'admin' = ANY(badges)
  );
$$;

-- <PERSON><PERSON> function to safely update user badges (admin only)
CREATE OR REPLACE FUNCTION public.admin_update_user_badges(
  target_user_id uuid,
  new_badges text[],
  admin_user_id uuid
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the calling user is an admin
  IF NOT public.is_admin(admin_user_id) THEN
    RAISE EXCEPTION 'Access denied: User is not an admin';
  END IF;
  
  -- Update the user's badges
  UPDATE public.profiles
  SET badges = new_badges
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$;

-- <PERSON>reate function to safely update premium status (admin only)
CREATE OR REPLACE FUNCTION public.admin_update_premium_status(
  target_user_id uuid,
  is_premium boolean,
  admin_user_id uuid
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the calling user is an admin
  IF NOT public.is_admin(admin_user_id) THEN
    RAISE EXCEPTION 'Access denied: User is not an admin';
  END IF;
  
  -- Update the user's premium status
  UPDATE public.profiles
  SET is_premium = is_premium
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$;

-- Create function to safely update user profile (admin only)
CREATE OR REPLACE FUNCTION public.admin_update_user_profile(
  target_user_id uuid,
  new_username text,
  new_region text,
  new_bio text,
  admin_user_id uuid
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the calling user is an admin
  IF NOT public.is_admin(admin_user_id) THEN
    RAISE EXCEPTION 'Access denied: User is not an admin';
  END IF;
  
  -- Update the user's profile
  UPDATE public.profiles
  SET 
    username = new_username,
    region = new_region,
    bio = new_bio
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$;

-- Create function to safely update services (admin only)
CREATE OR REPLACE FUNCTION public.admin_update_user_services(
  target_user_id uuid,
  new_services jsonb,
  admin_user_id uuid
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the calling user is an admin
  IF NOT public.is_admin(admin_user_id) THEN
    RAISE EXCEPTION 'Access denied: User is not an admin';
  END IF;
  
  -- Update the user's services
  UPDATE public.profiles
  SET services = new_services
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.is_admin(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_update_user_badges(uuid, text[], uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_update_premium_status(uuid, boolean, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_update_user_profile(uuid, text, text, text, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_update_user_services(uuid, jsonb, uuid) TO authenticated;
