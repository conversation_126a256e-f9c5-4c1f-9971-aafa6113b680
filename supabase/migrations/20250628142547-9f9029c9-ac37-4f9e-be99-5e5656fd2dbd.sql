
-- Add views column to videos table
ALTER TABLE public.videos 
ADD COLUMN views integer DEFAULT 0 NOT NULL;

-- Create an index on views for better performance when sorting
CREATE INDEX idx_videos_views ON public.videos(views DESC);

-- Create a function to increment video views
CREATE OR REPLACE FUNCTION public.increment_video_views(video_id_param uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  UPDATE public.videos
  SET views = views + 1
  WHERE id = video_id_param;
END;
$function$;
