
-- Drop ALL existing policies on group_chat_members to start fresh
DROP POLICY IF EXISTS "Members can view group membership" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group owners can add members" ON public.group_chat_members;
DROP POLICY IF EXISTS "Users can leave groups" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group owners can remove members" ON public.group_chat_members;
DROP POLICY IF EXISTS "Users can view group chat members for groups they belong to" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group creators can add members" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group creators can remove members" ON public.group_chat_members;

-- Create the simplest possible policies that don't cause recursion
-- Allow users to see their own membership records
CREATE POLICY "Users can view their own group memberships"
  ON public.group_chat_members FOR SELECT
  USING (auth.uid() = user_id);

-- Allow group creators to manage memberships (using group_chats table directly)
CREATE POLICY "Group creators can manage memberships"
  ON public.group_chat_members FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.group_chats 
      WHERE id = group_chat_id AND created_by = auth.uid()
    )
  );

-- Allow users to leave groups (delete their own membership)
CREATE POLICY "Users can leave groups"
  ON public.group_chat_members FOR DELETE
  USING (auth.uid() = user_id);

-- Also simplify the messages policies to avoid any group chat references for now
DROP POLICY IF EXISTS "Users can view group messages" ON public.messages;
DROP POLICY IF EXISTS "Users can send group messages" ON public.messages;
DROP POLICY IF EXISTS "Users can view their direct messages" ON public.messages;
DROP POLICY IF EXISTS "Users can send direct messages" ON public.messages;

-- Keep only direct message policies active for now
CREATE POLICY "Users can view their direct messages"
  ON public.messages FOR SELECT
  USING (
    message_type = 'direct' AND 
    group_chat_id IS NULL AND
    (sender_id = auth.uid() OR receiver_id = auth.uid())
  );

CREATE POLICY "Users can send direct messages"
  ON public.messages FOR INSERT
  WITH CHECK (
    message_type = 'direct' AND 
    group_chat_id IS NULL AND
    sender_id = auth.uid()
  );
