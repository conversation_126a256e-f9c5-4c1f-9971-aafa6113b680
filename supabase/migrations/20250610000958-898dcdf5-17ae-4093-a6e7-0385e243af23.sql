
-- Fix infinite recursion in group_chat_members policies
-- Drop ALL existing problematic policies that are causing infinite recursion
DROP POLICY IF EXISTS "Users can view group chat members for groups they belong to" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group creators can add members" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group creators can remove members" ON public.group_chat_members;
DROP POLICY IF EXISTS "Users can leave groups" ON public.group_chat_members;
DROP POLICY IF EXISTS "Members can view group membership" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group owners can add members" ON public.group_chat_members;
DROP POLICY IF EXISTS "Group owners can remove members" ON public.group_chat_members;

-- Drop the existing security definer functions if they exist
DROP FUNCTION IF EXISTS public.is_group_member(uuid, uuid);
DROP FUNCTION IF EXISTS public.is_group_creator(uuid, uuid);
DROP FUNCTION IF EXISTS public.check_group_membership(uuid, uuid);
DROP FUNCTION IF EXISTS public.check_group_ownership(uuid, uuid);

-- Create new security definer functions that don't cause recursion
CREATE OR REPLACE FUNCTION public.check_group_membership(group_id uuid, user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM group_chat_members 
    WHERE group_chat_id = group_id AND group_chat_members.user_id = user_id
  );
$$;

-- Create function to check group ownership
CREATE OR REPLACE FUNCTION public.check_group_ownership(group_id uuid, user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM group_chats 
    WHERE id = group_id AND created_by = user_id
  );
$$;

-- Recreate the group_chat_members policies using the new functions
CREATE POLICY "Members can view group membership"
  ON public.group_chat_members FOR SELECT
  USING (
    user_id = auth.uid() OR 
    public.check_group_membership(group_chat_id, auth.uid()) OR
    public.check_group_ownership(group_chat_id, auth.uid())
  );

CREATE POLICY "Group owners can add members"
  ON public.group_chat_members FOR INSERT
  WITH CHECK (public.check_group_ownership(group_chat_id, auth.uid()));

CREATE POLICY "Users can leave groups"
  ON public.group_chat_members FOR DELETE
  USING (auth.uid() = user_id);

CREATE POLICY "Group owners can remove members"
  ON public.group_chat_members FOR DELETE
  USING (public.check_group_ownership(group_chat_id, auth.uid()));

-- Also fix the messages policy that might be causing issues
DROP POLICY IF EXISTS "Users can view their direct messages" ON public.messages;

-- Create a simpler direct messages policy that doesn't reference group_chat_members
CREATE POLICY "Users can view their direct messages"
  ON public.messages FOR SELECT
  USING (
    message_type = 'direct' AND 
    group_chat_id IS NULL AND
    (sender_id = auth.uid() OR receiver_id = auth.uid())
  );
