
-- Fix infinite recursion in RLS policies by removing problematic group_chat_members references

-- Drop the problematic policies on message_reactions
DROP POLICY IF EXISTS "Users can view reactions on messages they can see" ON public.message_reactions;

-- Create simpler policies for message_reactions that don't reference group_chat_members
CREATE POLICY "Users can view reactions on direct messages they participate in"
  ON public.message_reactions FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.messages 
      WHERE id = message_id 
      AND message_type = 'direct'
      AND group_chat_id IS NULL
      AND (sender_id = auth.uid() OR receiver_id = auth.uid())
    )
  );

CREATE POLICY "Users can add reactions to direct messages they participate in"
  ON public.message_reactions FOR INSERT
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM public.messages 
      WHERE id = message_id 
      AND message_type = 'direct'
      AND group_chat_id IS NULL
      AND (sender_id = auth.uid() OR receiver_id = auth.uid())
    )
  );

CREATE POLICY "Users can remove their own reactions"
  ON public.message_reactions FOR DELETE
  USING (auth.uid() = user_id);

-- Drop the problematic policies on message_delivery_status
DROP POLICY IF EXISTS "Users can view delivery status for their messages" ON public.message_delivery_status;
DROP POLICY IF EXISTS "Users can update delivery status" ON public.message_delivery_status;

-- Create simpler policies for message_delivery_status that don't reference group_chat_members
CREATE POLICY "Users can view delivery status for direct messages they sent"
  ON public.message_delivery_status FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.messages 
      WHERE id = message_id 
      AND message_type = 'direct'
      AND group_chat_id IS NULL
      AND sender_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own delivery status for direct messages"
  ON public.message_delivery_status FOR ALL
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM public.messages 
      WHERE id = message_id 
      AND message_type = 'direct'
      AND group_chat_id IS NULL
      AND (sender_id = auth.uid() OR receiver_id = auth.uid())
    )
  );

-- Also ensure the messages policy is clean and doesn't cause recursion
DROP POLICY IF EXISTS "Users can view group messages" ON public.messages;
DROP POLICY IF EXISTS "Users can send group messages" ON public.messages;

-- For now, only allow direct messages to avoid group chat complexity
-- Group chat policies can be added later with proper security definer functions
