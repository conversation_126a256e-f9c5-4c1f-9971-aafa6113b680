
-- First, let's fix the infinite recursion in group chat policies
-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view group chats they are members of" ON public.group_chats;
DROP POLICY IF EXISTS "Users can create group chats" ON public.group_chats;
DROP POLICY IF EXISTS "Users can view group chat members" ON public.group_chat_members;
DROP POLICY IF EXISTS "Users can add members to their group chats" ON public.group_chat_members;

-- Create a security definer function to check group membership
CREATE OR REPLACE FUNCTION public.is_group_member(group_id uuid, user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.group_chat_members 
    WHERE group_chat_id = group_id AND group_chat_members.user_id = user_id
  );
$$;

-- Create a security definer function to check if user created the group
CREATE OR REPLACE FUNCTION public.is_group_creator(group_id uuid, user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.group_chats 
    WHERE id = group_id AND created_by = user_id
  );
$$;

-- Recreate group_chats policies using the security definer functions
CREATE POLICY "Users can view group chats they are members of"
  ON public.group_chats FOR SELECT
  USING (public.is_group_member(id, auth.uid()));

CREATE POLICY "Users can create group chats"
  ON public.group_chats FOR INSERT
  WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own group chats"
  ON public.group_chats FOR UPDATE
  USING (auth.uid() = created_by);

-- Recreate group_chat_members policies
CREATE POLICY "Users can view group chat members for groups they belong to"
  ON public.group_chat_members FOR SELECT
  USING (public.is_group_member(group_chat_id, auth.uid()));

CREATE POLICY "Group creators can add members"
  ON public.group_chat_members FOR INSERT
  WITH CHECK (public.is_group_creator(group_chat_id, auth.uid()));

CREATE POLICY "Users can remove themselves from groups"
  ON public.group_chat_members FOR DELETE
  USING (auth.uid() = user_id);

CREATE POLICY "Group creators can remove members"
  ON public.group_chat_members FOR DELETE
  USING (public.is_group_creator(group_chat_id, auth.uid()));

-- Enable RLS on messages table if not already enabled
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Drop existing message policies if they exist
DROP POLICY IF EXISTS "Users can view their direct messages" ON public.messages;
DROP POLICY IF EXISTS "Users can send direct messages" ON public.messages;
DROP POLICY IF EXISTS "Users can view group messages" ON public.messages;
DROP POLICY IF EXISTS "Users can send group messages" ON public.messages;

-- Create policies for direct messages
CREATE POLICY "Users can view their direct messages"
  ON public.messages FOR SELECT
  USING (
    message_type = 'direct' AND 
    group_chat_id IS NULL AND
    (sender_id = auth.uid() OR receiver_id = auth.uid())
  );

CREATE POLICY "Users can send direct messages"
  ON public.messages FOR INSERT
  WITH CHECK (
    message_type = 'direct' AND 
    group_chat_id IS NULL AND
    sender_id = auth.uid()
  );

-- Create policies for group messages
CREATE POLICY "Users can view group messages"
  ON public.messages FOR SELECT
  USING (
    message_type = 'group' AND 
    group_chat_id IS NOT NULL AND
    public.is_group_member(group_chat_id, auth.uid())
  );

CREATE POLICY "Users can send group messages"
  ON public.messages FOR INSERT
  WITH CHECK (
    message_type = 'group' AND 
    group_chat_id IS NOT NULL AND
    sender_id = auth.uid() AND
    public.is_group_member(group_chat_id, auth.uid())
  );

-- Ensure we have a message-attachments storage bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('message-attachments', 'message-attachments', true)
ON CONFLICT (id) DO NOTHING;

-- Drop existing storage policies if they exist
DROP POLICY IF EXISTS "Users can upload message attachments" ON storage.objects;
DROP POLICY IF EXISTS "Users can view message attachments" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own message attachments" ON storage.objects;

-- Create storage policies for message attachments
CREATE POLICY "Users can upload message attachments"
  ON storage.objects FOR INSERT
  WITH CHECK (bucket_id = 'message-attachments' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view message attachments"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'message-attachments');

CREATE POLICY "Users can delete their own message attachments"
  ON storage.objects FOR DELETE
  USING (bucket_id = 'message-attachments' AND auth.uid()::text = (storage.foldername(name))[1]);
