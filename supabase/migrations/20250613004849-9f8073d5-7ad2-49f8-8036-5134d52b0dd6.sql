
-- Create avatars bucket for profile pictures (only if it doesn't exist)
INSERT INTO storage.buckets (id, name, public)
SELECT 'avatars', 'avatars', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'avatars');

-- Create attachments bucket for message attachments (only if it doesn't exist)
INSERT INTO storage.buckets (id, name, public)
SELECT 'attachments', 'attachments', false
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'attachments');

-- Create RLS policies for videos bucket (in case they don't exist)
DO $$ 
BEGIN
    -- Videos bucket policies
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can upload videos') THEN
        CREATE POLICY "Users can upload videos" ON storage.objects
        FOR INSERT WITH CHECK (
          bucket_id = 'videos' AND
          auth.role() = 'authenticated'
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Videos are publicly viewable') THEN
        CREATE POLICY "Videos are publicly viewable" ON storage.objects
        FOR SELECT USING (bucket_id = 'videos');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can update own videos') THEN
        CREATE POLICY "Users can update own videos" ON storage.objects
        FOR UPDATE USING (
          bucket_id = 'videos' AND
          auth.uid()::text = (storage.foldername(name))[1]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can delete own videos') THEN
        CREATE POLICY "Users can delete own videos" ON storage.objects
        FOR DELETE USING (
          bucket_id = 'videos' AND
          auth.uid()::text = (storage.foldername(name))[1]
        );
    END IF;

    -- Avatars bucket policies
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can upload avatars') THEN
        CREATE POLICY "Users can upload avatars" ON storage.objects
        FOR INSERT WITH CHECK (
          bucket_id = 'avatars' AND
          auth.role() = 'authenticated'
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Avatars are publicly viewable') THEN
        CREATE POLICY "Avatars are publicly viewable" ON storage.objects
        FOR SELECT USING (bucket_id = 'avatars');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can update own avatars') THEN
        CREATE POLICY "Users can update own avatars" ON storage.objects
        FOR UPDATE USING (
          bucket_id = 'avatars' AND
          auth.uid()::text = (storage.foldername(name))[1]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can delete own avatars') THEN
        CREATE POLICY "Users can delete own avatars" ON storage.objects
        FOR DELETE USING (
          bucket_id = 'avatars' AND
          auth.uid()::text = (storage.foldername(name))[1]
        );
    END IF;

    -- Attachments bucket policies
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can upload attachments') THEN
        CREATE POLICY "Users can upload attachments" ON storage.objects
        FOR INSERT WITH CHECK (
          bucket_id = 'attachments' AND
          auth.role() = 'authenticated'
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can view own attachments') THEN
        CREATE POLICY "Users can view own attachments" ON storage.objects
        FOR SELECT USING (
          bucket_id = 'attachments' AND
          auth.uid()::text = (storage.foldername(name))[1]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can update own attachments') THEN
        CREATE POLICY "Users can update own attachments" ON storage.objects
        FOR UPDATE USING (
          bucket_id = 'attachments' AND
          auth.uid()::text = (storage.foldername(name))[1]
        );
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can delete own attachments') THEN
        CREATE POLICY "Users can delete own attachments" ON storage.objects
        FOR DELETE USING (
          bucket_id = 'attachments' AND
          auth.uid()::text = (storage.foldername(name))[1]
        );
    END IF;
END $$;
