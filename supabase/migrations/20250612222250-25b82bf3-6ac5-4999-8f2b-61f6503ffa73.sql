
-- Drop the problematic policy and recreate it with proper auth check
DROP POLICY IF EXISTS "Users can create group chats" ON public.group_chats;

-- Create a simpler, more direct policy for group chat creation
CREATE POLICY "Users can create group chats"
  ON public.group_chats FOR INSERT
  WITH CHECK (auth.uid() IS NOT NULL AND created_by = auth.uid());

-- Also ensure the group_chat_members policy allows the creator to add themselves
DROP POLICY IF EXISTS "Group creators can manage memberships" ON public.group_chat_members;

CREATE POLICY "Group creators can manage memberships"
  ON public.group_chat_members FOR INSERT
  WITH CHECK (
    auth.uid() IS NOT NULL AND
    (
      -- Allow adding yourself as admin/member
      user_id = auth.uid() OR
      -- Allow group creators to add others
      EXISTS (
        SELECT 1 FROM public.group_chats 
        WHERE id = group_chat_id AND created_by = auth.uid()
      )
    )
  );
