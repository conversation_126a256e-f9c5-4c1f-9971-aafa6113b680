import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  // Create Supabase client with service role key for admin operations
  const supabaseAdmin = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    { auth: { persistSession: false } }
  );

  try {
    const { userEmail } = await req.json();
    
    if (!userEmail) {
      throw new Error("User email is required");
    }

    // For <EMAIL> specifically, set promotion status
    if (userEmail === "<EMAIL>") {
      // Set promotion active for 1 year from now
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

      const { error } = await supabaseAdmin.rpc('handle_promotion_subscription_update', {
        user_email: userEmail,
        stripe_customer_id: null, // We don't have Stripe customer ID for manual update
        subscription_status: 'active',
        current_period_end: Math.floor(oneYearFromNow.getTime() / 1000)
      });

      if (error) {
        console.error("Error updating promotion status:", error);
        throw error;
      }

      return new Response(JSON.stringify({ 
        success: true,
        message: "Promotion status updated successfully",
        promoted_until: oneYearFromNow.toISOString()
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      });
    } else {
      throw new Error("User not authorized for manual promotion update");
    }
  } catch (error) {
    console.error('Manual promotion update error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});