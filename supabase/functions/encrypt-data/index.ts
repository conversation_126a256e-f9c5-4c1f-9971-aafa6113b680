
// Endpoint to encrypt and decrypt sensitive data
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { encrypt, decrypt, initEncryption } from "../_shared/encryption.ts";
import { rateLimitMiddleware } from "../_shared/rate-limiter.ts";

serve(async (req: Request) => {
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  // Apply rate limiting - stricter for encryption/decryption operations
  const rateLimitResult = rateLimitMiddleware(req, {
    limit: 20, // Limit to 20 requests per minute
    windowSizeInSeconds: 60
  });

  if (rateLimitResult) {
    return rateLimitResult;
  }

  try {
    // Initialize encryption
    if (!(await initEncryption())) {
      return new Response(
        JSON.stringify({ error: "Encryption setup failed" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Parse the request body
    const { action, data } = await req.json();

    if (!action || !data) {
      return new Response(
        JSON.stringify({ error: "Missing required parameters" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    let result;
    
    // Process the request based on the action
    if (action === "encrypt") {
      result = await encrypt(data);
    } else if (action === "decrypt") {
      result = await decrypt(data);
    } else {
      return new Response(
        JSON.stringify({ error: "Invalid action. Use 'encrypt' or 'decrypt'" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Return the result
    return new Response(
      JSON.stringify({ result }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error("Error processing encryption request:", error);
    
    return new Response(
      JSON.stringify({ error: error.message || "Encryption operation failed" }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});
