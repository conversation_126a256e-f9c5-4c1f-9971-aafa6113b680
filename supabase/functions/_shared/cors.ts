
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Max-Age': '86400', // 24 hours cache for preflight requests
};

// Re-export rate limiter functions for easier imports
export { 
  applyRateLimit,
  rateLimitMiddleware,
  addRateLimitHeaders,
  checkRateLimit
} from './rate-limiter.ts';  // Added .ts extension
