
/**
 * Encryption utilities for Supabase Edge Functions
 * Used to encrypt/decrypt sensitive data before storing in the database
 */

const ENCRYPTION_KEY_LENGTH = 32; // 256 bits
let encryptionKey: CryptoKey | null = null;

/**
 * Initialize encryption key from environment variable
 * Should be called at the beginning of any function that requires encryption
 */
export async function initEncryption(): Promise<boolean> {
  if (encryptionKey) return true;

  try {
    const keyBase64 = Deno.env.get("ENCRYPTION_KEY");
    if (!keyBase64) {
      console.error("Missing ENCRYPTION_KEY environment variable");
      return false;
    }

    // Convert base64 key to ArrayBuffer
    const keyData = base64ToArrayBuffer(keyBase64);
    
    // Import the key
    encryptionKey = await crypto.subtle.importKey(
      "raw",
      keyData,
      { name: "AES-GCM" },
      false,
      ["encrypt", "decrypt"]
    );
    
    return true;
  } catch (error) {
    console.error("Failed to initialize encryption:", error);
    return false;
  }
}

/**
 * Convert base64 string to ArrayBuffer
 */
function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
}

/**
 * Convert ArrayBuffer to base64 string
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

/**
 * Encrypt a string value
 * Returns a base64 string with format: iv:ciphertext
 */
export async function encrypt(plaintext: string): Promise<string> {
  if (!encryptionKey) {
    if (!(await initEncryption())) {
      throw new Error("Encryption not initialized");
    }
  }

  try {
    // Generate random IV (Initialization Vector)
    const iv = crypto.getRandomValues(new Uint8Array(12));
    
    // Encrypt the data
    const data = new TextEncoder().encode(plaintext);
    const ciphertext = await crypto.subtle.encrypt(
      {
        name: "AES-GCM",
        iv
      },
      encryptionKey!,
      data
    );

    // Combine IV and ciphertext and convert to base64
    const combined = new Uint8Array(iv.length + ciphertext.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(ciphertext), iv.length);
    
    return arrayBufferToBase64(combined.buffer);
  } catch (error) {
    console.error("Encryption failed:", error);
    throw new Error("Encryption failed");
  }
}

/**
 * Decrypt an encrypted string value
 * Input should be base64 string with format: iv:ciphertext
 */
export async function decrypt(encryptedData: string): Promise<string> {
  if (!encryptionKey) {
    if (!(await initEncryption())) {
      throw new Error("Encryption not initialized");
    }
  }
  
  try {
    // Decode the base64 combined data
    const combined = base64ToArrayBuffer(encryptedData);
    
    // Extract IV and ciphertext
    const iv = new Uint8Array(combined, 0, 12);
    const ciphertext = new Uint8Array(combined, 12);
    
    // Decrypt the data
    const decrypted = await crypto.subtle.decrypt(
      {
        name: "AES-GCM",
        iv
      },
      encryptionKey!,
      ciphertext
    );
    
    // Convert the decrypted data back to a string
    return new TextDecoder().decode(decrypted);
  } catch (error) {
    console.error("Decryption failed:", error);
    throw new Error("Decryption failed");
  }
}

/**
 * Generate a secure encryption key
 * Returns a base64 string that can be used as ENCRYPTION_KEY
 */
export async function generateEncryptionKey(): Promise<string> {
  // Generate a random 256-bit key
  const key = crypto.getRandomValues(new Uint8Array(ENCRYPTION_KEY_LENGTH));
  return arrayBufferToBase64(key.buffer);
}
