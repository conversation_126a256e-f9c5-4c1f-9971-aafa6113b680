
// Simple rate limiter implementation for Supabase Edge Functions
export interface RateLimitOptions {
  // Maximum number of requests allowed in the time window
  limit: number;
  // Time window in seconds
  windowSizeInSeconds: number;
  // Identifier for the rate limit (usually IP or user ID)
  identifier: string;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetAt: Date;
  limit: number;
}

// In-memory storage for rate limits
// Note: This will reset when the function instance is recycled
// For production, use a persistent store like Redis or the database
const rateLimitStore = new Map<string, { count: number, resetAt: number }>();

/**
 * Check if a request is allowed based on rate limiting rules
 */
export function checkRateLimit(options: RateLimitOptions): RateLimitResult {
  const { limit, windowSizeInSeconds, identifier } = options;
  const now = Date.now();
  const windowKey = `${identifier}:${Math.floor(now / (windowSizeInSeconds * 1000))}`;
  
  // Get or create entry for this time window
  let entry = rateLimitStore.get(windowKey);
  if (!entry) {
    entry = {
      count: 0,
      resetAt: now + (windowSizeInSeconds * 1000)
    };
    rateLimitStore.set(windowKey, entry);
    
    // Cleanup old entries (simple garbage collection)
    if (rateLimitStore.size > 10000) {
      const keysToDelete = [];
      for (const [key, value] of rateLimitStore.entries()) {
        if (value.resetAt < now) {
          keysToDelete.push(key);
        }
      }
      keysToDelete.forEach(key => rateLimitStore.delete(key));
    }
  }
  
  // Increment count
  entry.count += 1;
  
  // Check if limit exceeded
  const allowed = entry.count <= limit;
  const remaining = Math.max(0, limit - entry.count);
  
  return {
    allowed,
    remaining,
    resetAt: new Date(entry.resetAt),
    limit
  };
}

/**
 * Add rate limit headers to a Response
 */
export function addRateLimitHeaders(
  response: Response,
  result: RateLimitResult
): Response {
  const headers = new Headers(response.headers);
  headers.set('X-RateLimit-Limit', result.limit.toString());
  headers.set('X-RateLimit-Remaining', result.remaining.toString());
  headers.set('X-RateLimit-Reset', Math.floor(result.resetAt.getTime() / 1000).toString());
  
  if (!result.allowed) {
    headers.set('Retry-After', Math.ceil((result.resetAt.getTime() - Date.now()) / 1000).toString());
  }
  
  return new Response(response.body, {
    status: result.allowed ? response.status : 429,
    statusText: result.allowed ? response.statusText : 'Too Many Requests',
    headers
  });
}

/**
 * Apply rate limiting middleware to a request
 */
export function applyRateLimit(
  req: Request,
  options?: Partial<RateLimitOptions>
): RateLimitResult {
  // Extract client IP from request
  const clientIp = req.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown';
  
  // Default options
  const defaultOptions: RateLimitOptions = {
    limit: 60,
    windowSizeInSeconds: 60,
    identifier: clientIp
  };
  
  // Merge provided options with defaults
  const rateOptions = { ...defaultOptions, ...options };
  
  // Check rate limit
  return checkRateLimit(rateOptions);
}

/**
 * Middleware for rate limiting requests
 */
export function rateLimitMiddleware(
  req: Request,
  options?: Partial<RateLimitOptions>
): Response | null {
  const result = applyRateLimit(req, options);
  
  if (!result.allowed) {
    return new Response(
      JSON.stringify({
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: Math.ceil((result.resetAt.getTime() - Date.now()) / 1000)
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': Math.ceil((result.resetAt.getTime() - Date.now()) / 1000).toString(),
          'X-RateLimit-Limit': result.limit.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': Math.floor(result.resetAt.getTime() / 1000).toString()
        }
      }
    );
  }
  
  return null;
}

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};
