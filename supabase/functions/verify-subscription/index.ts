import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  // Create Supabase clients
  const supabaseClient = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_ANON_KEY") ?? ""
  );

  const supabaseAdmin = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    { auth: { persistSession: false } }
  );

  try {
    const authHeader = req.headers.get("Authorization")!;
    const token = authHeader.replace("Bearer ", "");
    const { data } = await supabaseClient.auth.getUser(token);
    const user = data.user;
    
    if (!user?.email) {
      throw new Error("User not authenticated or email not available");
    }

    const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
      apiVersion: "2023-10-16",
    });

    // Find the customer by email
    const customers = await stripe.customers.list({ 
      email: user.email, 
      limit: 1 
    });

    let isPromoted = false;
    let promotedUntil = null;
    let stripeCustomerId = null;

    if (customers.data.length > 0) {
      const customer = customers.data[0];
      stripeCustomerId = customer.id;

      // Get active subscriptions for this customer
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: 'active',
        limit: 10
      });

      // Check if any subscription is for service promotion
      for (const subscription of subscriptions.data) {
        // You may need to adjust this check based on your price IDs
        // For now, we'll assume any active subscription means promotion is active
        if (subscription.status === 'active') {
          isPromoted = true;
          promotedUntil = new Date(subscription.current_period_end * 1000).toISOString();
          break;
        }
      }
    }

    // Update the user's profile with the current subscription status
    const { error } = await supabaseAdmin.rpc('handle_promotion_subscription_update', {
      user_email: user.email,
      stripe_customer_id: stripeCustomerId,
      subscription_status: isPromoted ? 'active' : 'inactive',
      current_period_end: isPromoted && promotedUntil ? Math.floor(new Date(promotedUntil).getTime() / 1000) : 0
    });

    if (error) {
      console.error("Error updating subscription status:", error);
      throw error;
    }

    return new Response(JSON.stringify({ 
      is_promoted: isPromoted,
      promoted_until: promotedUntil,
      message: "Subscription status verified and updated"
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error('Subscription verification error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});