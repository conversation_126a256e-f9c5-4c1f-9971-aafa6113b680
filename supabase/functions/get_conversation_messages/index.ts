
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req: Request) => {
  // Handle CORS
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log('📨 Starting get_conversation_messages function');
    
    // Get the authorization header from the request
    const authHeader = req.headers.get("authorization");
    console.log('🔑 Auth header present:', !!authHeader);
    
    if (!authHeader) {
      console.error('❌ Missing authorization header');
      return new Response(
        JSON.stringify({ error: "Missing authorization header" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }

    // Extract the JWT token from the Bearer header
    const token = authHeader.replace('Bearer ', '');
    console.log('🎫 Token extracted, length:', token.length);

    // Create supabase client with service role key for admin operations
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    console.log('🏗️ Supabase clients created');

    // Get the current user from the auth token using the admin client
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);
    
    console.log('👤 User fetch result:', !!user, userError ? 'Error: ' + userError.message : 'Success');
    
    if (userError || !user) {
      console.error('❌ Invalid authorization token:', userError?.message);
      return new Response(
        JSON.stringify({ error: "Invalid authorization token", details: userError?.message }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }

    // Extract user IDs from request
    const { user1_id, user2_id } = await req.json();

    if (!user1_id || !user2_id) {
      console.error('❌ Missing required parameters');
      return new Response(
        JSON.stringify({
          error: "Missing required parameters user1_id or user2_id",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Verify that the current user is one of the participants
    if (user.id !== user1_id && user.id !== user2_id) {
      console.error('❌ Unauthorized access to conversation');
      return new Response(
        JSON.stringify({ error: "Unauthorized access to conversation" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 403,
        }
      );
    }

    console.log('📨 Fetching messages between:', user1_id, 'and', user2_id);

    // Use admin client to query messages bypassing RLS
    const { data, error } = await supabaseAdmin
      .from("messages")
      .select("*")
      .eq("message_type", "direct")
      .is("group_chat_id", null)
      .or(
        `and(sender_id.eq.${user1_id},receiver_id.eq.${user2_id}),and(sender_id.eq.${user2_id},receiver_id.eq.${user1_id})`
      )
      .order("created_at", { ascending: true });

    if (error) {
      console.error('❌ Error fetching messages:', error);
      return new Response(
        JSON.stringify({ error: "Error fetching messages", details: error.message }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    console.log('✅ Fetched', data?.length || 0, 'messages');

    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error('❌ Error in get_conversation_messages:', error);
    return new Response(JSON.stringify({ 
      error: "Internal server error", 
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
