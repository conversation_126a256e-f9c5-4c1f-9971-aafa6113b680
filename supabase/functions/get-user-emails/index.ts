
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4'

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE"
}

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }
  
  try {
    // Create a Supabase client with the Admin key
    const supabaseAdmin = createClient(
      // @ts-ignore
      Deno.env.get('SUPABASE_URL') ?? '',
      // @ts-ignore
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { auth: { persistSession: false } }
    )
    
    // Verify the user is authenticated and has admin privileges
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('Missing Authorization header')
      return new Response(JSON.stringify({ error: 'Missing Authorization header' }), { 
        status: 401, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)
    
    if (authError || !user) {
      console.error('Authentication failed:', authError)
      return new Response(JSON.stringify({ error: 'Unauthorized - Invalid token' }), { 
        status: 401, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    console.log('Authenticated user:', user.email)
    
    // Check if the user is an admin by looking at their profile
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('badges')
      .eq('id', user.id)
      .single()
    
    console.log('User profile:', profile)
    console.log('Profile error:', profileError)
    
    if (profileError) {
      console.error('Error fetching user profile:', profileError)
      return new Response(JSON.stringify({ error: 'Error fetching user profile' }), { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    if (!profile || !profile.badges || !Array.isArray(profile.badges) || !profile.badges.includes('admin')) {
      console.error('User is not an admin:', profile)
      return new Response(JSON.stringify({ error: 'Unauthorized - Admin privileges required' }), { 
        status: 403, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    console.log('Admin access confirmed for user:', user.email)
    
    // Fetch users from auth.users
    const { data: users, error: usersError } = await supabaseAdmin.auth.admin.listUsers()
    
    if (usersError) {
      console.error('Error retrieving users:', usersError)
      return new Response(JSON.stringify({ error: 'Failed to retrieve users' }), { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    // Extract only the necessary information
    const userEmails = users.users.map(user => ({
      id: user.id,
      email: user.email,
      created_at: user.created_at
    }))
    
    console.log(`Successfully retrieved ${userEmails.length} users`)
    
    return new Response(JSON.stringify(userEmails), { 
      status: 200, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(JSON.stringify({ error: error.message }), { 
      status: 500, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})
