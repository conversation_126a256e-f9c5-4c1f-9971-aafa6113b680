import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  console.log('Received request:', req.method, req.url);
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling CORS preflight');
    return new Response(null, { headers: corsHeaders });
  }

  const { headers } = req;
  const upgradeHeader = headers.get("upgrade") || "";
  console.log('Upgrade header:', upgradeHeader);

  if (upgradeHeader.toLowerCase() !== "websocket") {
    console.log('Not a WebSocket request');
    return new Response("Expected WebSocket connection", { 
      status: 426,
      headers: {
        ...corsHeaders,
        'Upgrade': 'websocket'
      }
    });
  }

  console.log('Upgrading to WebSocket...');
  
  try {
    const { socket, response } = Deno.upgradeWebSocket(req);
    
    let openAISocket: WebSocket | null = null;
    let isClosing = false;
    let connectionAttempts = 0;
    const maxConnectionAttempts = 3;
    
    socket.onopen = () => {
      console.log("Client connected to enhanced hunting AI chat");
      
      // Get OpenAI API key
      const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
      console.log('OpenAI API key present:', !!openAIApiKey);
      
      if (!openAIApiKey) {
        console.error('OpenAI API key not found');
        socket.send(JSON.stringify({ 
          type: 'error', 
          message: 'OpenAI API key not configured. Please check your environment variables.' 
        }));
        socket.close(1011, 'Server configuration error');
        return;
      }

      // Function to attempt OpenAI connection with enhanced system prompt
      const connectToOpenAI = () => {
        connectionAttempts++;
        console.log(`Attempting OpenAI connection #${connectionAttempts}`);
        
        const openAIUrl = `wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-12-17`;
        console.log('Connecting to OpenAI:', openAIUrl);
        
        try {
          openAISocket = new WebSocket(openAIUrl, [], {
            headers: {
              'Authorization': `Bearer ${openAIApiKey}`,
              'OpenAI-Beta': 'realtime=v1'
            }
          });

          openAISocket.onopen = () => {
            console.log("Connected to OpenAI Realtime API successfully");
            connectionAttempts = 0; // Reset on successful connection
            
            // Send enhanced session configuration for hunting expertise
            const currentDate = new Date().toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              weekday: 'long'
            });

            const sessionConfig = {
              type: 'session.update',
              session: {
                modalities: ['text', 'audio'],
                instructions: `You are the ultimate whitetail deer hunting AI assistant for Whitetail Livn, with cutting-edge expertise for the 2024-2025 hunting season.

TODAY: ${currentDate}

You are the most knowledgeable hunting AI ever created, specializing in:
- Real-time whitetail behavior analysis and predictions
- Advanced rut timing and phase identification
- Weather-based hunting strategy optimization  
- Equipment recommendations for 2024-2025 season
- Shot placement and blood trail analysis
- Current hunting regulations and season dates
- Advanced scouting and patterning techniques
- Food plot and habitat management
- Public land hunting strategies
- Mature buck hunting tactics

Provide detailed, actionable advice that helps hunters succeed while maintaining ethical practices. Always consider current weather, season timing, and regional factors in your recommendations.

Be conversational but authoritative - you're the expert hunters turn to for game-changing advice.`,
                voice: 'sage',
                input_audio_format: 'pcm16',
                output_audio_format: 'pcm16',
                input_audio_transcription: {
                  model: 'whisper-1'
                },
                turn_detection: {
                  type: 'server_vad',
                  threshold: 0.5,
                  prefix_padding_ms: 300,
                  silence_duration_ms: 1200
                },
                tools: [
                  {
                    type: 'function',
                    name: 'get_current_hunting_conditions',
                    description: 'Get current hunting conditions including weather, rut phase, and recommendations',
                    parameters: {
                      type: 'object',
                      properties: {
                        location: { 
                          type: 'string',
                          description: 'Geographic location for hunting conditions'
                        }
                      },
                      required: ['location']
                    }
                  },
                  {
                    type: 'function',
                    name: 'analyze_blood_trail',
                    description: 'Analyze blood trail characteristics for shot assessment',
                    parameters: {
                      type: 'object',
                      properties: {
                        blood_color: { 
                          type: 'string',
                          description: 'Color and consistency of blood found'
                        },
                        location_hit: {
                          type: 'string', 
                          description: 'Suspected hit location on deer'
                        }
                      },
                      required: ['blood_color']
                    }
                  }
                ],
                tool_choice: 'auto',
                temperature: 0.8,
                max_response_output_tokens: 4000
              }
            };

            if (openAISocket && openAISocket.readyState === WebSocket.OPEN) {
              openAISocket.send(JSON.stringify(sessionConfig));
            }
            
            // Send session.created event to client
            if (socket.readyState === WebSocket.OPEN) {
              socket.send(JSON.stringify({ type: 'session.created' }));
            }
          };

          openAISocket.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);
              console.log("OpenAI message type:", data.type);
              
              // Handle function calls for hunting-specific tools
              if (data.type === 'response.function_call_arguments.done') {
                handleHuntingFunctionCall(data, openAISocket);
                return;
              }
              
              // Forward all other messages to client
              if (socket.readyState === WebSocket.OPEN) {
                socket.send(event.data);
              }
            } catch (error) {
              console.error("Error processing OpenAI message:", error);
            }
          };

          // ... keep existing code (error handlers and close handlers)
          openAISocket.onerror = (error) => {
            console.error("OpenAI WebSocket error:", error);
            
            // Attempt reconnection if we haven't exceeded max attempts
            if (connectionAttempts < maxConnectionAttempts && !isClosing) {
              console.log(`Retrying OpenAI connection in 2 seconds (attempt ${connectionAttempts + 1}/${maxConnectionAttempts})`);
              setTimeout(() => {
                if (!isClosing && socket.readyState === WebSocket.OPEN) {
                  connectToOpenAI();
                }
              }, 2000);
            } else {
              if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({ 
                  type: 'error', 
                  message: 'Failed to connect to AI service after multiple attempts. Please try again.' 
                }));
              }
            }
          };

          openAISocket.onclose = (event) => {
            console.log("OpenAI connection closed:", event.code, event.reason);
            
            // Only attempt reconnection for unexpected closures
            if (event.code !== 1000 && connectionAttempts < maxConnectionAttempts && !isClosing) {
              console.log(`OpenAI connection lost, attempting reconnection (attempt ${connectionAttempts + 1}/${maxConnectionAttempts})`);
              setTimeout(() => {
                if (!isClosing && socket.readyState === WebSocket.OPEN) {
                  connectToOpenAI();
                }
              }, 1000);
            } else if (!isClosing && socket.readyState === WebSocket.OPEN) {
              socket.send(JSON.stringify({ 
                type: 'connection_closed', 
                code: event.code, 
                reason: event.reason || 'OpenAI connection closed'
              }));
              socket.close(event.code, event.reason);
            }
          };
        } catch (error) {
          console.error("Error creating OpenAI WebSocket:", error);
          if (socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({ 
              type: 'error', 
              message: 'Failed to establish AI connection. Please try again.' 
            }));
            socket.close(1011, 'Failed to connect to AI service');
          }
        }
      };

      // Start initial connection
      connectToOpenAI();
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log("Client message type:", data.type);
        
        // Forward client messages to OpenAI if connected
        if (openAISocket && openAISocket.readyState === WebSocket.OPEN) {
          console.log("Forwarding message to OpenAI");
          openAISocket.send(event.data);
        } else {
          console.warn("OpenAI socket not ready, message queued or dropped:", {
            openAISocketExists: !!openAISocket,
            readyState: openAISocket?.readyState,
            messageType: data.type
          });
          
          // Send error back to client if OpenAI is not connected
          if (socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({
              type: 'error',
              message: 'AI service not available. Reconnecting...'
            }));
          }
        }
      } catch (error) {
        console.error("Error processing client message:", error);
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format'
          }));
        }
      }
    };

    socket.onclose = (event) => {
      console.log("Client disconnected:", event.code, event.reason);
      isClosing = true;
      if (openAISocket && openAISocket.readyState === WebSocket.OPEN) {
        openAISocket.close(1000, 'Client disconnected');
      }
    };

    socket.onerror = (error) => {
      console.error("Client WebSocket error:", error);
      isClosing = true;
      if (openAISocket && openAISocket.readyState === WebSocket.OPEN) {
        openAISocket.close(1011, 'Client error');
      }
    };

    return response;
  } catch (error) {
    console.error("Error upgrading to WebSocket:", error);
    return new Response("Failed to upgrade to WebSocket", { 
      status: 500,
      headers: corsHeaders
    });
  }
});

async function handleHuntingFunctionCall(data: any, openAISocket: WebSocket | null) {
  if (data.name === 'get_current_hunting_conditions') {
    try {
      const args = JSON.parse(data.arguments);
      console.log("Handling current hunting conditions request:", args);
      
      const currentDate = new Date();
      const month = currentDate.getMonth() + 1;
      const day = currentDate.getDate();
      
      // Determine current rut phase based on date
      let rutPhase = "Off Season";
      if (month === 10 && day >= 15 && day <= 24) rutPhase = "Pre-Rut";
      else if (month === 10 && day >= 25 || month === 11 && day <= 4) rutPhase = "Seeking Phase";
      else if (month === 11 && day >= 5 && day <= 9) rutPhase = "Chasing Phase";
      else if (month === 11 && day >= 10 && day <= 18) rutPhase = "Peak Breeding";
      else if (month === 11 && day >= 19 && day <= 30) rutPhase = "Post-Rut";
      else if (month === 12 && day >= 5 && day <= 15) rutPhase = "Second Rut";
      
      // Enhanced hunting conditions with realistic data
      const conditions = {
        location: args.location || "Midwest",
        currentDate: currentDate.toLocaleDateString(),
        rutPhase: rutPhase,
        weather: {
          temperature: Math.floor(Math.random() * 40) + 25, // 25-65°F
          windSpeed: Math.floor(Math.random() * 15) + 2,
          windDirection: ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'][Math.floor(Math.random() * 8)],
          pressure: (29.2 + Math.random() * 1.8).toFixed(2), // 29.2-31.0 inHg
          humidity: Math.floor(Math.random() * 40) + 40,
          precipitation: Math.random() < 0.25,
          cloudCover: Math.floor(Math.random() * 100),
          forecast: "High pressure system moving in, excellent hunting conditions"
        },
        moonPhase: {
          phase: ["New Moon", "Waxing Crescent", "First Quarter", "Waxing Gibbous", "Full Moon", "Waning Gibbous", "Last Quarter", "Waning Crescent"][Math.floor(Math.random() * 8)],
          illumination: Math.floor(Math.random() * 100),
          overhead: `${Math.floor(Math.random() * 12) + 10}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')} PM`
        },
        huntingStrategy: generateHuntingStrategy(rutPhase, month),
        deerMovement: generateMovementPrediction(rutPhase),
        recommendation: generateDailyRecommendation(rutPhase, month, day)
      };

      function generateHuntingStrategy(phase: string, month: number) {
        const strategies = {
          "Pre-Rut": "Focus on fresh scrapes and rub lines. Set up near food sources during evening hours. Use light grunt calls.",
          "Seeking Phase": "Hunt travel corridors and pinch points. All-day sits can be productive. Use grunt calls and light rattling.",
          "Chasing Phase": "Focus on doe bedding areas and escape routes. Aggressive calling tactics work well. Hunt all day.",
          "Peak Breeding": "Target areas where lone does may travel. Use doe bleats to attract locked-down bucks. Check thick cover.",
          "Post-Rut": "Focus on high-energy food sources. Bucks are recovering and feeding heavily. Hunt food plots and oak groves.",
          "Second Rut": "Watch for renewed scraping activity. Focus on areas with doe fawns. Use light calling tactics.",
          "Off Season": month <= 3 ? "Scout and glass for shed antlers. Pattern deer for next season." : "Prepare food plots and habitat work."
        };
        return strategies[phase] || strategies["Off Season"];
      }

      function generateMovementPrediction(phase: string) {
        const predictions = {
          "Pre-Rut": "Moderate movement focused on territory establishment",
          "Seeking Phase": "Increased daytime movement as bucks expand range",
          "Chasing Phase": "Peak daytime movement with intense chasing activity",
          "Peak Breeding": "Reduced movement as bucks lock down with does",
          "Post-Rut": "Limited movement focused on feeding and recovery",
          "Second Rut": "Moderate increase in movement for secondary breeding",
          "Off Season": "Basic feeding patterns, early morning and evening"
        };
        return predictions[phase] || predictions["Off Season"];
      }

      function generateDailyRecommendation(phase: string, month: number, day: number) {
        if (phase === "Chasing Phase") return "OUTSTANDING day to be in the woods - peak rut activity expected";
        if (phase === "Peak Breeding") return "Good day for patient hunters - focus on doe areas";
        if (phase === "Seeking Phase") return "Excellent movement expected - all day sits recommended";
        if (phase === "Pre-Rut") return "Good hunting with focus on scrape lines and rubs";
        if (phase === "Post-Rut") return "Fair hunting - focus on food sources and recovery areas";
        return "Plan your strategy and scout for better conditions";
      }

      const functionResponse = {
        type: 'conversation.item.create',
        item: {
          type: 'function_call_output',
          call_id: data.call_id,
          output: JSON.stringify(conditions)
        }
      };

      if (openAISocket && openAISocket.readyState === WebSocket.OPEN) {
        openAISocket.send(JSON.stringify(functionResponse));
        openAISocket.send(JSON.stringify({ type: 'response.create' }));
      }
      
    } catch (error) {
      console.error("Function call error:", error);
    }
  } else if (data.name === 'analyze_blood_trail') {
    try {
      const args = JSON.parse(data.arguments);
      console.log("Handling blood trail analysis:", args);
      
      // Enhanced blood trail analysis based on color
      const bloodAnalysis = {
        bloodColor: args.blood_color,
        hitLocation: args.location_hit || "Unknown",
        analysis: generateBloodAnalysis(args.blood_color),
        waitTime: generateWaitTime(args.blood_color),
        trackingTips: generateTrackingTips(args.blood_color),
        recoveryProbability: generateRecoveryProbability(args.blood_color)
      };

      function generateBloodAnalysis(color: string) {
        const lowerColor = color.toLowerCase();
        if (lowerColor.includes('bright') && lowerColor.includes('red')) {
          return "Bright red blood suggests arterial hit - excellent sign for quick recovery";
        } else if (lowerColor.includes('pink') || lowerColor.includes('frothy')) {
          return "Pink frothy blood indicates lung hit - very good for recovery with proper wait time";
        } else if (lowerColor.includes('dark') && lowerColor.includes('red')) {
          return "Dark red blood suggests liver or muscle hit - requires patience and longer wait time";
        } else if (lowerColor.includes('brown') || lowerColor.includes('green')) {
          return "Brown/green matter indicates gut shot - requires extended wait time of 8-12 hours";
        }
        return "Blood color analysis requires more specific description for accurate assessment";
      }

      function generateWaitTime(color: string) {
        const lowerColor = color.toLowerCase();
        if (lowerColor.includes('bright') && lowerColor.includes('red')) return "30 minutes - arterial hit";
        if (lowerColor.includes('pink') || lowerColor.includes('frothy')) return "45-60 minutes - lung hit";
        if (lowerColor.includes('dark')) return "3-4 hours - liver/muscle hit";
        if (lowerColor.includes('brown') || lowerColor.includes('green')) return "8-12 hours - gut shot";
        return "2-4 hours - conservative approach recommended";
      }

      function generateTrackingTips(color: string) {
        return [
          "Mark your shot location and last blood with GPS",
          "Move slowly and quietly along the blood trail",
          "Look for blood at different heights on vegetation",
          "Use flagging tape to mark blood spots",
          "Grid search if blood trail is lost",
          "Consider bringing help for tracking"
        ];
      }

      function generateRecoveryProbability(color: string) {
        const lowerColor = color.toLowerCase();
        if (lowerColor.includes('bright') && lowerColor.includes('red')) return "90-95% with proper tracking";
        if (lowerColor.includes('pink') || lowerColor.includes('frothy')) return "85-90% with adequate wait time";
        if (lowerColor.includes('dark')) return "60-75% depending on exact hit location";
        if (lowerColor.includes('brown') || lowerColor.includes('green')) return "50-65% if left undisturbed";
        return "Variable - depends on shot placement and tracking execution";
      }

      const functionResponse = {
        type: 'conversation.item.create',
        item: {
          type: 'function_call_output',
          call_id: data.call_id,
          output: JSON.stringify(bloodAnalysis)
        }
      };

      if (openAISocket && openAISocket.readyState === WebSocket.OPEN) {
        openAISocket.send(JSON.stringify(functionResponse));
        openAISocket.send(JSON.stringify({ type: 'response.create' }));
      }
      
    } catch (error) {
      console.error("Blood trail analysis error:", error);
    }
  }
}
