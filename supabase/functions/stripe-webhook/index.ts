import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  // Create Supabase client with service role key for admin operations
  const supabaseAdmin = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    { auth: { persistSession: false } }
  );

  try {
    const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
      apiVersion: "2023-10-16",
    });

    const body = await req.text();
    const sig = req.headers.get("stripe-signature");
    
    if (!sig) {
      throw new Error("Missing Stripe signature");
    }

    const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");
    if (!webhookSecret) {
      throw new Error("Missing webhook secret");
    }

    // Verify the webhook signature
    const event = stripe.webhooks.constructEvent(body, sig, webhookSecret);
    
    console.log("Webhook event type:", event.type);

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        
        // Check if this is a promotion subscription
        if (session.metadata?.product_type === 'service_promotion') {
          console.log("Processing promotion subscription checkout completion");
          
          if (session.mode === 'subscription' && session.subscription) {
            // Get subscription details
            const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
            
            // Update user's promotion status
            const { error } = await supabaseAdmin.rpc('handle_promotion_subscription_update', {
              user_email: session.customer_email || session.customer_details?.email,
              stripe_customer_id: session.customer as string,
              subscription_status: subscription.status,
              current_period_end: subscription.current_period_end
            });

            if (error) {
              console.error("Error updating promotion status:", error);
              throw error;
            }

            console.log("Successfully updated promotion status for user:", session.customer_email);
          }
        }
        break;
      }

      case 'customer.subscription.updated':
      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        
        // Get customer details
        const customer = await stripe.customers.retrieve(subscription.customer as string);
        
        if ('email' in customer && customer.email) {
          const status = event.type === 'customer.subscription.deleted' ? 'canceled' : subscription.status;
          
          // Update user's promotion status
          const { error } = await supabaseAdmin.rpc('handle_promotion_subscription_update', {
            user_email: customer.email,
            stripe_customer_id: subscription.customer as string,
            subscription_status: status,
            current_period_end: subscription.current_period_end
          });

          if (error) {
            console.error("Error updating subscription status:", error);
            throw error;
          }

          console.log("Successfully updated subscription status for user:", customer.email);
        }
        break;
      }

      default:
        console.log("Unhandled event type:", event.type);
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error('Webhook error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 400,
    });
  }
});