
// Function to generate a secure encryption key for admins only
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { generateEncryptionKey } from "../_shared/encryption.ts";
import { rateLimitMiddleware } from "../_shared/rate-limiter.ts";

serve(async (req: Request) => {
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }
  
  // Apply strict rate limiting - this is an admin function
  const rateLimitResult = rateLimitMiddleware(req, {
    limit: 5, // Very strict limit since this is rarely needed
    windowSizeInSeconds: 60
  });

  if (rateLimitResult) {
    return rateLimitResult;
  }

  try {
    // Get authorization header
    const authHeader = req.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return new Response(
        JSON.stringify({ error: "Authorization required" }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Verify it's a service role key for admin operations
    const token = authHeader.replace("Bearer ", "");
    const serviceRoleKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
    
    if (token !== serviceRoleKey) {
      return new Response(
        JSON.stringify({ error: "Unauthorized. Admin access required." }),
        {
          status: 403,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }
    
    // Generate a new encryption key
    const key = await generateEncryptionKey();
    
    return new Response(
      JSON.stringify({ 
        key,
        instructions: "Store this key securely in your Supabase secrets as ENCRYPTION_KEY" 
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error("Error generating encryption key:", error);
    
    return new Response(
      JSON.stringify({ error: error.message || "Failed to generate encryption key" }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});
