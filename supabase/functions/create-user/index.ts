
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.33.1'

// Define types for the request body
interface CreateUserRequest {
  email: string;
  password: string;
  username: string;
  role?: string;
}

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Create a Supabase client with the service role key
const supabaseAdmin = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  // Check that the request is POST
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Not authorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Verify the user making the request is authenticated
    const token = authHeader.replace('Bearer ', '')
    const {
      data: { user: authenticatedUser },
      error: authError,
    } = await supabaseAdmin.auth.getUser(token)

    if (authError || !authenticatedUser) {
      return new Response(JSON.stringify({ error: 'Not authorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Verify calling user is an admin by checking their profile badges
    const { data: adminProfile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('badges')
      .eq('id', authenticatedUser.id)
      .single()

    if (profileError || !adminProfile || !adminProfile.badges || !adminProfile.badges.includes('admin')) {
      return new Response(JSON.stringify({ error: 'Not authorized - admin access required' }), {
        status: 403,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Parse request body to get user details
    const { email, password, username, role } = await req.json() as CreateUserRequest

    // Check that all required fields are present
    if (!email || !password || !username) {
      return new Response(JSON.stringify({ error: 'Email, password, and username are required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    console.log(`Admin ${authenticatedUser.email} is creating a new user: ${email}`)

    // Create the new user using the admin API
    const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Skip email verification
      user_metadata: {
        username,
      }
    })

    if (createError) {
      throw createError
    }

    // If a role was specified, add it as a badge to the user's profile
    if (role && role !== 'user' && newUser.user) {
      const badges = role === 'admin' ? ['admin'] : 
                    role === 'verified' ? ['verified'] :
                    role === 'featured' ? ['featured'] : []
      
      if (badges.length > 0) {
        const { error: badgeError } = await supabaseAdmin
          .from('profiles')
          .update({ badges })
          .eq('id', newUser.user.id)
        
        if (badgeError) {
          console.error('Error assigning badges:', badgeError)
          // Don't throw here, user was still created
        }
      }
    }

    return new Response(JSON.stringify({ success: true, user: newUser.user }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Error creating user:', error)
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

Deno.serve(handler)
