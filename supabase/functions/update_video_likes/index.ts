
// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/getting_started/setup_your_environment

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.0";

const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY") || "";
const serviceRoleKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the service role key for more privileges
    const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey);
    
    // Parse the request body to get the video ID
    const { video_id_param } = await req.json();
    
    if (!video_id_param) {
      return new Response(
        JSON.stringify({ error: "Video ID is required" }),
        { 
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400 
        }
      );
    }
    
    // Count the likes for this video
    const { count, error: countError } = await supabaseAdmin
      .from('likes')
      .select('*', { count: 'exact', head: true })
      .eq('video_id', video_id_param);
    
    if (countError) {
      console.error('Error counting likes:', countError);
      throw countError;
    }
    
    // Update the likes count in the videos table
    const { data, error: updateError } = await supabaseAdmin
      .from('videos')
      .update({ likes: count || 0 })
      .eq('id', video_id_param)
      .select('likes');
      
    if (updateError) {
      console.error('Error updating video like count:', updateError);
      throw updateError;
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Like count updated successfully", 
        likeCount: count || 0,
        data
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200
      }
    );
  } catch (error) {
    console.error('Error in update_video_like_count function:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500 
      }
    );
  }
});
