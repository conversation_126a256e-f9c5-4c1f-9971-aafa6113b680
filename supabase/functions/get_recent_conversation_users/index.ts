
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req: Request) => {
  // Handle CORS
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log('🔍 Starting get_recent_conversation_users function');
    
    // Get the authorization header from the request
    const authHeader = req.headers.get("authorization");
    console.log('🔑 Auth header present:', !!authHeader);
    
    if (!authHeader) {
      console.error('❌ Missing authorization header');
      return new Response(
        JSON.stringify({ error: "Missing authorization header" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }

    // Extract the JWT token from the Bearer header
    const token = authHeader.replace('Bearer ', '');
    console.log('🎫 Token extracted, length:', token.length);

    // Create supabase client with service role key for admin operations
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Create supabase client with user token for auth verification
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? ""
    );

    console.log('🏗️ Supabase clients created');

    // Get the current user from the auth token using the admin client
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);
    
    console.log('👤 User fetch result:', !!user, userError ? 'Error: ' + userError.message : 'Success');
    
    if (userError || !user) {
      console.error('❌ Invalid authorization token:', userError?.message);
      return new Response(
        JSON.stringify({ error: "Invalid authorization token", details: userError?.message }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }

    const current_user_id = user.id;
    console.log('🔍 Fetching conversation users for:', current_user_id);

    // Use admin client to query messages bypassing RLS
    const { data: sentTo, error: sentError } = await supabaseAdmin
      .from("messages")
      .select("receiver_id")
      .eq("sender_id", current_user_id)
      .eq("message_type", "direct")
      .is("group_chat_id", null)
      .order("created_at", { ascending: false });

    if (sentError) {
      console.error('❌ Error fetching sent messages:', sentError);
      return new Response(
        JSON.stringify({ error: "Error fetching sent messages", details: sentError.message }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    const { data: receivedFrom, error: receivedError } = await supabaseAdmin
      .from("messages")
      .select("sender_id")
      .eq("receiver_id", current_user_id)
      .eq("message_type", "direct")
      .is("group_chat_id", null)
      .order("created_at", { ascending: false });

    if (receivedError) {
      console.error('❌ Error fetching received messages:', receivedError);
      return new Response(
        JSON.stringify({ error: "Error fetching received messages", details: receivedError.message }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    // Combine and deduplicate user IDs
    const userIds = new Set<string>();
    sentTo?.forEach((msg) => userIds.add(msg.receiver_id));
    receivedFrom?.forEach((msg) => userIds.add(msg.sender_id));

    const userIdsArray = Array.from(userIds);
    console.log('✅ Found conversation partners:', userIdsArray.length, 'users');

    return new Response(JSON.stringify(userIdsArray), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error('❌ Unexpected error in get_recent_conversation_users:', error);
    return new Response(JSON.stringify({ 
      error: "Internal server error", 
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
