
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req: Request) => {
  // Handle CORS
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log('📤 Starting send_direct_message function');
    
    // Get the authorization header from the request
    const authHeader = req.headers.get("authorization");
    console.log('🔑 Auth header present:', !!authHeader);
    
    if (!authHeader) {
      console.error('❌ Missing authorization header');
      return new Response(
        JSON.stringify({ error: "Missing authorization header" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }

    // Extract the JWT token from the Bearer header
    const token = authHeader.replace('Bearer ', '');
    console.log('🎫 Token extracted, length:', token.length);

    // Create supabase client with service role key for admin operations
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    console.log('🏗️ Supabase clients created');

    // Get the current user from the auth token using the admin client
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);
    
    console.log('👤 User fetch result:', !!user, userError ? 'Error: ' + userError.message : 'Success');
    
    if (userError || !user) {
      console.error('❌ Invalid authorization token:', userError?.message);
      return new Response(
        JSON.stringify({ error: "Invalid authorization token", details: userError?.message }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }

    // Extract message data from request
    const messageData = await req.json();

    if (!messageData.sender_id || !messageData.receiver_id) {
      console.error('❌ Missing required parameters');
      return new Response(
        JSON.stringify({
          error: "Missing required parameters sender_id or receiver_id",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Verify that the current user is the sender
    if (user.id !== messageData.sender_id) {
      console.error('❌ Unauthorized: user is not the sender');
      return new Response(
        JSON.stringify({ error: "Unauthorized: user is not the sender" }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 403,
        }
      );
    }

    console.log('📤 Sending message:', messageData);

    // Use admin client to insert message bypassing RLS
    const { data, error } = await supabaseAdmin
      .from("messages")
      .insert(messageData)
      .select();

    if (error) {
      console.error('❌ Error sending message:', error);
      return new Response(
        JSON.stringify({ error: "Error sending message", details: error.message }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    console.log('✅ Message sent successfully:', data);

    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error('❌ Error in send_direct_message:', error);
    return new Response(JSON.stringify({ 
      error: "Internal server error", 
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
