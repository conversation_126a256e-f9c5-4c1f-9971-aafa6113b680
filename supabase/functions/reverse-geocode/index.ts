
// Follow Deno runtime's TypeScript definitions
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.5'
import { corsHeaders } from '../_shared/cors.ts'

interface GeocodeRequest {
  latitude?: number;
  longitude?: number;
  zipCode?: string;
}

const GEOCODE_API_URL = "https://geocode.maps.co/reverse";
const ZIPCODE_API_URL = "https://geocode.maps.co/search";

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const requestData = await req.json() as GeocodeRequest;
    
    // Check if we're searching by zip code
    if (requestData.zipCode) {
      console.log(`Processing zip code search for: ${requestData.zipCode}`);
      // Perform a forward geocode lookup using zip code
      const zipCodeUrl = `${ZIPCODE_API_URL}?postalcode=${requestData.zipCode}&country=USA&format=json&limit=1`;
      console.log(`Calling geocode API: ${zipCodeUrl}`);
      
      const response = await fetch(zipCodeUrl);
      const data = await response.json();
      
      console.log(`Geocode API response for zip ${requestData.zipCode}:`, data);
      
      if (!data || data.length === 0) {
        return new Response(
          JSON.stringify({ error: 'Location not found for the provided zip code' }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 404 
          }
        );
      }
      
      // Extract location data from the response
      const location = data[0];
      
      // Parse the location display_name to extract state, county, and city
      const addressParts = location.display_name.split(',').map((part: string) => part.trim());
      
      // For US addresses, format is typically: "City, County, State ZIP, Country"
      let state = '';
      let county = '';
      let city = '';
      let zipCode = requestData.zipCode; // Store the original zip code as well
      
      // Extract state - typically the second-to-last part (before country)
      if (addressParts.length >= 2) {
        // Check if the second-to-last part contains the ZIP code
        const stateWithZip = addressParts[addressParts.length - 2];
        if (stateWithZip.includes(requestData.zipCode)) {
          // Extract just the state part
          state = stateWithZip.split(' ').filter(part => !part.match(/^\d+$/)).join(' ');
        } else {
          state = stateWithZip;
        }
      }
      
      // Extract city - typically the first part
      if (addressParts.length >= 1) {
        city = addressParts[0];
      }
      
      // Extract county - often the second part if it contains "County"
      for (const part of addressParts) {
        if (part.toLowerCase().includes('county')) {
          county = part.replace(' County', '');
          break;
        }
      }
      
      // Use location.address if available (more structured data)
      if (location.address) {
        state = location.address.state || state;
        city = location.address.city || location.address.town || location.address.village || city;
        county = location.address.county ? location.address.county.replace(' County', '') : county;
        // Make sure we have the zip code from the request
        zipCode = requestData.zipCode;
      }
      
      return new Response(
        JSON.stringify({ 
          state,
          county,
          city,
          zipCode, // Include the original zip code in the response
          latitude: parseFloat(location.lat),
          longitude: parseFloat(location.lon),
          fullData: location
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      );
    }
    
    // If not searching by zip, proceed with reverse geocoding using lat/long
    const { latitude, longitude } = requestData;
    
    if (!latitude || !longitude) {
      return new Response(
        JSON.stringify({ error: 'Latitude and longitude are required for reverse geocoding' }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400 
        }
      );
    }

    console.log(`Processing reverse geocode for lat: ${latitude}, long: ${longitude}`);
    // Use the free geocode.maps.co API for reverse geocoding
    const geocodeUrl = `${GEOCODE_API_URL}?lat=${latitude}&lon=${longitude}&format=json`;
    const response = await fetch(geocodeUrl);
    const data = await response.json();
    
    console.log(`Reverse geocode API response:`, data);

    // Extract state and county from response
    let state = data?.address?.state || '';
    let county = data?.address?.county || '';
    let city = data?.address?.city || data?.address?.town || data?.address?.village || '';
    let zipCode = data?.address?.postcode || '';
    
    // Clean up county name if it ends with "County"
    if (county.endsWith(' County')) {
      county = county.replace(' County', '');
    }
    
    return new Response(
      JSON.stringify({ 
        state,
        county,
        city,
        zipCode,
        latitude,
        longitude,
        fullData: data // Include full data for debugging or additional info
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    );
  } catch (error) {
    console.error("Error in reverse-geocode function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
        status: 500 
      }
    );
  }
});
