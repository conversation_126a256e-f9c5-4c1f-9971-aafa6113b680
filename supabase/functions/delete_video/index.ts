
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
import { corsHeaders, rateLimitMiddleware } from "../_shared/cors.ts";

// This edge function implements secure video deletion with verification that
// the user is authorized to delete the specified video
serve(async (req: Request) => {
  // Handle CORS
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Apply strict rate limiting for video deletion operations (5 per minute per user)
    const rateLimitResponse = rateLimitMiddleware(req, {
      limit: 5,
      windowSizeInSeconds: 60
    });
    
    if (rateLimitResponse) {
      return rateLimitResponse;
    }
    
    // Get the JWT token from the authorization header
    const authorization = req.headers.get("Authorization");
    if (!authorization) {
      return new Response(
        JSON.stringify({ error: "Missing authorization header" }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Extract the token without the "Bearer " prefix
    const token = authorization.replace("Bearer ", "");

    // Initialize Supabase client with admin privileges
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { 
        auth: { persistSession: false }
      }
    );

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: "Invalid authorization token" }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Extract video ID from request
    const { videoId } = await req.json();

    if (!videoId) {
      return new Response(
        JSON.stringify({ error: "Missing videoId parameter" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Check if user is an admin
    const { data: profileData, error: profileError } = await supabaseClient
      .from('profiles')
      .select('badges')
      .eq('id', user.id)
      .single();
      
    const isAdmin = profileData?.badges?.includes("admin") || false;

    // Verify the user owns this video or is an admin
    const { data: videoData, error: videoError } = await supabaseClient
      .from('videos')
      .select('user_id, video_url')
      .eq('id', videoId)
      .single();

    if (videoError || !videoData) {
      return new Response(
        JSON.stringify({ error: "Video not found" }),
        {
          status: 404,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Security check: Ensure the user is either the owner of the video OR an admin
    if (videoData.user_id !== user.id && !isAdmin) {
      return new Response(
        JSON.stringify({ error: "Unauthorized to delete this video" }),
        {
          status: 403,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Delete any comments associated with the video
    const { error: commentsError } = await supabaseClient
      .from('comments')
      .delete()
      .eq('video_id', videoId);
      
    if (commentsError) {
      console.error(`Error deleting comments for video ${videoId}:`, commentsError);
      // Continue with video deletion even if comment deletion fails
    } else {
      console.log(`Successfully deleted comments for video ${videoId}`);
    }
    
    // Delete any likes associated with the video
    const { error: likesError } = await supabaseClient
      .from('likes')
      .delete()
      .eq('video_id', videoId);
      
    if (likesError) {
      console.error(`Error deleting likes for video ${videoId}:`, likesError);
      // Continue with video deletion even if likes deletion fails
    } else {
      console.log(`Successfully deleted likes for video ${videoId}`);
    }

    // Delete the video itself
    const { error: deleteError } = await supabaseClient
      .from('videos')
      .delete()
      .eq('id', videoId);
    
    if (deleteError) {
      return new Response(
        JSON.stringify({ error: "Failed to delete video" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Try to delete the video file from storage if possible
    if (videoData.video_url) {
      try {
        const filePath = new URL(videoData.video_url).pathname.split('/').pop();
        if (filePath) {
          await supabaseClient.storage.from('videos').remove([`videos/${filePath}`]);
        }
      } catch (storageError) {
        // Just log storage errors, don't fail the operation
        console.error("Error removing video file from storage:", storageError);
      }
    }

    return new Response(
      JSON.stringify({ success: true }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
