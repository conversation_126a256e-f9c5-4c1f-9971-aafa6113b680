import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import ProfileHeader, { ServiceItem } from "@/components/ProfileHeader";
import MainNavigation from "@/components/MainNavigation";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate, useParams } from "react-router-dom";
import VideoThumbnail from "@/components/VideoThumbnail";
import { useAuth } from "@/contexts/AuthContext";
import { MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

interface UserProfileData {
  username: string;
  avatarUrl: string;
  bio: string;
  region?: string;
  badges?: string[];
  services?: ServiceItem[];
  website?: string;
}

interface VideoItem {
  id: string;
  video_url: string;
  soundtrack_url?: string | null;
  likes: number;
  views: number;
}

const UserProfile = () => {
  const { userId } = useParams();
  const [userProfile, setUserProfile] = useState<UserProfileData>({
    username: "",
    avatarUrl: "",
    bio: "",
    region: "",
    badges: [],
    services: [],
    website: ""
  });
  const [userVideos, setUserVideos] = useState<VideoItem[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { user } = useAuth(); // Get the current logged-in user

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!userId) return;
      
      try {
        setLoading(true);
        
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();
          
        if (profileError) {
          console.error("Error fetching user profile:", profileError);
          return;
        }
        
        if (profileData) {
          // Convert services from the database to our ServiceItem format
          const services = Array.isArray(profileData.services) 
            ? profileData.services.map((service: any) => ({
                type: service.type || "",
                description: service.description || "",
                contactMethod: service.contactMethod || "",
                contactValue: service.contactValue || ""
              }))
            : [];
          
          setUserProfile({
            username: profileData.username || "User",
            avatarUrl: profileData.avatar_url || "https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",
            bio: profileData.bio || "Whitetail Livn User",
            region: profileData.region || undefined,
            badges: profileData.badges || ["Hunter"],
            services: services,
            website: profileData.website || ""
          });
        }
        
        // Fetch videos with soundtrack information
        const { data: videosData, error: videosError } = await supabase
          .from('videos')
          .select('*, soundtrack_data:sound_library(*)')
          .eq('user_id', userId)
          .order('created_at', { ascending: false });
          
        if (videosError) {
          console.error("Error fetching user videos:", videosError);
          return;
        }
        
        if (videosData) {
          console.log("Fetched videos with soundtrack data:", videosData);
          setUserVideos(videosData.map((video: any) => ({
            id: video.id,
            video_url: video.video_url,
            soundtrack_url: video.soundtrack_data?.sound_url || null,
            likes: video.likes || 0,
            views: video.views || 0
          })));
        }
      } catch (error) {
        console.error("Error in fetchUserProfile:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserProfile();

    // Set up real-time subscription for video updates
    const channel = supabase
      .channel('user-profile-videos')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'videos',
        filter: userId ? `user_id=eq.${userId}` : undefined
      }, (payload) => {
        console.log('Video updated in user profile:', payload);
        if (payload.new) {
          setUserVideos(prevVideos => 
            prevVideos.map(video => 
              video.id === payload.new.id 
                ? { ...video, views: payload.new.views || 0 }
                : video
            )
          );
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [userId]);

  const handleVideoPlay = async (videoId: string) => {
    try {
      // Call the database function to increment views
      const { error } = await supabase.rpc('increment_video_views', {
        video_id_param: videoId
      });
      
      if (error) {
        console.error('Error incrementing video views:', error);
        return;
      }

      // Update local state immediately for better UX
      setUserVideos(prevVideos => 
        prevVideos.map(video => 
          video.id === videoId 
            ? { ...video, views: video.views + 1 }
            : video
        )
      );

      // Call the edge function to ensure the views table is updated
      try {
        const { error: edgeError } = await supabase.functions.invoke('update_video_likes', {
          body: { video_id_param: videoId }
        });
        
        if (edgeError) {
          console.error('Error calling edge function:', edgeError);
        }
      } catch (edgeError) {
        console.error('Edge function call failed:', edgeError);
      }
    } catch (error) {
      console.error('Error in handleVideoPlay:', error);
    }
  };

  const handleMessageUser = () => {
    // Navigate to inbox with the user ID as a parameter
    if (userId) {
      navigate(`/inbox?newMessage=${userId}`);
    }
  };

  // Determine if this is the current user's profile
  const isCurrentUser = user && userId === user.id;

  return (
    <div className="pb-16 bg-background min-h-screen">
      <ProfileHeader 
        username={userProfile.username}
        avatarUrl={userProfile.avatarUrl}
        bio={userProfile.bio}
        isCurrentUser={isCurrentUser}
        region={userProfile.region}
        badges={userProfile.badges}
        services={userProfile.services}
        website={userProfile.website}
        userId={userId}
      />
      
      {/* Message Button - Only show if not viewing your own profile and user is logged in */}
      {!isCurrentUser && user && (
        <div className="px-4 mb-4">
          <Button 
            onClick={handleMessageUser}
            className="w-full flex items-center justify-center gap-2 bg-forest hover:bg-forest/90"
          >
            <MessageCircle size={18} />
            <span>Message</span>
          </Button>
        </div>
      )}
      
      <Tabs defaultValue="videos" className="mt-2">
        <TabsList className="w-full">
          <TabsTrigger value="videos" className="w-full">Videos</TabsTrigger>
        </TabsList>
        
        <TabsContent value="videos" className="p-4">
          {loading ? (
            <div className="flex justify-center p-4">
              <p>Loading videos...</p>
            </div>
          ) : userVideos.length > 0 ? (
            <div className="grid grid-cols-3 gap-2 sm:gap-3 md:gap-4">
              {userVideos.map(video => (
                <VideoThumbnail
                  key={video.id}
                  videoId={video.id}
                  videoUrl={video.video_url}
                  soundtrackUrl={video.soundtrack_url}
                  size="small"
                  navigateOnClick={false}
                  onClick={() => handleVideoPlay(video.id)}
                >
                  {/* Children content */}
                </VideoThumbnail>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <p className="text-muted-foreground">No videos yet</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
      
      <MainNavigation />
    </div>
  );
};

export default UserProfile;
