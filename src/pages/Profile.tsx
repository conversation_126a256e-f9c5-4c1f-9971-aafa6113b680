import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { useAuth } from "@/contexts/AuthContext";
import MainNavigation from "@/components/MainNavigation";
import { supabase } from "@/integrations/supabase/client";
import ProfileHeader, { ServiceItem } from "@/components/ProfileHeader";
import { useNavigate } from "react-router-dom";
import VideoThumbnail from "@/components/VideoThumbnail";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import MultiDeleteButton from "@/components/MultiDeleteButton";
import DeleteVideoButton from "@/components/DeleteVideoButton";
import InviteCodeManager from "@/components/InviteCodeManager";
import { toast } from "sonner";
import WhitetailScore from "@/components/WhitetailScore";
import { useWhitetailScore } from "@/hooks/useWhitetailScore";

// Define the interface for video items
interface VideoItem {
  id: string;
  created_at: string;
  user_id: string;
  caption?: string;
  likes: number;
  comments: number;
  game_type?: string;
  tags?: string[];
  views: number;
  video_url: string;
  soundtrack_url?: string | null;
}

// Extend the ProfileData interface to include services
interface ExtendedProfileData {
  services?: ServiceItem[];
  username?: string;
  avatar_url?: string;
  bio?: string;
  region?: string;
  badges?: string[];
  website?: string;
}

const Profile = () => {
  const [activeTab, setActiveTab] = useState("videos");
  const { user, profile, updateProfile } = useAuth();
  const navigate = useNavigate();
  const [userProfile, setUserProfile] = useState({
    username: "",
    avatarUrl: "",
    bio: "",
    region: "",
    badges: [] as string[],
    services: [] as ServiceItem[],
    website: ""
  });
  const [userVideos, setUserVideos] = useState<VideoItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [profileDataLoaded, setProfileDataLoaded] = useState(false);
  const [selectedVideos, setSelectedVideos] = useState<string[]>([]);
  const [selectMode, setSelectMode] = useState(false);
  const [playingVideoId, setPlayingVideoId] = useState<string | null>(null);
  const [profileUpdated, setProfileUpdated] = useState(false);
  const whitetailScore = useWhitetailScore();

  useEffect(() => {
    // Make sure no videos autoplay on initial load
    setPlayingVideoId(null);
  }, [profile]);

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        setProfileDataLoaded(false);
        
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
          
        if (error) {
          console.error("Error fetching user profile:", error);
          return;
        }
        
        if (data) {
          // Map profile data to our format
          const profileData = {
            username: data.username || "User",
            avatarUrl: data.avatar_url || "https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",
            bio: data.bio || "Whitetail Livn User",
            region: data.region || "",
            badges: data.badges || ["Hunter"],
            website: data.website || "",
            services: Array.isArray(data.services) 
              ? data.services.map((service: any) => ({
                  type: service.type || "",
                  description: service.description || "",
                  contactMethod: service.contactMethod || "",
                  contactValue: service.contactValue || ""
                }))
              : []
          };
          
          setUserProfile(profileData);
          setProfileDataLoaded(true);
        }
        
        fetchUserVideos();
      } catch (error) {
        console.error("Error in fetchUserProfile:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserProfile();
    
    const channel = supabase
      .channel('profile-videos')
      .on('postgres_changes', {
        event: 'DELETE',
        schema: 'public',
        table: 'videos',
        filter: user ? `user_id=eq.${user.id}` : undefined
      }, (payload) => {
        console.log('Video deleted in profile:', payload);
        if (payload.old?.id) {
          setUserVideos(prevVideos => 
            prevVideos.filter(video => video.id !== payload.old.id)
          );
          setSelectedVideos(prev => prev.filter(id => id !== payload.old.id));
        }
      })
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'videos',
        filter: user ? `user_id=eq.${user.id}` : undefined
      }, (payload) => {
        console.log('Video updated in profile:', payload);
        if (payload.new) {
          setUserVideos(prevVideos => 
            prevVideos.map(video => 
              video.id === payload.new.id 
                ? { ...video, views: payload.new.views || 0 }
                : video
            )
          );
        }
      })
      .subscribe();
    
    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, profileUpdated]);

  const fetchUserVideos = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      console.log("Fetching videos for user ID:", user.id);
      
      const { data, error } = await supabase
        .from('videos')
        .select('*, soundtrack_data:sound_library(*)')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });
        
      if (error) throw error;
      console.log("Fetched video data:", data);
      
      if (data) {
        // Map the video data to our format
        const formattedVideos = data.map(video => ({
          id: video.id,
          created_at: video.created_at,
          user_id: video.user_id,
          caption: video.caption || "",
          likes: video.likes || 0,
          comments: video.comments || 0,
          game_type: video.game_type || "",
          tags: video.tags || [],
          views: video.views || 0,
          video_url: video.video_url,
          soundtrack_url: video.soundtrack_data?.sound_url || null
        }));
        
        setUserVideos(formattedVideos);
      }
    } catch (error: any) {
      console.error("Error fetching user videos:", error);
      toast.error("Failed to load videos");
    } finally {
      setLoading(false);
    }
  };

  const handleVideoPlay = async (videoId: string) => {
    try {
      // Call the database function to increment views
      const { error } = await supabase.rpc('increment_video_views', {
        video_id_param: videoId
      });
      
      if (error) {
        console.error('Error incrementing video views:', error);
        return;
      }

      // Update local state immediately for better UX
      setUserVideos(prevVideos => 
        prevVideos.map(video => 
          video.id === videoId 
            ? { ...video, views: video.views + 1 }
            : video
        )
      );

      // Call the edge function to ensure the views table is updated
      try {
        const { error: edgeError } = await supabase.functions.invoke('update_video_likes', {
          body: { video_id_param: videoId }
        });
        
        if (edgeError) {
          console.error('Error calling edge function:', edgeError);
        }
      } catch (edgeError) {
        console.error('Edge function call failed:', edgeError);
      }
    } catch (error) {
      console.error('Error in handleVideoPlay:', error);
    }
  };

  const handleProfileUpdate = (profileData: any) => {
    setUserProfile({
      ...userProfile,
      ...profileData
    });
    setProfileUpdated(!profileUpdated);
  };

  const handleServicesUpdate = (services: ServiceItem[]) => {
    setUserProfile({
      ...userProfile,
      services
    });
    setProfileUpdated(!profileUpdated);
  };

  // Video click handler that handles both select mode and view tracking
  const handleVideoClick = async (videoId: string) => {
    if (selectMode) {
      toggleVideoSelection(videoId);
    } else {
      // Track the view when video is clicked
      await handleVideoPlay(videoId);
      // Return false to let VideoThumbnail handle the actual playback
      return false;
    }
  };

  const handleVideoDeleted = (deletedVideoId: string) => {
    console.log("Handling video deletion for ID:", deletedVideoId);
    setUserVideos(prevVideos => 
      prevVideos.filter(video => video.id !== deletedVideoId)
    );
  };

  const handleMultiDeleteSuccess = () => {
    console.log("Multi-delete successful");
    fetchUserVideos();
    setSelectedVideos([]);
    setSelectMode(false);
  };

  const toggleVideoSelection = (videoId: string) => {
    setSelectedVideos(prev => {
      if (prev.includes(videoId)) {
        return prev.filter(id => id !== videoId);
      } else {
        return [...prev, videoId];
      }
    });
  };

  const toggleSelectMode = () => {
    // When toggling select mode, make sure no videos are playing
    setPlayingVideoId(null);
    setSelectMode(!selectMode);
    
    if (selectMode) {
      setSelectedVideos([]);
    }
  };

  // Don't render ProfileHeader until we have the actual profile data loaded
  if (!profileDataLoaded) {
    return (
      <div className="pb-16 bg-background min-h-screen relative">
        <div className="flex justify-center items-center pt-20 relative z-10">
          <p>Loading profile...</p>
        </div>
        <MainNavigation />
      </div>
    );
  }

  return (
    <div className="pb-16 pt-safe bg-background min-h-screen relative">
      <div className="relative z-10">
        <ProfileHeader 
          username={userProfile.username}
          avatarUrl={userProfile.avatarUrl}
          bio={userProfile.bio}
          isCurrentUser={true}
          region={userProfile.region}
          badges={userProfile.badges}
          services={userProfile.services}
          website={userProfile.website}
          onProfileUpdate={handleProfileUpdate}
          onServicesUpdate={handleServicesUpdate}
        />
        
        {/* Whitetail Score Display */}
        <div className="px-4 mb-4">
          <div className="bg-muted/20 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-forest mb-1">Whitetail Score</h3>
                <WhitetailScore 
                  videoCount={whitetailScore.videoCount} 
                  size="medium" 
                  showCount={true} 
                />
              </div>
              <div className="text-right">
                <p className="text-xs text-muted-foreground mb-1">Next Tier</p>
                {whitetailScore.tier === 'Fawn' && (
                  <p className="text-xs text-forest font-medium">
                    {6 - whitetailScore.videoCount} videos to Spike
                  </p>
                )}
                {whitetailScore.tier === 'Spike' && (
                  <p className="text-xs text-forest font-medium">
                    {16 - whitetailScore.videoCount} videos to 8-Pointer
                  </p>
                )}
                {whitetailScore.tier === '8-Pointer' && (
                  <p className="text-xs text-forest font-medium">
                    {31 - whitetailScore.videoCount} videos to Trophy
                  </p>
                )}
                {whitetailScore.tier === 'Trophy' && (
                  <p className="text-xs text-purple-600 font-medium">
                    Max Tier Achieved! 🏆
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <Tabs defaultValue="videos" className="mt-6">
          <TabsList className="w-full">
            <TabsTrigger value="videos" onClick={() => setActiveTab("videos")} className="w-full">Videos</TabsTrigger>
          </TabsList>
          
          <TabsContent value="videos" className="p-4">
            {loading ? (
              <div className="flex justify-center p-4">
                <p>Loading videos...</p>
              </div>
            ) : userVideos.length > 0 ? (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={toggleSelectMode}
                    className="text-xs"
                  >
                    {selectMode ? "Cancel" : "Select Videos"}
                  </Button>
                  
                  {selectMode && (
                    <MultiDeleteButton 
                      videoIds={selectedVideos} 
                      userId={user?.id || ''} 
                      onDeleteSuccess={handleMultiDeleteSuccess}
                      disabled={selectedVideos.length === 0}
                    />
                  )}
                </div>
                
                <div className="grid grid-cols-3 gap-2 sm:gap-3 md:gap-4">
                  {userVideos.map(video => (
                    <div 
                      key={video.id} 
                      className="relative"
                    >
                      <VideoThumbnail
                        key={video.id}
                        videoId={video.id}
                        videoUrl={video.video_url}
                        soundtrackUrl={video.soundtrack_url}
                        size="small"
                        autoPlay={playingVideoId === video.id}
                        navigateOnClick={false}
                        onClick={selectMode ? () => toggleVideoSelection(video.id) : undefined}
                      >
                        {selectMode && (
                          <div className="absolute top-3 left-3 bg-black/60 rounded p-1 z-10">
                            <Checkbox 
                              checked={selectedVideos.includes(video.id)}
                              onCheckedChange={() => toggleVideoSelection(video.id)}
                              className="h-5 w-5"
                            />
                          </div>
                        )}
                        
                        {!selectMode && (
                          <div className="absolute top-3 right-3 pointer-events-auto z-10">
                            <DeleteVideoButton 
                              videoId={video.id} 
                              userId={user?.id || ''} 
                              onDeleteSuccess={() => handleVideoDeleted(video.id)}
                              variant="compact"
                            />
                          </div>
                        )}
                      </VideoThumbnail>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-8 text-center">
                <p className="text-muted-foreground mb-2">No videos yet</p>
                <Button 
                  className="bg-forest hover:bg-forest-dark"
                  onClick={() => navigate('/create')}
                >
                  Create your first video
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
        
        <MainNavigation />
        <InviteCodeManager />
      </div>
    </div>
  );
};

export default Profile;
