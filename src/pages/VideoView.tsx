
import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import VideoPlayer from "@/components/VideoPlayer";
import { ArrowLeft, Trash2 } from "lucide-react";
import VideoActions from "@/components/video/VideoActions";
import VideoInfo from "@/components/video/VideoInfo";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface VideoData {
  id: string;
  video_url: string;
  soundtrack_url?: string | null;
  likes: number;
  comments: number; // Added comments property to the interface
  user_id: string;
  username?: string;
  user_avatar?: string;
  caption?: string;
  game_type?: string;
  tags?: string[];
  created_at: string;
}

const VideoView = () => {
  const { videoId } = useParams();
  const navigate = useNavigate();
  const [videoData, setVideoData] = useState<VideoData | null>(null);
  const [loading, setLoading] = useState(true);
  const { user, profile } = useAuth(); // Get the current logged-in user and profile
  
  // Check if user is an admin
  const isAdmin = profile?.badges?.includes("admin") || false;
  
  useEffect(() => {
    const fetchVideoData = async () => {
      if (!videoId) return;
      
      try {
        setLoading(true);
        
        // Fetch video data with user information
        const { data, error } = await supabase
          .from('videos')
          .select(`
            id, 
            video_url,
            caption,
            likes,
            game_type,
            tags,
            user_id,
            username,
            user_avatar,
            created_at,
            soundtrack_data:sound_library(*),
            comments
          `)
          .eq('id', videoId)
          .single();
        
        if (error) {
          console.error("Error fetching video:", error);
          return;
        }
        
        if (data) {
          setVideoData({
            id: data.id,
            video_url: data.video_url,
            soundtrack_url: data.soundtrack_data?.sound_url || null,
            caption: data.caption || "",
            likes: data.likes || 0,
            comments: data.comments || 0,
            user_id: data.user_id,
            username: data.username,
            user_avatar: data.user_avatar,
            game_type: data.game_type,
            tags: data.tags,
            created_at: data.created_at
          });
        }
      } catch (error) {
        console.error("Error in fetchVideoData:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchVideoData();
  }, [videoId]);
  
  const goBack = () => {
    navigate(-1);
  };
  
  const handleDeleteVideo = async () => {
    if (!videoData) return;

    try {
      const { error } = await supabase
        .from('videos')
        .delete()
        .eq('id', videoData.id);
      
      if (error) {
        console.error("Error deleting video:", error);
        toast.error("Failed to delete video");
        return;
      }

      toast.success("Video deleted successfully");
      // Navigate back after deletion
      navigate("/");
    } catch (error) {
      console.error("Error in handleDeleteVideo:", error);
      toast.error("An unexpected error occurred");
    }
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-black">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }
  
  if (!videoData) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-black text-white">
        <p>Video not found</p>
        <button 
          onClick={goBack} 
          className="mt-4 bg-forest hover:bg-forest/90 text-white px-4 py-2 rounded-md"
        >
          Go Back
        </button>
      </div>
    );
  }
  
  // Check if the current user can delete this video (owner or admin)
  const canDelete = user && (user.id === videoData.user_id || isAdmin);
  
  return (
    <div className="fixed inset-0 bg-black flex flex-col">
      {/* Back button */}
      <button 
        onClick={goBack}
        className="absolute top-4 left-4 z-50 bg-black/50 text-white p-2 rounded-full"
      >
        <ArrowLeft size={24} />
      </button>
      
      {/* Admin or owner delete button */}
      {canDelete && (
        <button
          onClick={handleDeleteVideo}
          className="absolute top-4 right-4 z-50 bg-red-600 text-white p-2 rounded-full flex items-center justify-center hover:bg-red-700"
          title="Delete video"
        >
          <Trash2 size={20} />
        </button>
      )}
      
      {/* Video Player */}
      <div className="flex-1 flex items-center justify-center">
        <div className="h-full w-full max-w-md mx-auto">
          <VideoPlayer
            url={videoData.video_url}
            audioUrl={videoData.soundtrack_url}
            autoPlay={true}
            muted={false}
            enableAudioControl={true}
          />
        </div>
      </div>
      
      {/* Video Info and Actions */}
      <div className="w-full max-w-md mx-auto p-4 bg-black/50">
        {videoData.username && (
          <VideoInfo 
            username={videoData.username || "Anonymous"}
            userAvatar={videoData.user_avatar || "https://images.unsplash.com/photo-1599566150163-29194dcaad36"}
            userId={videoData.user_id}
            gameType={videoData.game_type}
            tags={videoData.tags || []}
            caption={videoData.caption || ""}
            soundtrack={"Original Sound"}
          />
        )}
        
        <VideoActions
          id={videoData.id}
          likes={videoData.likes}
          comments={videoData.comments || 0}
          shares={0}
          userId={videoData.user_id}
          username={videoData.username || "Anonymous"}
          videoUrl={videoData.video_url}
          caption={videoData.caption || ""}
          userAvatar={videoData.user_avatar || "https://images.unsplash.com/photo-1599566150163-29194dcaad36"}
          isAdmin={isAdmin} // Pass admin status
        />
      </div>
    </div>
  );
};

export default VideoView;
