
import { useState, useEffect } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import FilterBar from "@/components/FilterBar";
import MainNavigation from "@/components/MainNavigation";
import { useSearchParams } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import VideoThumbnail from "@/components/VideoThumbnail";
import { Card } from "@/components/ui/card";

interface DiscoverItem {
  id: string;
  thumbnail: string;
  likes: number;
  gameType?: string;
  region?: string;
  tags?: string[];
  soundtrack?: string;
}

const Discover = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState("");
  const [discoverItems, setDiscoverItems] = useState<DiscoverItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<DiscoverItem[]>([]);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Fetch videos from Supabase
  useEffect(() => {
    const fetchVideos = async () => {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('videos')
          .select('id, video_url, game_type, tags, likes, soundtrack_data:sound_library(*)')
          .order('created_at', { ascending: false });
        
        if (error) {
          console.error("Error fetching videos:", error);
          return;
        }
        
        if (data) {
          console.log("Fetched videos with soundtrack:", data);
          const formattedData = data.map(video => ({
            id: video.id,
            thumbnail: video.video_url,
            likes: video.likes || 0,
            gameType: video.game_type,
            tags: video.tags ? video.tags.map(tag => tag.toLowerCase()) : [], // Normalize tags to lowercase
            soundtrack: video.soundtrack_data?.sound_url || null
          }));
          
          console.log("Formatted discover items:", formattedData);
          setDiscoverItems(formattedData);
          setFilteredItems(formattedData);
        }
      } catch (error) {
        console.error("Error in fetchVideos:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchVideos();
  }, []);

  // Initialize from URL parameters
  useEffect(() => {
    const tag = searchParams.get('tag');
    const interest = searchParams.get('interest'); // Changed from gameType to interest
    const query = searchParams.get('q');
    
    // Set search query if present in URL
    if (query) {
      setSearchQuery(query);
    }
    
    // Set filters based on URL parameters
    const filters: string[] = [];
    if (tag) filters.push(tag.toLowerCase());
    if (interest) filters.push(interest.toLowerCase()); // Changed from gameType to interest
    
    if (filters.length > 0) {
      console.log("Setting active filters from URL:", filters);
      setActiveFilters(filters);
      handleFilterChange(filters);
    }
  }, [searchParams, discoverItems]);
  
  const handleFilterChange = (selectedFilters: string[]) => {
    setActiveFilters(selectedFilters);
    
    if (selectedFilters.length === 0) {
      setFilteredItems(discoverItems);
      return;
    }
    
    console.log("Filtering with selected filters:", selectedFilters);
    console.log("All items to filter:", discoverItems);
    
    // Convert all filters to lowercase for case-insensitive comparison
    const lowercaseFilters = selectedFilters.map(filter => filter.toLowerCase());
    
    const filtered = discoverItems.filter(item => {
      // Check if item matches any selected filter
      return lowercaseFilters.some(filter => {
        // Check game type (which maps to interest)
        if (item.gameType && item.gameType.toLowerCase() === filter) {
          console.log(`Match found for game type ${item.gameType} with filter ${filter}`);
          return true;
        }
        
        // Check region
        if (item.region && item.region.toLowerCase() === filter) {
          console.log(`Match found for region ${item.region} with filter ${filter}`);
          return true;
        }
        
        // Check tags - tags are already normalized to lowercase in fetchVideos
        if (item.tags && item.tags.length > 0) {
          const tagMatch = item.tags.some(tag => tag === filter);
          if (tagMatch) {
            console.log(`Match found for tag in [${item.tags.join(', ')}] with filter ${filter}`);
            return true;
          }
        }
        
        return false;
      });
    });
    
    console.log("Filtered items:", filtered);
    setFilteredItems(filtered);
    
    // Update URL with selected filters
    const newParams = new URLSearchParams(searchParams);
    
    // Remove existing filter parameters
    newParams.delete('tag');
    newParams.delete('interest'); // Changed from gameType to interest
    
    // Add new filter parameters if they exist
    if (selectedFilters.length === 1) {
      // Try to determine if it's an interest or other tag
      const filter = selectedFilters[0].toLowerCase();
      const isInterest = ["velvet bucks", "bow hunting", "gun hunting", "shed hunting", "food plots", "trail cam videos"].includes(filter);
      
      if (isInterest) {
        newParams.set('interest', filter); // Changed from gameType to interest
      } else {
        newParams.set('tag', filter);
      }
    }
    
    setSearchParams(newParams);
  };
  
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    
    // Update URL with search query
    const newParams = new URLSearchParams(searchParams);
    if (query) {
      newParams.set('q', query);
    } else {
      newParams.delete('q');
    }
    setSearchParams(newParams);
    
    // Filter items based on search query
    if (query) {
      const searchResults = discoverItems.filter(item => 
        // Search in game type
        (item.gameType && item.gameType.toLowerCase().includes(query.toLowerCase())) ||
        // Search in tags
        (item.tags && item.tags.some(tag => 
          tag.includes(query.toLowerCase())
        ))
      );
      setFilteredItems(searchResults);
    } else {
      // If no search query, apply only the active filters
      handleFilterChange(activeFilters);
    }
  };

  return (
    <div className="pb-16 min-h-screen bg-background pt-safe">
      <div className="p-4 space-y-4">
        <h1 className="text-2xl font-bold">Discover</h1>
        
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={18} />
          <Input
            className="pl-10 bg-muted"
            placeholder="Search videos, users, or tags"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
        
        <FilterBar onFilterChange={handleFilterChange} activeFilters={activeFilters} />
        
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <p className="text-muted-foreground">Loading videos...</p>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="flex justify-center items-center h-40">
            <p className="text-muted-foreground">No videos found matching your criteria</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-2">
            {filteredItems.map(item => (
              <Card key={item.id} className="overflow-hidden">
                <VideoThumbnail
                  videoId={item.id}
                  videoUrl={item.thumbnail}
                  soundtrackUrl={item.soundtrack}
                  count={item.likes}
                  countLabel=" likes"
                  navigateOnClick={false} // Set to false to use fullscreen expansion instead
                >
                  {item.tags && item.tags.length > 0 && (
                    <div className="absolute top-2 left-2 flex flex-wrap gap-1">
                      {item.tags.slice(0, 2).map((tag, index) => (
                        <span key={index} className="bg-black/50 text-white text-xs px-1.5 py-0.5 rounded-full">
                          #{tag}
                        </span>
                      ))}
                    </div>
                  )}
                </VideoThumbnail>
              </Card>
            ))}
          </div>
        )}
      </div>
      
      <MainNavigation />
    </div>
  );
};

export default Discover;
