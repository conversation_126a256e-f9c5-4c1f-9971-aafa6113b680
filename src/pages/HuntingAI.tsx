


import React, { useState, useRef, useEffect } from 'react';
import { ArrowLeft, Send, Mic, MicOff, Target, Loader2, Bo<PERSON>, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import VoiceChat from '@/components/VoiceChat';
import { useIsMobile } from "@/hooks/use-mobile";

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

const HuntingAI = () => {
  const isMobile = useIsMobile();

  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputValue.trim();
    if (!textToSend || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: textToSend,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    
    // Clear input only if using the text input
    if (!messageText) {
      setInputValue('');
    }
    
    setIsLoading(true);

    try {
      console.log('Sending message to hunting AI:', userMessage.content);
      
      const { data, error } = await supabase.functions.invoke('hunting-ai-chat', {
        body: { message: userMessage.content }
      });

      if (error) {
        console.error('Supabase function error:', error);
        throw new Error(error.message || 'Failed to get AI response');
      }

      if (data?.error) {
        console.error('AI function returned error:', data.error);
        throw new Error(data.error);
      }

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: data.response || 'I apologize, but I encountered an issue processing your request. Please try again.',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);
      
    } catch (error) {
      console.error('Error getting AI response:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'I apologize, but I encountered an error processing your request. Please check your connection and try again.',
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, errorMessage]);
      
      toast.error('Failed to get AI response. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleVoiceTextReceived = (text: string) => {
    // Send the transcribed text as a message
    handleSendMessage(text);
    // Close voice mode after sending
    setIsVoiceMode(false);
  };

  const toggleVoiceMode = () => {
    setIsVoiceMode(!isVoiceMode);
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Enhanced Header */}
      <div className={`border-b border-border/30 bg-background/95 backdrop-blur-sm fixed top-0 z-10 pt-safe ${isMobile ? 'w-full': ''}`}>
        <div className="flex items-center justify-between px-3 py-2 md:px-4 md:py-3">
          <div className="flex items-center space-x-2 md:space-x-3">
            <Link to="/">
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <ArrowLeft className="w-4 h-4" />
              </Button>
            </Link>
            <div className="flex items-center space-x-2 md:space-x-3">
              <div className="w-10 h-10 md:w-12 md:h-12 rounded-xl flex items-center justify-center shadow-lg">
                <img 
                  src="/lovable-uploads/c23f8b23-dc21-4346-8188-80ed45925c47.png" 
                  alt="Hunting AI" 
                  className="w-full h-full object-contain"
                />
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <h1 className="text-base md:text-lg font-bold text-foreground">Whitetail Livn AI</h1>
                  <div className="flex items-center space-x-1 px-2 py-0.5 bg-gradient-to-r from-emerald-500 to-forest-light rounded-full">
                    <Zap className="w-3 h-3 text-white" />
                    <span className="text-xs text-white font-bold">AI</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground hidden md:block">Advanced whitetail hunting intelligence</p>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 px-2 md:px-3 py-1 md:py-1.5 bg-green-50 rounded-full">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-700 font-medium">AI Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Container */}
      <div className={`flex-1 flex flex-col ${isMobile ? 'pt-16': ''}`}>
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-2 md:p-3 space-y-3 md:space-y-4">
          {messages.length === 0 ? (
            <div className="flex-1 flex items-center justify-center min-h-[60vh] px-4">
              <div className="text-center max-w-md">
                <div className="w-20 h-20 md:w-24 md:h-24 rounded-3xl mx-auto mb-4 md:mb-6 flex items-center justify-center shadow-xl">
                  <img 
                    src="/lovable-uploads/c23f8b23-dc21-4346-8188-80ed45925c47.png" 
                    alt="Hunting AI" 
                    className="w-full h-full object-contain"
                  />
                </div>
                <h2 className="text-xl md:text-2xl font-bold mb-2 md:mb-3 text-foreground">Whitetail Livn AI</h2>
                <div className="flex items-center justify-center space-x-2 mb-3 md:mb-4">
                  <span className="text-sm text-muted-foreground">Powered by</span>
                  <div className="flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-emerald-500 to-forest-light rounded-full">
                    <Zap className="w-3 h-3 text-white" />
                    <span className="text-xs text-white font-bold">Advanced AI</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground leading-relaxed mb-4 md:mb-6">
                  If it's about whitetails, our AI's got you covered. From chasing the rut to picking the right gear and knowing exact season dates — just ask and get dialed.
                </p>
              </div>
            </div>
          ) : (
            <>
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} animate-fade-in`}
                >
                  <div className={`flex max-w-[85%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'} gap-2 md:gap-3`}>
                    {/* Enhanced Avatar */}
                    <div className={`w-10 h-10 md:w-12 md:h-12 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md ${
                      message.type === 'user' 
                        ? 'bg-gradient-to-br from-forest to-forest-light text-white' 
                        : 'text-white'
                    }`}>
                      {message.type === 'user' ? (
                        <span className="text-sm font-bold">U</span>
                      ) : (
                        <img 
                          src="/lovable-uploads/c23f8b23-dc21-4346-8188-80ed45925c47.png" 
                          alt="Hunting AI" 
                          className="w-full h-full object-contain"
                        />
                      )}
                    </div>
                    
                    {/* Enhanced Message Content */}
                    <Card className={`p-3 md:p-4 border-0 shadow-lg ${
                      message.type === 'user' 
                        ? 'bg-gradient-to-br from-forest to-forest-light text-white' 
                        : 'bg-white dark:bg-card border border-border/50'
                    }`}>
                      <div className={`text-sm leading-relaxed whitespace-pre-wrap ${
                        message.type === 'user' ? 'text-white' : 'text-foreground'
                      }`}>
                        {message.content}
                      </div>
                      <div className={`text-xs mt-2 md:mt-3 flex items-center justify-between ${
                        message.type === 'user' ? 'text-white/70' : 'text-muted-foreground'
                      }`}>
                        <span>{message.timestamp.toLocaleTimeString()}</span>
                        {message.type === 'assistant' && (
                          <div className="flex items-center space-x-1">
                            <Zap className="w-3 h-3" />
                            <span className="text-xs font-medium">AI</span>
                          </div>
                        )}
                      </div>
                    </Card>
                  </div>
                </div>
              ))}
              
              {/* Enhanced Loading indicator */}
              {isLoading && (
                <div className="flex justify-start animate-fade-in">
                  <div className="flex gap-2 md:gap-3 max-w-[85%]">
                    <div className="w-10 h-10 md:w-12 md:h-12 rounded-xl flex items-center justify-center shadow-md">
                      <img 
                        src="/lovable-uploads/c23f8b23-dc21-4346-8188-80ed45925c47.png" 
                        alt="Hunting AI" 
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <Card className="p-3 md:p-4 bg-white dark:bg-card border border-border/50 shadow-lg">
                      <div className="flex items-center gap-3">
                        <Loader2 className="w-5 h-5 animate-spin text-forest" />
                        <span className="text-sm text-muted-foreground">Hunting results...</span>
                        <div className="flex items-center space-x-1">
                          <Zap className="w-3 h-3 text-emerald-500" />
                          <span className="text-xs font-medium text-emerald-500">Advanced AI</span>
                        </div>
                      </div>
                    </Card>
                  </div>
                </div>
              )}
            </>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Voice Chat Overlay */}
        {isVoiceMode && (
          <div className="absolute inset-0 bg-background/95 backdrop-blur-xl z-20 flex items-center justify-center p-4">
            <VoiceChat 
              isOpen={isVoiceMode} 
              onClose={() => setIsVoiceMode(false)}
              onTextReceived={handleVoiceTextReceived}
            />
          </div>
        )}

        {/* Enhanced Input Area with more prominent styling */}
        <div className="border-t border-border/30 bg-gradient-to-r from-forest/5 via-forest-light/5 to-emerald-500/5 p-3 md:p-4">
          <div className="flex items-end gap-3 md:gap-4 p-3 md:p-4 bg-white dark:bg-card rounded-2xl border-2 border-forest/20 shadow-xl shadow-forest/10 hover:border-forest/30 transition-all duration-300">
            <div className="flex-1">
              <Textarea
                ref={textareaRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Got a whitetail question? Type it or use speak-to-text"
                className="min-h-[44px] md:min-h-[48px] max-h-28 resize-none border-0 bg-transparent text-sm placeholder:text-muted-foreground focus-visible:ring-0 focus-visible:ring-offset-0 p-0"
                disabled={isLoading}
              />
            </div>
            
            {/* Enhanced Action Buttons */}
            <div className="flex items-center space-x-3">
              <Button
                onClick={toggleVoiceMode}
                variant={isVoiceMode ? "default" : "ghost"}
                size="icon"
                className={`h-10 w-10 md:h-12 md:w-12 rounded-xl shadow-lg transition-all duration-300 ${
                  isVoiceMode 
                    ? "bg-gradient-to-r from-forest to-forest-light text-white hover:from-forest/90 hover:to-forest-light/90" 
                    : "hover:bg-forest/10 hover:text-forest border border-forest/20"
                }`}
              >
                {isVoiceMode ? <MicOff className="w-4 h-4 md:w-5 md:h-5" /> : <Mic className="w-4 h-4 md:w-5 md:h-5" />}
              </Button>
              
              <Button
                onClick={() => handleSendMessage()}
                disabled={!inputValue.trim() || isLoading}
                size="icon"
                className="h-10 w-10 md:h-12 md:w-12 rounded-xl bg-gradient-to-r from-forest to-forest-light text-white disabled:opacity-50 shadow-lg hover:shadow-xl hover:from-forest/90 hover:to-forest-light/90 transition-all duration-300"
              >
                <Send className="w-4 h-4 md:w-5 md:h-5" />
              </Button>
            </div>
          </div>
          
          <div className="text-center mt-3">
            <span className="text-xs text-muted-foreground">
              Press Enter to send • Use voice input for hands-free interaction • Powered by Whitetail Livn AI
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HuntingAI;


