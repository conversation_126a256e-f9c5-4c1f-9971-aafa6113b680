import React, { useEffect } from "react";
import InboxComponent from "@/components/inbox/Inbox";
import { useLocation } from "react-router-dom";

const Inbox = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const newMessageUserId = searchParams.get("newMessage");

  return (
    <div className="h-screen pb-16 overflow-hidden pt-safe">
      <InboxComponent initialMessageUserId={newMessageUserId} />
    </div>
  );
};

export default Inbox;
