
import { useRef, useState, useEffect, useCallback } from "react";
import Header from "@/components/Header";
import VideoCard from "@/components/VideoCard";
import MainNavigation from "@/components/MainNavigation";
import { supabase } from "@/integrations/supabase/client";
import { Routes, Route, useLocation } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { toast } from "sonner";
import { useIsMobile } from "@/hooks/use-mobile";
import VideoSkeleton from "@/components/VideoSkeleton";

const Index = () => {
  const feedRef = useRef<HTMLDivElement>(null);
  const [videos, setVideos] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [visibleVideoIndices, setVisibleVideoIndices] = useState<number[]>([0, 1]);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);
  const location = useLocation();
  const { toast: useToastHook } = useToast();
  const isMobile = useIsMobile();
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isScrolling, setIsScrolling] = useState(false);
  const [forceAutoPlay, setForceAutoPlay] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const scrollTimeoutRef = useRef<number | null>(null);
  const loadingRef = useRef(false);

  const VIDEOS_PER_PAGE = 12;

  // Add home page class to body
  useEffect(() => {
    document.body.classList.add('home-page');
    
    return () => {
      document.body.classList.remove('home-page');
    };
  }, []);

  // Force auto-play when navigating to home feed
  useEffect(() => {
    if (location.pathname === '/') {
      console.log('Navigated to home feed, forcing auto-play');
      setForceAutoPlay(true);
      
      // Reset force auto-play after a short delay
      const timer = setTimeout(() => {
        setForceAutoPlay(false);
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [location.pathname]);

  // Function to pause all videos and audio
  const pauseAllMedia = useCallback(() => {
    const videos = document.querySelectorAll('video');
    const audios = document.querySelectorAll('audio');
    
    videos.forEach(video => {
      video.pause();
      video.muted = true;
    });
    
    audios.forEach(audio => {
      audio.pause();
      audio.muted = true;
    });
  }, []);

  // Enhanced video fetching with pagination
  const fetchVideos = useCallback(async (offsetValue = 0, append = false) => {
    if (loadingRef.current) return;
    
    try {
      loadingRef.current = true;
      if (!append) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }
      setError(null);
      
      const { data, error } = await supabase
        .from('videos')
        .select('*, sound_library(*)')
        .order('created_at', { ascending: false })
        .range(offsetValue, offsetValue + VIDEOS_PER_PAGE - 1);
      
      if (error) {
        throw error;
      }
      
      if (data) {
        console.log(`Fetched ${data.length} videos with offset ${offsetValue}`);
        
        // Check if we have more videos to load
        setHasMore(data.length === VIDEOS_PER_PAGE);
        
        if (append) {
          setVideos(prevVideos => [...prevVideos, ...data]);
        } else {
          setVideos(data);
          
          // Prefetch first few videos' thumbnails to improve initial load time
          if (data.length > 0) {
            data.slice(0, 3).forEach(video => {
              if (video.video_url) {
                const img = new Image();
                img.src = video.video_url;
              }
            });
          }
        }
        
        setOffset(offsetValue + data.length);
      }
    } catch (error: any) {
      console.error("Error fetching videos:", error);
      setError(error.message || "Failed to load videos");
      if (!append) {
        toast.error("Could not load videos. Please try again.");
      } else {
        toast.error("Could not load more videos.");
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setIsInitialLoad(false);
      loadingRef.current = false;
    }
  }, []);

  // Load more videos when user scrolls near the bottom
  const loadMoreVideos = useCallback(() => {
    if (!hasMore || loadingRef.current) return;
    
    console.log('Loading more videos, current offset:', offset);
    fetchVideos(offset, true);
  }, [offset, hasMore, fetchVideos]);

  // Update video user avatar when profile changes
  const updateVideoUserAvatar = useCallback((userId: string, newAvatarUrl: string) => {
    setVideos(prevVideos => 
      prevVideos.map(video => 
        video.user_id === userId 
          ? { ...video, user_avatar: newAvatarUrl }
          : video
      )
    );
  }, []);

  // Load videos and set up subscriptions
  useEffect(() => {
    fetchVideos(0, false);

    // Set up real-time subscription for videos changes
    const videosChannel = supabase
      .channel('videos-changes')
      .on('postgres_changes', {
        event: 'DELETE',
        schema: 'public',
        table: 'videos'
      }, (payload) => {
        console.log('Video deleted in feed:', payload);
        if (payload.old?.id) {
          setVideos(prevVideos => 
            prevVideos.filter(video => video.id !== payload.old.id)
          );
          toast.info("A video has been deleted");
        }
      })
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'videos'
      }, (payload) => {
        console.log('New video added:', payload);
        // Reset and refetch from beginning to include new video
        setOffset(0);
        setHasMore(true);
        fetchVideos(0, false);
      })
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'videos'
      }, (payload) => {
        console.log('Video updated:', payload);
        // For updates, we could be more efficient and just update the specific video
        fetchVideos(0, false);
      })
      .subscribe();

    // Set up real-time subscription for profile changes
    const profilesChannel = supabase
      .channel('profiles-changes')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'profiles'
      }, (payload) => {
        console.log('Profile updated:', payload);
        if (payload.new?.id && payload.new?.avatar_url) {
          updateVideoUserAvatar(payload.new.id, payload.new.avatar_url);
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(videosChannel);
      supabase.removeChannel(profilesChannel);
    };
  }, [fetchVideos, updateVideoUserAvatar]);

  // Restore scroll position from session storage
  useEffect(() => {
    if (location.pathname === '/' && sessionStorage.getItem('currentVideoId')) {
      const videoId = sessionStorage.getItem('currentVideoId');
      const videoIndex = videos.findIndex(video => video.id === videoId);
      
      if (videoIndex !== -1 && feedRef.current) {
        const videoHeight = window.innerHeight;
        feedRef.current.scrollTo({ 
          top: videoIndex * videoHeight,
          behavior: 'auto'
        });
      }
      
      sessionStorage.removeItem('currentVideoId');
    }
  }, [location.pathname, videos]);
  
  // Enhanced visible video tracking with infinite scroll detection
  useEffect(() => {
    if (!feedRef.current) return;
    
    let timeoutId: number;
    
    const handleScroll = () => {
      // Indicate that scrolling is happening
      setIsScrolling(true);
      
      // Clear previous timeout to debounce the scroll events
      if (timeoutId) {
        window.clearTimeout(timeoutId);
      }
      
      if (scrollTimeoutRef.current) {
        window.clearTimeout(scrollTimeoutRef.current);
      }
      
      // Use timeout to reduce calculations on rapid scrolling
      timeoutId = window.setTimeout(() => {
        if (!feedRef.current) return;
        
        const scrollTop = feedRef.current.scrollTop;
        const scrollHeight = feedRef.current.scrollHeight;
        const clientHeight = feedRef.current.clientHeight;
        const videoHeight = window.innerHeight;
        
        // Calculate which videos are currently visible or about to be visible
        const currentIndex = Math.floor(scrollTop / videoHeight);
        
        // Expand the range of preloaded videos
        const visibleIndices = [
          Math.max(0, currentIndex - 1), // Previous video
          currentIndex, // Current video
          Math.min(videos.length - 1, currentIndex + 1), // Next video
          Math.min(videos.length - 1, currentIndex + 2), // Video after next (preload)
          Math.min(videos.length - 1, currentIndex + 3), // One more for smoother scrolling
        ].filter((idx) => idx >= 0 && idx < videos.length);
        
        setVisibleVideoIndices(visibleIndices);
        
        // Check if we need to load more videos (when user is near bottom)
        const scrolledPercentage = (scrollTop + clientHeight) / scrollHeight;
        if (scrolledPercentage > 0.8 && hasMore && !loadingRef.current) {
          console.log('Near bottom, loading more videos');
          loadMoreVideos();
        }
        
        // Save current video index to session storage for state persistence
        if (videos[currentIndex]?.id) {
          sessionStorage.setItem('currentVideoIndex', currentIndex.toString());
          sessionStorage.setItem('currentVideoId', videos[currentIndex].id);
        }
        
        // Set scrolling status to false after a delay 
        scrollTimeoutRef.current = window.setTimeout(() => {
          setIsScrolling(false);
        }, 150);
        
      }, 80); // Reduce debounce time for more responsive updates
    };
    
    feedRef.current.addEventListener('scroll', handleScroll);
    handleScroll(); // Call once to set initial state
    
    return () => {
      if (timeoutId) {
        window.clearTimeout(timeoutId);
      }
      
      if (scrollTimeoutRef.current) {
        window.clearTimeout(scrollTimeoutRef.current);
      }
      
      feedRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, [videos.length, hasMore, loadMoreVideos]);

  const handleRefresh = () => {
    // Start refreshing state to mute all media
    setIsRefreshing(true);
    
    // Immediately pause and mute all media
    pauseAllMedia();
    
    // Scroll to top smoothly
    feedRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
    
    // Reset states
    setOffset(0);
    setHasMore(true);
    setForceAutoPlay(true); // Force auto-play on refresh
    
    // Clear session storage
    sessionStorage.removeItem('currentVideoIndex');
    sessionStorage.removeItem('currentVideoId');
    
    // Fetch fresh videos
    fetchVideos(0, false);
    
    toast.success("Refreshing feed...", {
      duration: 1000
    });
    
    // Reset states after refresh completes
    setTimeout(() => {
      setIsRefreshing(false);
      setForceAutoPlay(false);
    }, 1500);
  };

  const handleRetry = () => {
    setError(null);
    setOffset(0);
    setHasMore(true);
    fetchVideos(0, false);
  };

  const handleVideoDeleted = (deletedVideoId: string) => {
    console.log('Video deleted from feed UI:', deletedVideoId);
    setVideos(prevVideos => 
      prevVideos.filter(video => video.id !== deletedVideoId)
    );
  };

  // Calculate viewport height for better mobile fit
  useEffect(() => {
    const setAppHeight = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };
    
    setAppHeight();
    window.addEventListener('resize', setAppHeight);
    
    return () => window.removeEventListener('resize', setAppHeight);
  }, []);

  return (
    <div className="bg-black pt-safe min-h-screen relative">
      <Header />
      
      <div 
        ref={feedRef} 
        className="h-[calc(100vh-56px)] mt-[calc(var(--safe-area-inset-top))] overflow-y-scroll snap-y snap-mandatory pb-14 no-scrollbar overscroll-y-contain"
        style={{ height: 'calc(var(--vh, 1vh) * 100 - 56px)'}}
      >
        {loading && isInitialLoad ? (
          Array.from({ length: 3 }).map((_, index) => (
            <VideoSkeleton key={`skeleton-${index}`} />
          ))
        ) : error ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-white text-center px-4">
              <p className="mb-4">Failed to load videos</p>
              <p className="mb-4 text-sm opacity-70">{error}</p>
              <button 
                onClick={handleRetry}
                className="px-4 py-2 bg-forest rounded-md hover:bg-forest-light transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : videos.length === 0 ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-white text-center px-4">
              <p className="mb-4">No videos yet!</p>
              <p>Create your first post by clicking the + button below</p>
            </div>
          </div>
        ) : (
          <>
            {videos.map((video, index) => {
              // Determine loading strategy based on visibility
              const isVisible = visibleVideoIndices.includes(index);
              const isPriority = index <= 1 || isVisible; // First two videos and visible videos are high priority
              
              // Choose preload strategy based on visibility and priority
              const preloadStrategy = isPriority ? "auto" : isVisible ? "metadata" : "none";
              
              return (
                <VideoCard 
                  key={video.id} 
                  id={video.id}
                  username={video.username}
                  userAvatar={video.user_avatar}
                  videoUrl={video.video_url}
                  caption={video.caption}
                  soundtrack={video.soundtrack || "Original Sound"}
                  likes={video.likes || 0}
                  comments={video.comments || 0}
                  shares={video.shares || 0}
                  tags={video.tags || []}
                  gameType={video.game_type}
                  userId={video.user_id}
                  onDelete={handleVideoDeleted}
                  soundtrack_data={video.soundtrack_data || (video.sound_library ? {
                    title: video.sound_library.title,
                    url: video.sound_library.sound_url
                  } : null)}
                  preload={preloadStrategy}
                  priority={isPriority}
                  isScrolling={isScrolling}
                  forceAutoPlay={forceAutoPlay && !isRefreshing}
                  color_filter={video.color_filter}
                />
              );
            })}
            
            {/* Loading indicator for infinite scroll */}
            {loadingMore && (
              <div className="flex justify-center items-center h-screen bg-black">
                <VideoSkeleton />
              </div>
            )}
            
            {/* End of feed indicator */}
            {!hasMore && videos.length > 0 && (
              <div className="flex justify-center items-center h-32 bg-black text-white text-center">
                <div>
                  <p className="mb-2">You've reached the end!</p>
                  <p className="text-sm opacity-70">Pull down to refresh for new videos</p>
                </div>
              </div>
            )}
          </>
        )}
      </div>
      
      <MainNavigation onHomeClick={handleRefresh} />
    </div>
  );
};

export default Index;
