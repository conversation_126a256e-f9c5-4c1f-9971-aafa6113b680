import { useState, useRef, useEffect } from "react";
import { ArrowLeft, X, Upload, Crop, Scissors } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";
import { useAuth } from "@/contexts/AuthContext";
import VideoEditor from "@/components/VideoEditor";
import VideoPlayer from "@/components/VideoPlayer";
import TagInput from "@/components/TagInput";
import SoundSelector from "@/components/SoundSelector";
import ColorFilterSelector, { ColorFilter } from "@/components/ColorFilterSelector";
import MobileScrollArea from "@/components/ui/mobile-scroll-area";

interface SoundItem {
  id: string;
  title: string;
  description: string | null;
  sound_url: string;
  tags: string[];
  category?: string;
}

const Create = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoPreview, setVideoPreview] = useState<string | null>(null);
  const [editingVideo, setEditingVideo] = useState(false);
  const [caption, setCaption] = useState("");
  const [tags, setTags] = useState("");
  const [uploading, setUploading] = useState(false);
  const [selectedSound, setSelectedSound] = useState<SoundItem | null>(null);
  const [selectedColorFilter, setSelectedColorFilter] = useState<ColorFilter | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  // Track the current editor settings for persistence
  const [editorSettings, setEditorSettings] = useState({
    zoomLevel: 1,
    cropX: 0,
    aspectRatio: 9/16
  });
  
  // Check authentication on load
  useEffect(() => {
    if (!user) {
      toast.error("You must be logged in to upload videos");
      navigate("/auth", { state: { returnTo: "/create" } });
    }
  }, [user, navigate]);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    if (!file.type.includes('video/')) {
      toast.error("Please select a video file.");
      return;
    }
    
    // Validate file size (max 100MB)
    const MAX_SIZE_MB = 100;
    const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;
    
    if (file.size > MAX_SIZE_BYTES) {
      toast.error(`File is too large. Maximum size is ${MAX_SIZE_MB}MB.`);
      return;
    }
    
    setVideoFile(file);
    const videoUrl = URL.createObjectURL(file);
    setVideoPreview(videoUrl);
  };

  const handleSelectFileClick = () => {
    fileInputRef.current?.click();
  };
  
  const handleClearVideo = () => {
    if (videoPreview) {
      URL.revokeObjectURL(videoPreview);
    }
    setVideoFile(null);
    setVideoPreview(null);
    setEditingVideo(false);
    setSelectedSound(null);
    setSelectedColorFilter(null);
    // Reset editor settings when clearing video
    setEditorSettings({
      zoomLevel: 1,
      cropX: 0,
      aspectRatio: 9/16
    });
  };
  
  const handleSaveEditedVideo = (editedBlob: Blob) => {
    if (videoPreview) {
      URL.revokeObjectURL(videoPreview);
    }
    
    const editedFile = new File([editedBlob], videoFile?.name || "edited-video.mp4", { 
      type: editedBlob.type 
    });
    
    setVideoFile(editedFile);
    const newVideoUrl = URL.createObjectURL(editedBlob);
    setVideoPreview(newVideoUrl);
    setEditingVideo(false);
    
    toast.success("Video edited successfully!");
  };
  
  const handleEditVideo = () => {
    setEditingVideo(true);
  };
  
  const handleCancelEdit = () => {
    setEditingVideo(false);
  };
  
  const validateInput = () => {
    if (!videoFile) {
      toast.error("Please select a video to upload.");
      return false;
    }

    if (!user) {
      toast.error("You must be signed in to post a video.");
      navigate('/auth', { state: { returnTo: '/create' } });
      return false;
    }

    if (!caption.trim()) {
      toast.error("Please add a caption for your video.");
      return false;
    }
    
    return true;
  };
  
  const handlePost = async () => {
    if (!validateInput()) return;

    try {
      setUploading(true);
      
      const fileExt = videoFile.name.split('.').pop();
      const fileName = `${uuidv4()}.${fileExt}`;
      const filePath = `videos/${fileName}`;
      
      const { error: uploadError } = await supabase.storage
        .from('videos')
        .upload(filePath, videoFile);
        
      if (uploadError) {
        throw uploadError;
      }
      
      const { data } = supabase.storage
        .from('videos')
        .getPublicUrl(filePath);
      
      const videoUrl = data.publicUrl;
      
      const tagArray = tags.split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);
      
      // Update game types list to include new tags
      const gameTypes = ["Velvet Bucks", "Bow Hunting", "Gun Hunting", "Shed Hunting", "Food Plots", "Trail Cam Videos", "The Rut", "Big Bucks", "Harvest Shot"];
      let gameType = null;
      
      for (const tag of tagArray) {
        if (gameTypes.includes(tag)) {
          gameType = tag;
          break;
        }
      }
      
      const userData = {
        userId: user.id,
        username: profile?.username || user.email?.split('@')[0] || 'anonymous',
        userAvatar: profile?.avatar_url || "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80"
      };
      
      const insertData: any = {
        user_id: userData.userId,
        username: userData.username,
        user_avatar: userData.userAvatar,
        video_url: videoUrl,
        caption: caption,
        tags: tagArray,
        game_type: gameType,
        soundtrack: selectedSound ? selectedSound.title : "Original Sound"
      };
      
      // Add color filter data if selected (ensure it's properly formatted)
      if (selectedColorFilter && selectedColorFilter.id !== "none") {
        insertData.color_filter = {
          id: selectedColorFilter.id,
          name: selectedColorFilter.name,
          cssFilter: selectedColorFilter.cssFilter
        };
      } else {
        // Explicitly set to null if no filter or "none" filter is selected
        insertData.color_filter = null;
      }
      
      // Add sound library reference if selected
      if (selectedSound) {
        insertData.sound_library_id = selectedSound.id;
        insertData.soundtrack_data = { 
          title: selectedSound.title,
          url: selectedSound.sound_url,
          category: selectedSound.category || null
        };
      }
      
      console.log('Inserting video data:', insertData);
      
      const { error: insertError } = await supabase
        .from('videos')
        .insert(insertData);
      
      if (insertError) {
        console.error('Insert error:', insertError);
        throw insertError;
      }
      
      toast.success("Video uploaded successfully!");
      
      // Navigate to discover page with the first tag filter applied
      if (tagArray.length > 0) {
        navigate(`/discover?tag=${encodeURIComponent(tagArray[0].toLowerCase())}`);
      } else {
        navigate('/profile');
      }
      
    } catch (error) {
      console.error("Error uploading:", error);
      toast.error("Failed to upload video. Please try again.");
    } finally {
      setUploading(false);
    }
  };

  return (
    <MobileScrollArea className="bg-background min-h-screen pt-safe">
      <div className="pb-20">
        <div className="top-0 left-0 right-0 bg-background z-10 shadow-sm">
          <div className="flex items-center justify-between px-4">
            <Button variant="ghost" size="icon" onClick={() => navigate('/')}>
              <ArrowLeft />
            </Button>
            <h1 className="text-xl font-bold">New Post</h1>
            <div className="w-8"></div>
          </div>
        </div>
        
        <div className="pt-16 px-4">
          {!videoPreview ? (
            <div className="border-2 border-dashed border-border rounded-lg p-4 h-64 flex flex-col items-center justify-center">
              <div className="cursor-pointer flex flex-col items-center">
                <Upload size={40} className="mb-2 text-forest" />
                <p className="font-medium mb-1">Upload video</p>
                <p className="text-xs text-muted-foreground mb-4">MP4 or MOV format</p>
                <Button 
                  className="bg-forest hover:bg-forest-dark" 
                  onClick={handleSelectFileClick}
                >
                  Select File
                </Button>
                <input 
                  ref={fileInputRef}
                  type="file" 
                  accept="video/*" 
                  className="hidden" 
                  onChange={handleFileChange}
                />
              </div>
            </div>
          ) : editingVideo ? (
            <VideoEditor 
              videoSrc={videoPreview}
              onSave={handleSaveEditedVideo}
              onCancel={handleCancelEdit}
              initialSettings={editorSettings}
              onSettingsChange={setEditorSettings}
            />
          ) : (
            <div className="rounded-lg overflow-hidden mb-4 relative">
              <div className="relative w-full aspect-[9/16] max-h-[70vh]">
                <div 
                  style={{ 
                    filter: selectedColorFilter?.cssFilter || "none"
                  }}
                  className="w-full h-full"
                >
                  <VideoPlayer 
                    url={videoPreview} 
                    muted={false}
                    autoPlay={true}
                    audioUrl={selectedSound?.sound_url}
                    isCreatePagePreview={true}
                  />
                </div>
                <div className="absolute top-2 right-2 flex space-x-2 z-10">
                  <Button 
                    variant="secondary" 
                    className="bg-white/80 hover:bg-white text-forest flex items-center gap-1 px-3 py-1"
                    onClick={handleEditVideo}
                  >
                    <Scissors size={16} />
                    <span>Edit</span>
                  </Button>
                  <Button 
                    variant="destructive" 
                    size="icon"
                    onClick={handleClearVideo}
                  >
                    <X size={16} />
                  </Button>
                </div>
              </div>
            </div>
          )}
          
          {!editingVideo && videoPreview && (
            <div className="space-y-4 mt-4">
              <div>
                <Textarea 
                  placeholder="Add a caption..."
                  value={caption}
                  onChange={(e) => setCaption(e.target.value)}
                  className="resize-none"
                />
              </div>
              
              <ColorFilterSelector
                onFilterSelect={setSelectedColorFilter}
                selectedFilter={selectedColorFilter}
              />
              
              <TagInput
                value={tags}
                onChange={setTags}
                placeholder="Add tags (separate with commas)"
              />
              
              <SoundSelector
                onSoundSelect={setSelectedSound}
                selectedSound={selectedSound}
              />
              
              <Button 
                className="w-full bg-forest hover:bg-forest-dark h-12 text-lg font-semibold"
                onClick={handlePost}
                disabled={!videoFile || uploading || !user}
              >
                {uploading ? "Uploading..." : "Post Video"}
              </Button>
            </div>
          )}
        </div>
      </div>
    </MobileScrollArea>
  );
};

export default Create;
