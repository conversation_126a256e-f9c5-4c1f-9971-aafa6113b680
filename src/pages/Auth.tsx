import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, Navigate, <PERSON> } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, <PERSON>O<PERSON> } from "lucide-react";

const loginSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

const signupSchema = loginSchema.extend({
  username: z.string().min(3, { message: "Username must be at least 3 characters" }),
  inviteCode: z.string().min(1, { message: "Invite code is required" }),
  confirmPassword: z.string().min(6, { message: "Password must be at least 6 characters" }),
  termsAccepted: z.boolean().refine(val => val === true, {
    message: "You must accept the Terms of Service and Privacy Policy",
  }),
  marketingConsent: z.boolean().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

const resetPasswordSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
});

type LoginFormValues = z.infer<typeof loginSchema>;
type SignupFormValues = z.infer<typeof signupSchema>;
type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

const Auth = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [resetPasswordMode, setResetPasswordMode] = useState(false);
  const [resetPasswordSent, setResetPasswordSent] = useState(false);
  const [inviteCodeError, setInviteCodeError] = useState<string | null>(null);
  const [usernameCheckLoading, setUsernameCheckLoading] = useState(false);
  const [usernameError, setUsernameError] = useState<string | null>(null);
  const [showLoginPassword, setShowLoginPassword] = useState(false);
  const [showSignupPassword, setShowSignupPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { signIn, signUp, user, resetPassword, checkUsernameAvailable } = useAuth();
  const navigate = useNavigate();

  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const signupForm = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      email: "",
      username: "",
      inviteCode: "",
      password: "",
      confirmPassword: "",
      termsAccepted: false,
      marketingConsent: false,
    },
  });

  const resetPasswordForm = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const onLoginSubmit = async (data: LoginFormValues) => {
    setIsLoading(true);
    try {
      await signIn(data.email, data.password);
      // Check for a redirect path saved in session storage
      const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/';
      sessionStorage.removeItem('redirectAfterLogin'); // Clean up
      navigate(redirectPath);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const validateInviteCode = async (code: string): Promise<boolean> => {
    try {
      const { data, error } = await supabase.rpc('validate_and_use_invite_code', {
        code_text: code
      });
      
      if (error) {
        console.error('Error validating invite code:', error);
        setInviteCodeError('Error validating invite code. Please try again.');
        return false;
      }
      
      if (data === false) {
        setInviteCodeError('Invalid or expired invite code. Please contact the person who invited you.');
        return false;
      }
      
      setInviteCodeError(null);
      return true;
    } catch (error) {
      console.error('Invite code validation error:', error);
      setInviteCodeError('Error validating invite code. Please try again.');
      return false;
    }
  };

  const checkUsername = async (username: string) => {
    if (username.length < 3) return;
    
    setUsernameCheckLoading(true);
    try {
      const isAvailable = await checkUsernameAvailable(username);
      if (!isAvailable) {
        setUsernameError("Username is already taken");
      } else {
        setUsernameError(null);
      }
    } catch (error) {
      console.error("Username check error:", error);
    } finally {
      setUsernameCheckLoading(false);
    }
  };

  const onSignupSubmit = async (data: SignupFormValues) => {
    setIsLoading(true);
    try {
      // Check username availability again
      const isUsernameAvailable = await checkUsernameAvailable(data.username);
      if (!isUsernameAvailable) {
        setUsernameError("Username is already taken");
        setIsLoading(false);
        return;
      }
      
      // Validate the invite code
      const isValidCode = await validateInviteCode(data.inviteCode);
      if (!isValidCode) {
        setIsLoading(false);
        return;
      }
      
      // If valid, proceed with signup
      await signUp(data.email, data.password, data.username, data.inviteCode);
      toast.success("Account created! Please check your email to verify your account.", {
        duration: 5000,
      });
      signupForm.reset();
    } catch (error: any) {
      console.error(error);
      toast.error(error.message || "An error occurred during sign up");
    } finally {
      setIsLoading(false);
    }
  };

  const onResetPasswordSubmit = async (data: ResetPasswordFormValues) => {
    setIsLoading(true);
    try {
      await resetPassword(data.email);
      setResetPasswordSent(true);
      toast.success("Password reset link sent. Please check your email.", {
        duration: 5000,
      });
    } catch (error) {
      console.error(error);
      toast.error("Failed to send password reset email. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleResetPasswordMode = () => {
    setResetPasswordMode(!resetPasswordMode);
    setResetPasswordSent(false);
  };

  if (user) {
    // Check for a redirect path saved in session storage
    const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/';
    sessionStorage.removeItem('redirectAfterLogin'); // Clean up
    return <Navigate to={redirectPath} />;
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 relative overflow-hidden pt-safe">
      <div className="absolute inset-0 z-0">
        <img 
          src="/lovable-uploads/553f8a7e-7db3-415b-8850-6e65a790638a.png" 
          alt="Whitetail deer with antlers in brush" 
          className="w-full h-full object-cover opacity-60"
        />
        <div className="absolute inset-0 bg-black/60" />
      </div>
      
      <div className="w-full max-w-md mx-auto z-10">
        <div className="text-center mb-8">
          <h1 className="text-5xl font-bold mb-2 text-white drop-shadow-[0_2px_8px_rgba(0,0,0,0.8)] animate-[fade-in_0.6s_ease-out] bg-gradient-to-r from-white via-sky-200 to-white bg-clip-text text-transparent">
            Whitetail Livn
          </h1>
          <p className="text-xl font-semibold text-sky-300 drop-shadow-[0_1px_3px_rgba(0,0,0,0.9)] animate-[fade-in_0.8s_ease-out_0.2s_both] tracking-wider">
            The Hunt Never Ends
          </p>
        </div>

        {resetPasswordMode ? (
          <Card className="border-border bg-background/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-2xl text-forest">Reset Password</CardTitle>
              <CardDescription className="text-base">
                Enter your email address and we'll send you a link to reset your password
              </CardDescription>
            </CardHeader>
            <CardContent>
              {resetPasswordSent ? (
                <Alert className="mb-4 bg-green-100 border-green-200">
                  <AlertDescription>
                    If an account exists with this email, you will receive a password reset link shortly.
                  </AlertDescription>
                </Alert>
              ) : (
                <Form {...resetPasswordForm}>
                  <form onSubmit={resetPasswordForm.handleSubmit(onResetPasswordSubmit)} className="space-y-4">
                    <FormField
                      control={resetPasswordForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base font-medium">Email</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button type="submit" className="w-full bg-forest hover:bg-forest/80 text-base font-semibold py-5" disabled={isLoading}>
                      {isLoading ? "Sending..." : "Send Reset Link"}
                    </Button>
                  </form>
                </Form>
              )}
            </CardContent>
            <CardFooter>
              <Button variant="link" onClick={toggleResetPasswordMode} className="w-full">
                Back to Login
              </Button>
            </CardFooter>
          </Card>
        ) : (
          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="login" className="text-base font-medium">Login</TabsTrigger>
              <TabsTrigger value="signup" className="text-base font-medium">Sign Up</TabsTrigger>
            </TabsList>

            <TabsContent value="login">
              <Card className="border-border bg-background/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl text-forest">Login</CardTitle>
                  <CardDescription className="text-base">Enter your credentials to access your account</CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...loginForm}>
                    <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                      <FormField
                        control={loginForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base font-medium">Email</FormLabel>
                            <FormControl>
                              <Input placeholder="<EMAIL>" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={loginForm.control}
                        name="password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base font-medium">Password</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input 
                                  type={showLoginPassword ? "text" : "password"} 
                                  placeholder="••••••" 
                                  {...field} 
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                  onClick={() => setShowLoginPassword(!showLoginPassword)}
                                >
                                  {showLoginPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </Button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <Button type="submit" className="w-full bg-forest hover:bg-forest/80 text-base font-semibold py-5" disabled={isLoading}>
                        {isLoading ? "Logging in..." : "Login"}
                      </Button>
                      <div className="text-center">
                        <Button variant="link" onClick={toggleResetPasswordMode} className="text-sm text-sky-700 hover:text-sky-500">
                          Forgot password?
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="signup">
              <Card className="border-border bg-background/80 backdrop-blur-sm border-sky-200/20">
                <CardHeader className="pb-2">
                  <CardTitle className="text-2xl text-sky-500 font-bold">Create an Account</CardTitle>
                  <CardDescription className="text-base text-white/90">Join the Whitetail Livn community</CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...signupForm}>
                    <form onSubmit={signupForm.handleSubmit(onSignupSubmit)} className="space-y-4">
                      <FormField
                        control={signupForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base font-medium text-white/90">Email</FormLabel>
                            <FormControl>
                              <Input placeholder="<EMAIL>" className="border-sky-200/30 focus-visible:ring-sky-500/50" {...field} />
                            </FormControl>
                            <FormMessage className="font-medium" />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={signupForm.control}
                        name="username"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base font-medium text-white/90">Username</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="johndoe" 
                                className={`border-sky-200/30 focus-visible:ring-sky-500/50 ${usernameError ? 'border-red-500' : ''}`} 
                                {...field} 
                                onChange={(e) => {
                                  field.onChange(e);
                                  if (e.target.value.length >= 3) {
                                    checkUsername(e.target.value);
                                  }
                                }}
                              />
                            </FormControl>
                            {usernameError && (
                              <div className="text-red-500 text-sm mt-1">{usernameError}</div>
                            )}
                            <FormMessage className="font-medium" />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={signupForm.control}
                        name="inviteCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base font-medium text-white/90">
                              Invite Code <span className="text-red-400">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Enter your invite code" 
                                className={`border-sky-200/30 focus-visible:ring-sky-500/50 ${inviteCodeError ? 'border-red-500' : ''}`} 
                                {...field} 
                              />
                            </FormControl>
                            {inviteCodeError && (
                              <div className="text-red-500 text-sm mt-1">{inviteCodeError}</div>
                            )}
                            <FormMessage className="font-medium" />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={signupForm.control}
                        name="password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base font-medium text-white/90">Password</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input 
                                  type={showSignupPassword ? "text" : "password"} 
                                  placeholder="••••••" 
                                  className="border-sky-200/30 focus-visible:ring-sky-500/50" 
                                  {...field} 
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                  onClick={() => setShowSignupPassword(!showSignupPassword)}
                                >
                                  {showSignupPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </Button>
                              </div>
                            </FormControl>
                            <FormMessage className="font-medium" />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={signupForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base font-medium text-white/90">Confirm Password</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input 
                                  type={showConfirmPassword ? "text" : "password"} 
                                  placeholder="••••••" 
                                  className="border-sky-200/30 focus-visible:ring-sky-500/50" 
                                  {...field} 
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                >
                                  {showConfirmPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </Button>
                              </div>
                            </FormControl>
                            <FormMessage className="font-medium" />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={signupForm.control}
                        name="termsAccepted"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-4">
                            <FormControl>
                              <Checkbox 
                                checked={field.value} 
                                onCheckedChange={field.onChange}
                                className="data-[state=checked]:bg-sky-500 data-[state=checked]:border-sky-500"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="text-sm font-medium text-white/90">
                                I agree to the{" "}
                                <Link to="/terms" className="text-sky-400 hover:text-sky-300 underline">
                                  Terms of Service
                                </Link>{" "}
                                and{" "}
                                <Link to="/privacy" className="text-sky-400 hover:text-sky-300 underline">
                                  Privacy Policy
                                </Link>
                              </FormLabel>
                              <FormMessage />
                            </div>
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={signupForm.control}
                        name="marketingConsent"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-2">
                            <FormControl>
                              <Checkbox 
                                checked={field.value} 
                                onCheckedChange={field.onChange}
                                className="data-[state=checked]:bg-sky-500 data-[state=checked]:border-sky-500"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="text-sm font-medium text-white/90">
                                I agree to receive updates, offers, and promotional emails from Whitetail Livn
                              </FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                      
                      <Button 
                        type="submit" 
                        className="w-full bg-sky-500 hover:bg-sky-600 text-white text-base font-semibold py-5 mt-4 shadow-lg transition-all" 
                        disabled={isLoading || usernameCheckLoading || !!usernameError}
                      >
                        {isLoading ? "Creating Account..." : "Sign Up"}
                      </Button>
                    </form>
                  </Form>
                </CardContent>
                <CardFooter className="text-xs text-center text-muted-foreground font-medium pt-0">
                  By signing up, you agree to our <Link to="/terms" className="text-sky-400 hover:text-sky-300 underline">Terms of Service</Link> and <Link to="/privacy" className="text-sky-400 hover:text-sky-300 underline">Privacy Policy</Link>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
};

export default Auth;
