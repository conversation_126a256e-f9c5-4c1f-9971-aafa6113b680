
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import AdminUserManagement from "@/components/admin/AdminUserManagement";
import AdminPostModeration from "@/components/admin/AdminPostModeration";
import AdminServicesControl from "@/components/admin/AdminServicesControl";
import AdminAnalytics from "@/components/admin/AdminAnalytics";
import AdminInviteCodes from "@/components/admin/AdminInviteCodes";
import AdminSoundLibrary from "@/components/admin/AdminSoundLibrary";
import { Shield } from "lucide-react";

const AdminPanel = () => {
  const { user, profile } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check if user has admin access
  useEffect(() => {
    const checkAdminStatus = () => {
      // For now, we'll check if the user has an admin badge
      if (profile?.badges && Array.isArray(profile.badges)) {
        setIsAdmin(profile.badges.includes("admin"));
      }
      setLoading(false);
    };

    if (profile) {
      checkAdminStatus();
    }
  }, [profile]);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Verifying admin access...</p>
        </div>
      </div>
    );
  }

  // Redirect non-admin users
  if (!isAdmin) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="min-h-screen bg-background pb-20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center h-16 px-4">
          <div className="flex items-center gap-2 font-semibold">
            <Shield className="h-5 w-5 text-primary" />
            <h1 className="text-xl">Admin Panel</h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container py-6 max-w-7xl mx-auto">
        <Tabs defaultValue="users" className="w-full">
          <TabsList className="grid grid-cols-6 mb-8">
            <TabsTrigger value="users">User Management</TabsTrigger>
            <TabsTrigger value="posts">Post Moderation</TabsTrigger>
            <TabsTrigger value="services">Services Control</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="invite-codes">Invite Codes</TabsTrigger>
            <TabsTrigger value="sounds">Sound Library</TabsTrigger>
          </TabsList>
          
          <TabsContent value="users">
            <AdminUserManagement />
          </TabsContent>
          
          <TabsContent value="posts">
            <AdminPostModeration />
          </TabsContent>
          
          <TabsContent value="services">
            <AdminServicesControl />
          </TabsContent>
          
          <TabsContent value="analytics">
            <AdminAnalytics />
          </TabsContent>
          
          <TabsContent value="invite-codes">
            <AdminInviteCodes />
          </TabsContent>
          
          <TabsContent value="sounds">
            <AdminSoundLibrary />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminPanel;
