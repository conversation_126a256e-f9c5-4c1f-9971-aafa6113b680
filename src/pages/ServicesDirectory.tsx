import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import Header from "@/components/Header";
import MainNavigation from "@/components/MainNavigation";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { MapPin, MessageCircle, Filter, X, User, Crown, Compass } from "lucide-react";

import ServicesPromotionCard from "@/components/ServicesPromotionCard";


interface Service {
  type: string;
  description: string;
  contactMethod: string;
  contactValue: string;
  location?: {
    city?: string;
    zipCode?: string;
    coordinates?: {
      lat?: number;
      lng?: number;
    } | null;
  };
}

interface ServiceItem {
  id: string;
  username: string;
  region?: string;
  services: Service[];
  avatar_url?: string;
  bio?: string;
  is_premium?: boolean;
  premium_until?: string | null;
}

interface UserLocationCoords {
  lat: number;
  lng: number;
}

const ServicesDirectory = () => {
  const { user, profile } = useAuth();
  const navigate = useNavigate();
  const [services, setServices] = useState<ServiceItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterType, setFilterType] = useState("all");
  const [zipCode, setZipCode] = useState("");
  const [locationRadius, setLocationRadius] = useState([50]);
  const [showFilters, setShowFilters] = useState(false);
  const [availableServiceTypes, setAvailableServiceTypes] = useState<string[]>([]);
  const [userLocationCoords, setUserLocationCoords] = useState<UserLocationCoords | null>(null);
  const [zipCodeLoading, setZipCodeLoading] = useState(false);

  // Service types matching the admin panel
  const serviceTypes = [
    "Bow Tech",
    "Deer Processing",
    "Deer Tracking Dogs",
    "Drone Recovery",
    "Euro Mounts",
    "Food Plot Work",
    "Guided Hunts",
    "Influencer Collaboration",
    "Land Management",
    "Official Scorer",
    "Real Estate Agent",
    "Social Media Marketing",
    "Taxidermy",
    "Trail Cam Setup",
    "Videographer"
  ];

  useEffect(() => {
    fetchServices();
  }, []);

  // Function to calculate distance between two coordinates using Haversine formula
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 3959; // Earth's radius in miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Function to get coordinates for a zip code
  const getCoordinatesForZipCode = async (zipCode: string): Promise<UserLocationCoords | null> => {
    try {
      setZipCodeLoading(true);
      const { data, error } = await supabase.functions.invoke('reverse-geocode', {
        body: { zipCode }
      });

      if (error) {
        console.error('Error getting coordinates for zip code:', error);
        toast.error("Failed to get location for zip code");
        return null;
      }

      if (data && data.latitude && data.longitude) {
        return {
          lat: data.latitude,
          lng: data.longitude
        };
      }

      return null;
    } catch (error) {
      console.error('Error in getCoordinatesForZipCode:', error);
      toast.error("Failed to get location for zip code");
      return null;
    } finally {
      setZipCodeLoading(false);
    }
  };

  // Update user location when zip code changes
  useEffect(() => {
    if (zipCode && zipCode.length >= 5) {
      const debounceTimer = setTimeout(async () => {
        const coords = await getCoordinatesForZipCode(zipCode);
        setUserLocationCoords(coords);
      }, 500);

      return () => clearTimeout(debounceTimer);
    } else {
      setUserLocationCoords(null);
    }
  }, [zipCode]);

  const fetchServices = async () => {
    try {
      setLoading(true);
      // Only fetch services from users with promotion subscription
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .not('services', 'is', null)
        .eq('is_promoted', true)
        .gte('promoted_until', new Date().toISOString());

      if (error) {
        console.error('Error fetching services:', error);
        toast.error("Failed to load services");
        return;
      }

      if (data) {
        const servicesData: ServiceItem[] = data.map(profile => ({
          id: profile.id,
          username: profile.username || 'Anonymous',
          region: profile.region,
          services: Array.isArray(profile.services) ? (profile.services as unknown as Service[]) : [],
          avatar_url: profile.avatar_url,
          bio: profile.bio,
          is_premium: profile.is_premium,
          premium_until: profile.premium_until
        })).filter(item => item.services.length > 0);

        // Sort services to put premium partners first
        const sortedServicesData = servicesData.sort((a, b) => {
          // Premium partners first
          if (a.is_premium && !b.is_premium) return -1;
          if (!a.is_premium && b.is_premium) return 1;
          // Then sort by username alphabetically
          return (a.username || '').localeCompare(b.username || '');
        });

        // Extract unique service types from actual data
        const uniqueServiceTypes = new Set<string>();
        sortedServicesData.forEach(item => {
          item.services.forEach(service => {
            if (service.type) {
              uniqueServiceTypes.add(service.type);
            }
          });
        });
        
        console.log("Available service types from data:", Array.from(uniqueServiceTypes));
        setAvailableServiceTypes(Array.from(uniqueServiceTypes));
        setServices(sortedServicesData);
      }
    } catch (error) {
      console.error('Error in fetchServices:', error);
      toast.error("Failed to load services");
    } finally {
      setLoading(false);
    }
  };

  const startConversation = async (recipientId: string) => {
    if (!user) {
      toast.error("You must be logged in to send messages");
      return;
    }

    if (user.id === recipientId) {
      toast.error("You cannot message yourself");
      return;
    }

    try {
      // Create or find existing conversation
      const { data: existingMessages } = await supabase
        .from('messages')
        .select('*')
        .or(`and(sender_id.eq.${user.id},receiver_id.eq.${recipientId}),and(sender_id.eq.${recipientId},receiver_id.eq.${user.id})`)
        .limit(1);

      if (existingMessages && existingMessages.length > 0) {
        // Conversation exists, navigate to inbox
        navigate('/inbox');
        return;
      }

      // Create initial message
      const { error } = await supabase
        .from('messages')
        .insert({
          sender_id: user.id,
          receiver_id: recipientId,
          content: "Hi, I'm interested in your services. Can we chat?"
        });

      if (error) {
        console.error('Error creating conversation:', error);
        toast.error("Failed to start conversation");
        return;
      }

      toast.success("Conversation started!");
      navigate('/inbox');
    } catch (error) {
      console.error('Error in startConversation:', error);
      toast.error("Failed to start conversation");
    }
  };

  const renderServiceCard = (serviceItem: ServiceItem) => {
    const isPremium = serviceItem.is_premium;
    
    return (
      <Card 
        key={serviceItem.id} 
        className={`group relative overflow-hidden transition-all duration-500 hover:shadow-2xl hover:-translate-y-3 hover:rotate-1 shadow-lg ${
          isPremium 
            ? "bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50 border-2 border-yellow-400 hover:border-yellow-500 shadow-yellow-200/50 hover:shadow-yellow-300/60 ring-2 ring-yellow-200 hover:ring-yellow-300" 
            : "bg-gradient-to-br from-white via-gray-50 to-blue-50/30 border border-gray-200 hover:border-forest/40 shadow-forest/20"
        }`}
      >
        {/* Enhanced animated background overlay for premium cards */}
        <div className={`absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${
          isPremium 
            ? "bg-gradient-to-br from-yellow-100/30 to-amber-100/40" 
            : "bg-gradient-to-br from-forest/5 to-blue-100/30"
        }`}></div>
        
        {/* Enhanced decorative corner accent */}
        <div className={`absolute top-0 left-0 w-24 h-24 rounded-br-full ${
          isPremium 
            ? "bg-gradient-to-br from-yellow-300/20 to-amber-300/30" 
            : "bg-gradient-to-br from-forest/10 to-transparent"
        }`}></div>

        {/* Premium crown decoration */}
        {isPremium && (
          <div className="absolute top-3 right-3 z-20">
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-yellow-400 to-amber-500 flex items-center justify-center shadow-lg animate-pulse">
              <Crown className="h-4 w-4 text-white" />
            </div>
          </div>
        )}

        {/* Profile Header */}
        <CardHeader className="pb-4 relative z-10">
          <div className="flex items-start gap-4">
            <div className="relative">
              <div className={`w-20 h-20 rounded-full overflow-hidden shadow-xl transition-all duration-300 group-hover:ring-8 ${
                isPremium 
                  ? "ring-4 ring-yellow-300 group-hover:ring-yellow-400" 
                  : "ring-4 ring-white group-hover:ring-forest/20"
              }`}>
                {serviceItem.avatar_url ? (
                  <img 
                    src={serviceItem.avatar_url} 
                    alt={serviceItem.username} 
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                ) : (
                  <div className={`w-full h-full flex items-center justify-center ${
                    isPremium 
                      ? "bg-gradient-to-br from-yellow-200/40 to-amber-300/60" 
                      : "bg-gradient-to-br from-forest/20 to-forest/40"
                  }`}>
                    <User className={`h-10 w-10 ${isPremium ? "text-amber-600" : "text-forest"}`} />
                  </div>
                )}
              </div>
              {/* Enhanced online indicator */}
              <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-3 border-white shadow-lg ${
                isPremium ? "bg-yellow-400 animate-bounce" : "bg-green-500 animate-pulse"
              }`}></div>
            </div>
            
            <div className="flex-1 min-w-0">
              <CardTitle className={`text-2xl font-bold mb-2 transition-colors duration-300 ${
                isPremium 
                  ? "text-amber-800 group-hover:text-amber-700" 
                  : "text-gray-900 group-hover:text-forest"
              }`}>
                {serviceItem.username}
              </CardTitle>
              {isPremium && (
                <Badge variant="secondary" className="bg-gradient-to-r from-yellow-200 to-amber-200 text-amber-800 border-2 border-yellow-400 mb-2 shadow-md">
                  <Crown className="h-3 w-3 mr-1" />
                  Premium Partner
                </Badge>
              )}
              {serviceItem.region && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <div className={`p-2 rounded-full transition-colors duration-300 ${
                    isPremium 
                      ? "bg-yellow-100 group-hover:bg-yellow-200" 
                      : "bg-gray-100 group-hover:bg-forest/10"
                  }`}>
                    <MapPin className="h-4 w-4" />
                  </div>
                  <span className="font-semibold">{serviceItem.region}</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0 pb-6 relative z-10">
          <div className="space-y-4">
            {serviceItem.services.slice(0, 2).map((service, idx) => (
              <div key={idx} className="relative">
                <div className={`backdrop-blur-sm rounded-xl p-5 border shadow-md hover:shadow-lg transition-all duration-200 group-hover:scale-105 ${
                  isPremium 
                    ? "bg-white/90 border-yellow-200 hover:border-yellow-300 shadow-yellow-100" 
                    : "bg-white/80 border-gray-100 hover:border-forest/30"
                }`}>
                  <div className="flex items-center gap-3 mb-4">
                    <Badge 
                      variant="outline" 
                      className={`font-bold px-4 py-2 text-sm transition-all duration-200 shadow-sm hover:shadow-md ${
                        isPremium 
                          ? "bg-gradient-to-r from-yellow-100 to-amber-100 text-amber-700 border-yellow-400" 
                          : "bg-gradient-to-r from-forest/10 to-forest/20 text-forest border-forest/40"
                      }`}
                    >
                      {service.type}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-gray-700 leading-relaxed mb-4 font-medium">
                    {service.description}
                  </p>
                  
                  
                  {service.contactMethod && service.contactValue && (
                    <div className="flex items-center gap-1 mt-1 text-xs text-muted-foreground">
                      <span className="font-medium">{service.contactMethod}:</span>
                      <span className="truncate">{service.contactValue}</span>
                    </div>
                  )}
                  
                  {service.location && (service.location.zipCode || service.location.city) && (
                    <div className={`flex items-center gap-3 text-xs font-medium rounded-lg px-4 py-3 border ${
                      isPremium 
                        ? "text-amber-700 bg-yellow-50 border-yellow-200" 
                        : "text-gray-600 bg-gray-50 border-gray-200"
                    }`}>
                      <MapPin className={`h-4 w-4 ${isPremium ? "text-amber-600" : "text-forest"}`} />
                      <span>
                        {service.location.zipCode}
                        {service.location.city && ` • ${service.location.city}`}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
            
            {serviceItem.services.length > 2 && (
              <div className="text-center">
                <Badge variant="secondary" className={`px-4 py-2 font-semibold transition-all duration-200 ${
                  isPremium 
                    ? "bg-yellow-100 text-amber-700 hover:bg-yellow-200" 
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}>
                  +{serviceItem.services.length - 2} more services
                </Badge>
              </div>
            )}
          </div>
        </CardContent>
        
        <CardFooter className={`pt-4 relative z-10 ${
          isPremium ? "bg-gradient-to-t from-yellow-50/80 to-transparent" : "bg-gradient-to-t from-gray-50/80 to-transparent"
        }`}>
          <Button
            onClick={() => startConversation(serviceItem.id)}
            className={`w-full group-hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl font-bold text-base py-3 ${
              isPremium 
                ? "bg-gradient-to-r from-yellow-500 via-amber-500 to-orange-500 hover:from-yellow-600 hover:via-amber-600 hover:to-orange-600 text-white shadow-yellow-400/40 hover:shadow-yellow-500/50" 
                : "bg-gradient-to-r from-forest via-forest/90 to-forest/80 hover:from-forest/90 hover:via-forest hover:to-forest text-white shadow-forest/30"
            }`}
          >
            <MessageCircle className="h-5 w-5 mr-3" />
            <span>Contact {isPremium ? "Premium " : ""}Provider</span>
          </Button>
        </CardFooter>
      </Card>
    );
  };

  const filteredServices = services.filter(serviceItem => {
    const matchesType = filterType === "all" || 
      serviceItem.services.some(service => {
        console.log("Comparing:", service.type, "with filter:", filterType);
        return service.type === filterType;
      });

    // Enhanced location filtering with radius support
    const matchesLocation = !zipCode || !userLocationCoords || 
      serviceItem.services.some(service => {
        if (!service.location) return false;
        
        // First check if zip code matches directly
        if (service.location.zipCode?.includes(zipCode)) {
          return true;
        }
        
        // Then check radius if service has coordinates
        if (service.location.coordinates?.lat && service.location.coordinates?.lng) {
          const distance = calculateDistance(
            userLocationCoords.lat,
            userLocationCoords.lng,
            service.location.coordinates.lat,
            service.location.coordinates.lng
          );
          console.log(`Distance from ${zipCode} to service location: ${distance.toFixed(2)} miles`);
          return distance <= locationRadius[0];
        }
        
        return false;
      });

    return matchesType && matchesLocation;
  }).sort((a, b) => {
    // Ensure premium partners stay at the top even after filtering
    if (a.is_premium && !b.is_premium) return -1;
    if (!a.is_premium && b.is_premium) return 1;
    // Then sort by username alphabetically
    return (a.username || '').localeCompare(b.username || '');
  });

  return (
    <div className="bg-gradient-to-br from-gray-50 to-white min-h-screen">
      <Header />
      
      <div className="container mx-auto px-4 pt-20 py-6 pb-20">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Main Content */}
          <div className="flex-1">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Services Directory</h1>
                <p className="text-gray-600">Find hunting and outdoor services in your area</p>
              </div>
            </div>

            {/* Search and Filters */}
            <Card className="mb-6">
              <CardHeader className="pb-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <Label htmlFor="service-type">Service Type</Label>
                    <Select value={filterType} onValueChange={setFilterType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select service type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All services</SelectItem>
                        {/* Use actual service types from data */}
                        {availableServiceTypes.map(type => (
                          <SelectItem key={type} value={type}>{type}</SelectItem>
                        ))}
                        {/* Also include predefined types that might not be in current data */}
                        {serviceTypes.filter(type => !availableServiceTypes.includes(type)).map(type => (
                          <SelectItem key={type} value={type}>{type}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex-1">
                    <Label htmlFor="zip-code">Zip Code</Label>
                    <div className="relative">
                      <Input
                        id="zip-code"
                        placeholder="Enter zip code..."
                        value={zipCode}
                        onChange={(e) => setZipCode(e.target.value)}
                        disabled={zipCodeLoading}
                      />
                      {zipCodeLoading && (
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-forest"></div>
                        </div>
                      )}
                    </div>
                    {userLocationCoords && (
                      <p className="text-xs text-green-600 mt-1">
                        Location found: {userLocationCoords.lat.toFixed(4)}, {userLocationCoords.lng.toFixed(4)}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowFilters(!showFilters)}
                      className="flex items-center gap-2"
                    >
                      <Filter className="h-4 w-4" />
                      Radius
                    </Button>
                  </div>
                </div>

                {showFilters && (
                  <div className="pt-4 border-t">
                    <Label htmlFor="radius-slider">Search radius: {locationRadius[0]} miles</Label>
                    <Slider
                      id="radius-slider"
                      min={5}
                      max={200}
                      step={5}
                      value={locationRadius}
                      onValueChange={setLocationRadius}
                      className="mt-2"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {zipCode && userLocationCoords 
                        ? `Searching within ${locationRadius[0]} miles of ${zipCode}`
                        : "Enter a zip code to use radius search"
                      }
                    </p>
                  </div>
                )}

                {(filterType !== "all" || zipCode) && (
                  <div className="flex flex-wrap gap-2 pt-4 border-t">
                    {filterType !== "all" && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        Service: {filterType}
                        <X className="h-3 w-3 cursor-pointer" onClick={() => setFilterType("all")} />
                      </Badge>
                    )}
                    {zipCode && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        Zip: {zipCode} ({locationRadius[0]} miles)
                        <X className="h-3 w-3 cursor-pointer" onClick={() => setZipCode("")} />
                      </Badge>
                    )}
                  </div>
                )}
              </CardHeader>
            </Card>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="h-3 bg-gray-200 rounded"></div>
                        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <>
                {/* Services Grid */}
                {filteredServices.length === 0 ? (
                  <Card className="p-8 text-center">
                    <Compass className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No services found</h3>
                    <p className="text-gray-600 mb-4">
                      {filterType !== "all" || zipCode
                        ? "Try adjusting your search filters"
                        : "Check back later for new services!"}
                    </p>
                  </Card>
                ) : (
                  <div>
                    <div className="flex items-center gap-3 mb-6">
                      <div className="p-2 rounded-full bg-gradient-to-r from-forest to-forest/80 shadow-lg">
                        <Compass className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900">All Services</h2>
                        <p className="text-sm text-gray-600">Premium Partners listed first • Discover local hunting and outdoor services</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                      {filteredServices.map((serviceItem) => renderServiceCard(serviceItem))}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:w-80 space-y-6">
            {user && (
              <ServicesPromotionCard />
            )}
          </div>
        </div>
      </div>

      <MainNavigation />
    </div>
  );
};

export default ServicesDirectory;
