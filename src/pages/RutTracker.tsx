import { useState, useEffect, useCallback } from "react";
import Header from "@/components/Header";
import MainNavigation from "@/components/MainNavigation";
import { Star, Activity, CloudSun, MapPin, X, Plus, Clock } from "lucide-react";
import DeerActivity from "@/components/rut-tracker/RutActivity";
import WeatherAlerts from "@/components/rut-tracker/WeatherAlerts";
import RutForm from "@/components/rut-tracker/RutForm";
import LocationDialog from "@/components/rut-tracker/LocationDialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { format, addDays } from "date-fns";
import { useSearchParams, useNavigate } from "react-router-dom";
import { generateOptimizedPredictions, getCurrentRutPhase, calculateRutScore, clearPredictionCache } from "@/utils/deerActivityPredictor";

import { Geolocation } from "@capacitor/geolocation";
import { Capacitor } from "@capacitor/core";
import { useIsMobile } from "@/hooks/use-mobile";

// Add keys to sessionStorage to store and retrieve location data
const LOCATION_STORAGE_KEY = "rutTracker_location";
const ADDITIONAL_LOCATIONS_KEY = "rutTracker_additional_locations";

// Calculate days until next rut phase
const getDaysUntilNextPhase = (currentPhase: string): {
  nextPhase: string;
  daysRemaining: number;
} => {
  const today = new Date();
  const currentYear = today.getFullYear();
  const month = today.getMonth(); // 0-based
  const date = today.getDate();
  const phaseTransitions = [{
    phase: "Off Season",
    nextPhase: "Pre-Rut",
    targetDate: new Date(currentYear, 9, 15)
  },
  // Oct 15
  {
    phase: "Pre-Rut",
    nextPhase: "Seeking Phase",
    targetDate: new Date(currentYear, 9, 25)
  },
  // Oct 25
  {
    phase: "Seeking Phase",
    nextPhase: "Chasing Phase",
    targetDate: new Date(currentYear, 10, 5)
  },
  // Nov 5
  {
    phase: "Chasing Phase",
    nextPhase: "Peak Breeding",
    targetDate: new Date(currentYear, 10, 10)
  },
  // Nov 10
  {
    phase: "Peak Breeding",
    nextPhase: "Post-Rut",
    targetDate: new Date(currentYear, 10, 19)
  },
  // Nov 19
  {
    phase: "Post-Rut",
    nextPhase: "Second Rut",
    targetDate: new Date(currentYear, 11, 5)
  },
  // Dec 5
  {
    phase: "Second Rut",
    nextPhase: "Off Season",
    targetDate: new Date(currentYear, 11, 16)
  } // Dec 16
  ];

  // Find current phase transition
  const currentTransition = phaseTransitions.find(t => t.phase === currentPhase);
  if (!currentTransition) {
    // Default to next Pre-Rut if phase not found
    const nextPreRut = month < 9 || month === 9 && date < 15 ? new Date(currentYear, 9, 15) : new Date(currentYear + 1, 9, 15);
    const daysRemaining = Math.ceil((nextPreRut.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return {
      nextPhase: "Pre-Rut",
      daysRemaining
    };
  }
  let targetDate = currentTransition.targetDate;

  // If the target date has passed this year, use next year's date
  if (targetDate < today) {
    if (currentPhase === "Second Rut") {
      // After Second Rut, next is Pre-Rut of next year
      targetDate = new Date(currentYear + 1, 9, 15);
    } else {
      targetDate = new Date(currentYear + 1, targetDate.getMonth(), targetDate.getDate());
    }
  }
  const daysRemaining = Math.ceil((targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  return {
    nextPhase: currentTransition.nextPhase,
    daysRemaining: Math.max(0, daysRemaining)
  };
};
const RutTracker = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const activeTab = searchParams.get("tab") || "activity";
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const isMobile = useIsMobile();
  const [location, setLocation] = useState<{
    state: string;
    county: string;
  } | null>(null);
  const [additionalLocations, setAdditionalLocations] = useState<{
    state: string;
    county: string;
  }[]>([]);
  const [activeLocation, setActiveLocation] = useState<{
    state: string;
    county: string;
  } | null>(null);
  const [rutScore, setRutScore] = useState<number>(0);
  const [locationLoading, setLocationLoading] = useState<boolean>(false);
  const [showLocationDialog, setShowLocationDialog] = useState<boolean>(false);
  // Increment locationKey to force re-renders when location changes
  const [locationKey, setLocationKey] = useState<number>(1);

  // Get user's location based on browser geolocation or sessionStorage
  useEffect(() => {
    console.log("Initial location setup");
    // First check if we have stored location data
    const storedLocation = sessionStorage.getItem(LOCATION_STORAGE_KEY);
    if (storedLocation) {
      try {
        const parsedLocation = JSON.parse(storedLocation);
        console.log("Loaded stored location:", parsedLocation);
        setLocation(parsedLocation);
        setActiveLocation(parsedLocation);

        // Also load additional locations
        const storedAdditionalLocations = sessionStorage.getItem(ADDITIONAL_LOCATIONS_KEY);
        if (storedAdditionalLocations) {
          setAdditionalLocations(JSON.parse(storedAdditionalLocations));
        }
        return; // Exit early if we found stored location
      } catch (err) {
        console.error("Failed to parse stored location:", err);
        // Continue to fetch new location if parsing fails
      }
    }
    fetchUserLocation();
  }, []);
  const fetchUserLocation = () => {
    setLocationLoading(true);
    console.log("Fetching user location");

    if (Capacitor.isPluginAvailable('Geolocation')) {
      Geolocation.getCurrentPosition(
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0
        }
      ).then(async position => {
        try {
          const {
            latitude,
            longitude
          } = position.coords;
          console.log("Location detected:", latitude, longitude);

          // Call our Supabase Edge Function for reverse geocoding
          const {
            data,
            error
          } = await supabase.functions.invoke('reverse-geocode', {
            body: {
              latitude,
              longitude
            }
          });
          if (error) {
            console.error("Error from reverse geocoding function:", error);
            toast.error("Couldn't determine your location");
            setLocationLoading(false);
            return;
          }
          if (data?.state && data?.county) {
            console.log("Location resolved:", data);
            const locationData = {
              state: data.state,
              county: data.county
            };
            setLocation(locationData);
            setActiveLocation(locationData);

            // Store location in sessionStorage for persistence
            sessionStorage.setItem(LOCATION_STORAGE_KEY, JSON.stringify(locationData));

            // Increment location key to force re-renders
            setLocationKey(prevKey => prevKey + 1);
            toast.success("Location detected");
          } else {
            toast.error("Location data incomplete");
            console.log("Geocoding response:", data);
          }
        } catch (error) {
          console.error("Error fetching location details:", error);
          toast.error("Failed to determine your location");
        } finally {
          setLocationLoading(false);
        }
      }).catch( error => {
        console.error("Geolocation error:", error);
        toast.error("Please enable location services to get localized rut data");
        setLocationLoading(false);
      });
    } else {
      toast.error("Your browser doesn't support geolocation");
      setLocationLoading(false);
    }
  };

  // Function to get coordinates for a specific location
  const getLocationCoordinates = async (locationData: {
    state: string;
    county: string;
  }) => {
    try {
      // Use a geocoding service to get coordinates for the specific location
      const response = await fetch(`https://api.openweathermap.org/geo/1.0/direct?q=${locationData.county} County,${locationData.state},US&limit=1&appid=${import.meta.env.VITE_OPENWEATHER_API_KEY || 'demo'}`);
      if (!response.ok) {
        throw new Error('Geocoding failed');
      }
      const data = await response.json();
      if (data && data.length > 0) {
        return {
          latitude: data[0].lat,
          longitude: data[0].lon
        };
      }
      throw new Error('Location not found');
    } catch (error) {
      console.log('Using fallback coordinates due to geocoding error:', error);
      // Fallback to default coordinates if geocoding fails
      return {
        latitude: 39.8283,
        longitude: -98.5795 // Center of US
      };
    }
  };

  // Handle location change from dialog
  const handleLocationChange = (newLocation: {
    state: string;
    county: string;
  }) => {
    console.log("Location change handler called with:", newLocation);

    // Clear prediction cache when location changes
    clearPredictionCache();

    // Check if this is a new additional location
    if (!location) {
      // If there's no primary location, set this as the primary
      setLocation(newLocation);
      setActiveLocation(newLocation);
      sessionStorage.setItem(LOCATION_STORAGE_KEY, JSON.stringify(newLocation));
    } else {
      // Add to additional locations
      const updatedLocations = [...additionalLocations, newLocation];
      setAdditionalLocations(updatedLocations);
      sessionStorage.setItem(ADDITIONAL_LOCATIONS_KEY, JSON.stringify(updatedLocations));

      // Set the new location as active
      setActiveLocation(newLocation);
    }

    // Increment locationKey to force components to re-render
    setLocationKey(prevKey => prevKey + 1);

    // Invalidate queries to refetch data with new location
    queryClient.invalidateQueries({
      queryKey: ['rutData']
    });
    queryClient.invalidateQueries({
      queryKey: ['weatherData']
    });
  };
  const handleSwitchLocation = useCallback((loc: {
    state: string;
    county: string;
  }) => {
    console.log("Switching to location:", loc);

    // Only update if it's actually a different location
    if (!activeLocation || activeLocation.state !== loc.state || activeLocation.county !== loc.county) {
      // Clear cache for better performance
      clearPredictionCache();
      setActiveLocation(loc);

      // Increment locationKey to force components to re-render
      setLocationKey(prevKey => prevKey + 1);

      // Invalidate queries to refetch data with new location
      queryClient.invalidateQueries({
        queryKey: ['rutData']
      });
      queryClient.invalidateQueries({
        queryKey: ['weatherData']
      });
      toast.info(`Switched to ${loc.county} County, ${loc.state}`);
    }
  }, [activeLocation, queryClient]);
  const handleRemoveLocation = (index: number) => {
    const locationToRemove = additionalLocations[index];
    const updatedLocations = additionalLocations.filter((_, i) => i !== index);
    setAdditionalLocations(updatedLocations);
    sessionStorage.setItem(ADDITIONAL_LOCATIONS_KEY, JSON.stringify(updatedLocations));

    // If removing the active location, set active to primary location
    if (activeLocation && activeLocation.state === locationToRemove.state && activeLocation.county === locationToRemove.county) {
      setActiveLocation(location);
      setLocationKey(prevKey => prevKey + 1);

      // Invalidate queries to refetch data
      queryClient.invalidateQueries({
        queryKey: ['rutData']
      });
      queryClient.invalidateQueries({
        queryKey: ['weatherData']
      });
    }
    toast.info("Location removed");
  };
  const handleLocationReset = () => {
    clearPredictionCache();
    sessionStorage.removeItem(LOCATION_STORAGE_KEY);
    sessionStorage.removeItem(ADDITIONAL_LOCATIONS_KEY);
    setLocation(null);
    setAdditionalLocations([]);
    setActiveLocation(null);
    setLocationKey(prevKey => prevKey + 1);
    toast.info("All locations reset. Please enable location services.");

    // Clean up any stale data
    queryClient.removeQueries({
      queryKey: ['rutData']
    });
    queryClient.removeQueries({
      queryKey: ['weatherData']
    });
  };

  // Create a dependable locationString for query keys
  const locationString = activeLocation ? `${activeLocation.state}-${activeLocation.county}` : 'none';

  // Optimized score calculation using the new prediction system
  useEffect(() => {
    if (activeLocation) {
      const currentPhase = getCurrentRutPhase();
      const optimizedScore = calculateRutScore(currentPhase, activeLocation);
      setRutScore(parseFloat(optimizedScore.toFixed(1)));
    }
  }, [activeLocation, locationString]);

  // Optimized rut data query with better performance
  const {
    data: rutData,
    isLoading: rutLoading,
    refetch: refetchRutData
  } = useQuery({
    queryKey: ['rutData', locationString, locationKey],
    queryFn: async () => {
      console.log("Fetching optimized rut data for key:", locationKey, "and location:", locationString);

      // Simulate API delay for realistic feel
      await new Promise(resolve => setTimeout(resolve, 400));
      if (!activeLocation) return null;
      const currentPhase = getCurrentRutPhase();
      const today = new Date();

      // Use location hash for consistent but varied data
      const locationHash = Array.from(locationString).reduce((acc, char, i) => acc + char.charCodeAt(0) * (i + 1), 0);

      // Generate more realistic reports based on current phase
      const getPhaseMultiplier = (phase: string) => {
        switch (phase) {
          case "Pre-Rut":
            return 1.0;
          case "Seeking Phase":
            return 1.3;
          case "Chasing Phase":
            return 1.8;
          case "Peak Breeding":
            return 2.0;
          case "Post-Rut":
            return 0.8;
          case "Second Rut":
            return 1.5;
          default:
            return 0.3;
        }
      };
      const phaseMultiplier = getPhaseMultiplier(currentPhase);
      const reports = [{
        type: "scrape",
        count: Math.round((8 + locationHash % 12) * phaseMultiplier),
        trend: ["increasing", "decreasing", "stable"][locationHash % 3] as "increasing" | "decreasing" | "stable"
      }, {
        type: "chase",
        count: Math.round((3 + locationHash % 8) * phaseMultiplier),
        trend: ["increasing", "decreasing", "stable"][Math.floor(locationHash / 3) % 3] as "increasing" | "decreasing" | "stable"
      }, {
        type: "rub",
        count: Math.round((12 + locationHash % 15) * phaseMultiplier),
        trend: ["increasing", "decreasing", "stable"][Math.floor(locationHash / 5) % 3] as "increasing" | "decreasing" | "stable"
      }, {
        type: "matureBuck",
        count: Math.round((2 + locationHash % 6) * phaseMultiplier),
        trend: ["increasing", "decreasing", "stable"][Math.floor(locationHash / 7) % 3] as "increasing" | "decreasing" | "stable"
      }];

      // Generate basic activity data (the optimized predictions will enhance this)
      const activity = [];

      // Past 5 days with more realistic variation
      for (let i = 0; i < 5; i++) {
        const pastDate = format(addDays(today, -5 + i), "MMM d");
        const locVariation = Math.sin(i + locationHash) * 0.4;
        const pastScore = Math.max(1, Math.min(5, rutScore - 0.3 * (5 - i) + locVariation));
        activity.push({
          date: pastDate,
          score: parseFloat(pastScore.toFixed(1))
        });
      }

      // Today's score
      activity.push({
        date: "Today",
        score: rutScore
      });

      // Future 5 days (basic predictions - will be enhanced by the optimizer)
      for (let i = 1; i <= 5; i++) {
        const futureDate = format(addDays(today, i), "MMM d");
        const locPrediction = Math.cos(i + locationHash) * 0.5;
        const futureScore = Math.max(1, Math.min(5, rutScore + locPrediction));
        activity.push({
          date: futureDate,
          score: parseFloat(futureScore.toFixed(1)),
          isPrediction: true
        });
      }
      return {
        phase: currentPhase,
        activity,
        reports
      };
    },
    enabled: !!activeLocation,
    staleTime: 2 * 60 * 1000 // Cache for 2 minutes for better performance
  });

  // Weather data query remains the same but with better caching
  const {
    data: weatherData,
    isLoading: weatherLoading,
    refetch: refetchWeatherData
  } = useQuery({
    queryKey: ['weatherData', locationString, locationKey],
    queryFn: async () => {
      console.log("Fetching weather data for key:", locationKey, "and location:", locationString);
      if (!activeLocation) return null;
      try {
        // Call our Supabase Edge Function to get weather data
        // The edge function will handle geocoding internally
        const {
          data,
          error
        } = await supabase.functions.invoke('get-weather', {
          body: {
            location: activeLocation,
            // Pass the location object instead of coordinates
            locationString // Add locationString for consistent results
          }
        });
        if (error) {
          console.error("Error fetching weather data:", error);
          throw error;
        }

        // Format dates in the response to match current date
        if (data && data.forecast) {
          data.forecast = data.forecast.map((day: any, index: number) => {
            return {
              ...day,
              date: index === 0 ? "Today" : format(addDays(new Date(), index), "MMM d")
            };
          });

          // Update nextColdFront date if it exists
          if (data.nextColdFront && data.nextColdFront.date) {
            const coldFrontIndex = data.forecast.findIndex((day: any) => day.date === data.nextColdFront.date);
            if (coldFrontIndex >= 0) {
              data.nextColdFront.date = data.forecast[coldFrontIndex].date;
            }
          }

          // Update alert dates if they exist
          if (data.alerts && data.alerts.length > 0) {
            data.alerts = data.alerts.map((alert: any) => {
              const alertIndex = data.forecast.findIndex((day: any) => day.date === alert.date);
              return {
                ...alert,
                date: alertIndex >= 0 ? data.forecast[alertIndex].date : alert.date
              };
            });
          }
        }
        return data;
      } catch (error) {
        console.error("Failed to fetch weather data:", error);
        // Fallback to mock data if API fails
        await new Promise(resolve => setTimeout(resolve, 300));

        // Use location string to create deterministic but different values for each location
        const locationHash = Array.from(locationString).reduce((acc, char, i) => acc + char.charCodeAt(0) * (i + 1), 0);
        const today = new Date();

        // Determine a random but consistent day for the cold front (1-4 days from now)
        const coldFrontDay = locationHash % 4 + 1;
        const coldFrontDate = format(addDays(today, coldFrontDay), "MMM d");

        // Location-specific temperature variation
        const tempVariation = locationHash % 20 - 10; // -10 to +10
        const baseTemp = 40 + tempVariation;
        const alerts = [{
          id: 1,
          type: "cold-front",
          date: coldFrontDate,
          message: `${10 + locationHash % 10}°F temperature drop coming - mature buck movement likely`,
          severity: ["low", "medium", "high"][locationHash % 3] as "low" | "medium" | "high"
        }];

        // Add an extra alert for some locations
        if (locationHash % 5 === 0) {
          alerts.push({
            id: 2,
            type: "wind",
            date: format(addDays(today, locationHash % 3 + 1), "MMM d"),
            message: "Strong winds expected - consider hunting protected areas",
            severity: "medium" as "low" | "medium" | "high"
          });
        }

        // Generate moon phase data
        const currentDate = new Date();
        const dayOfMonth = currentDate.getDate();
        // Simulate moon illumination based on day of month (0% to 100% over the month cycle)
        const illumination = Math.round(dayOfMonth / 30 * 100) % 100;
        // Adjust illumination slightly based on location
        const adjustedIllumination = Math.min(100, Math.max(0, illumination + locationHash % 10 - 5));
        let phase = "New Moon";
        let isGood = false;
        let message = "";
        if (adjustedIllumination < 10) {
          phase = "New Moon";
          isGood = true;
          message = "New moon period with minimal illumination. Excellent for deer movement, especially at dusk and dawn.";
        } else if (adjustedIllumination < 40) {
          phase = "Waxing Crescent";
          isGood = true;
          message = "Growing moonlight provides good visibility without excessive brightness. Favorable for early evening deer activity.";
        } else if (adjustedIllumination < 60) {
          phase = "Half Moon";
          isGood = false;
          message = "Moderate illumination may affect deer movement patterns. Focus on dawn hunting during this phase.";
        } else if (adjustedIllumination < 90) {
          phase = "Waxing Gibbous";
          isGood = false;
          message = "Bright evening conditions may delay deer movement until later hours. Consider morning hunts or heavily covered areas.";
        } else {
          phase = "Full Moon";
          isGood = false;
          message = "Maximum illumination may push deer movement toward midnight hours. Challenging hunting conditions for traditional times.";
        }

        // Create a consistent but varying pressure trend based on location
        const pressureTrends = ["rising", "falling", "stable"];
        const pressureTrend = pressureTrends[locationHash % 3] as "rising" | "falling" | "stable";

        // Generate forecast with location-specific variations
        const forecast = [];
        for (let i = 0; i <= 4; i++) {
          const dayHash = (locationHash + i) * 31;
          const high = baseTemp + 5 - i * 2 + dayHash % 7;
          const low = baseTemp - 15 + dayHash % 5;
          const pressure = 30 + dayHash % 100 / 100;
          forecast.push({
            date: i === 0 ? "Today" : format(addDays(today, i), "MMM d"),
            high: high,
            low: low,
            pressure: pressure,
            icon: "cloud-sun",
            wind: {
              am: {
                speed: 3 + dayHash % 8,
                direction: ["N", "NE", "E", "SE", "S", "SW", "W", "NW"][dayHash % 8]
              },
              pm: {
                speed: 5 + dayHash % 10,
                direction: ["N", "NE", "E", "SE", "S", "SW", "W", "NW"][(dayHash + 4) % 8]
              },
              maxGust: 10 + dayHash % 15
            }
          });
        }
        return {
          currentTemp: baseTemp,
          pressureTrend,
          nextColdFront: {
            date: coldFrontDate,
            tempDrop: 10 + locationHash % 10,
            pressureSpike: locationHash % 2 === 0
          },
          forecast,
          alerts,
          moonPhase: {
            phase,
            illumination: adjustedIllumination,
            isGood,
            message
          }
        };
      }
    },
    enabled: !!activeLocation,
    staleTime: 5 * 60 * 1000 // Cache weather data for 5 minutes
  });

  // Force refetch when location changes through useEffect
  useEffect(() => {
    // Refetch data when location changes
    if (activeLocation) {
      console.log("Forcing refetch due to location change, key:", locationKey);
      refetchRutData();
      refetchWeatherData();
    }
  }, [activeLocation, locationKey, refetchRutData, refetchWeatherData]);
  const handleTabChange = (value: string) => {
    setSearchParams({
      tab: value
    });
  };

  // Get the rating label based on score
  const getStarRatingLabel = (score: number): string => {
    if (score >= 4.5) return "GET IN THE WOODS";
    if (score >= 3.5) return "Call In Sick";
    if (score >= 2.5) return "Good";
    if (score >= 1.5) return "Ok";
    return "Sleep In";
  };
  const renderStars = (score: number) => {
    const stars = [];
    const fullStars = Math.floor(score);
    const hasHalfStar = score - fullStars >= 0.5;
    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<Star key={i} className="fill-yellow-400 text-yellow-400 drop-shadow-sm" size={28} />);
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(<div key={i} className="relative">
            <Star className="text-gray-300" size={28} />
            <div className="absolute top-0 left-0 w-1/2 overflow-hidden">
              <Star className="fill-yellow-400 text-yellow-400 drop-shadow-sm" size={28} />
            </div>
          </div>);
      } else {
        stars.push(<Star key={i} className="text-gray-300" size={28} />);
      }
    }
    return stars;
  };
  return(
    <div className="bg-background min-h-screen pb-20">
      <Header />
      
      <div className={`container px-4 ${isMobile ? 'pt-16' : 'pt-4'}`}>
        <h1 className="text-2xl font-bold text-forest mb-2">
      </h1>
        
        {/* Enhanced Location and current deer score card */}
        <div className="bg-gradient-to-br from-white via-white to-forest/5 rounded-xl shadow-xl border border-forest/10 p-6 mb-4 relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-forest/10 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-yellow-100/50 to-transparent rounded-full translate-y-12 -translate-x-12"></div>
          
          {location ? <>
              <div className="flex items-center justify-between mb-4 relative z-10">
                <h2 className="text-lg">
                  <span className="font-medium text-gray-800">{activeLocation ? `${activeLocation.county} County, ${activeLocation.state}` : `${location.county} County, ${location.state}`}</span>
                </h2>
                <button onClick={() => setShowLocationDialog(true)} className="text-xs text-forest hover:text-forest/80 font-medium hover:underline transition-colors duration-200">
                  Add Location
                </button>
              </div>

              {/* Primary location card */}
              <div className={`rounded-lg p-3 mb-2 flex justify-between items-center cursor-pointer transition-all duration-200 hover:scale-[1.02] ${activeLocation?.state === location.state && activeLocation?.county === location.county ? 'bg-forest/10 border-l-4 border-forest shadow-md' : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'}`} onClick={() => handleSwitchLocation(location)}>
                <div className="flex items-center flex-1">
                  <MapPin className={`h-4 w-4 mr-2 ${activeLocation?.state === location.state && activeLocation?.county === location.county ? 'text-forest' : 'text-gray-500'}`} />
                  <span className={`text-sm font-medium ${activeLocation?.state === location.state && activeLocation?.county === location.county ? 'text-forest' : 'text-gray-700'}`}>
                    {location.county} County, {location.state}
                    {activeLocation?.state === location.state && activeLocation?.county === location.county && <span className="ml-2 text-xs bg-forest/20 text-forest px-1.5 py-0.5 rounded-full">Active</span>}
                  </span>
                </div>
              </div>

              {/* Additional locations */}
              {additionalLocations.length > 0 && <div className="space-y-2 mb-4">
                  {additionalLocations.map((loc, index) => <div key={`${loc.state}-${loc.county}-${index}`} className={`rounded-lg p-3 flex justify-between items-center cursor-pointer transition-all duration-200 hover:scale-[1.02] ${activeLocation?.state === loc.state && activeLocation?.county === loc.county ? 'bg-blue-100 border-l-4 border-blue-500 shadow-md' : 'bg-blue-50 border border-blue-100 hover:bg-blue-100'}`} onClick={() => handleSwitchLocation(loc)}>
                      <div className="flex items-center flex-1">
                        <MapPin className={`h-4 w-4 mr-2 ${activeLocation?.state === loc.state && activeLocation?.county === loc.county ? 'text-blue-600' : 'text-blue-400'}`} />
                        <span className={`text-sm font-medium ${activeLocation?.state === loc.state && activeLocation?.county === loc.county ? 'text-blue-700' : 'text-blue-600'}`}>
                          {loc.county} County, {loc.state}
                          {activeLocation?.state === loc.state && activeLocation?.county === loc.county && <span className="ml-2 text-xs bg-blue-200 text-blue-700 px-1.5 py-0.5 rounded-full">Active</span>}
                        </span>
                      </div>
                      <Button variant="ghost" size="sm" onClick={e => {
                e.stopPropagation();
                handleRemoveLocation(index);
              }} className="text-gray-500 hover:text-red-600 hover:bg-red-50 h-8 w-8 p-0 transition-colors duration-200">
                        <X className="h-4 w-4" />
                      </Button>
                    </div>)}
                </div>}

              {/* Location controls */}
              <div className="flex space-x-2 mb-6">
                <Button variant="outline" size="sm" onClick={() => setShowLocationDialog(true)} className="text-forest hover:text-forest/80 hover:bg-forest/10 border-forest/20 flex items-center gap-1 flex-1 transition-all duration-200 hover:scale-105">
                  <Plus className="h-3 w-3" />
                  Add Location
                </Button>
                <Button variant="outline" size="sm" onClick={handleLocationReset} className="text-red-500 hover:text-red-600 hover:bg-red-50 border-red-200 transition-all duration-200 hover:scale-105">
                  Reset All
                </Button>
              </div>
              
              {/* Enhanced Activity Rating Section */}
              <div className="relative z-10">
                <div className="flex flex-col items-center justify-center py-6 bg-gradient-to-br from-forest/5 to-yellow-50/80 rounded-xl border border-forest/20 shadow-inner">
                  <div className="text-sm text-gray-700 mb-2 font-medium tracking-wide">Optimized Activity Rating ({format(new Date(), "MMM d")})</div>
                  
                  <div className="flex items-center justify-center mb-3 gap-1">
                    {renderStars(rutScore)}
                  </div>
                  
                  <div className="text-4xl font-bold text-forest mb-2 drop-shadow-sm">{rutScore}/5</div>
                  
                  <div className="text-lg font-bold text-gray-800 mb-3">{getStarRatingLabel(rutScore)}</div>
                  
                  <div className="flex items-center gap-3">
                    <div className="bg-gradient-to-r from-forest to-forest/80 text-white px-4 py-2 rounded-full font-semibold text-sm shadow-lg border border-forest/30">
                      {rutData?.phase || "Loading..."}
                    </div>
                    {rutData?.phase && (() => {
                  const countdownInfo = getDaysUntilNextPhase(rutData.phase);
                  return countdownInfo.daysRemaining > 0 && <div className="flex items-center gap-1 text-xs text-gray-600 bg-gray-100/80 px-3 py-1.5 rounded-full border border-gray-200">
                          <Clock className="h-3 w-3" />
                          <span className="font-medium">{countdownInfo.daysRemaining}d</span>
                          <span>to {countdownInfo.nextPhase}</span>
                        </div>;
                })()}
                  </div>
                </div>
              </div>
            </> : <div className="flex flex-col items-center justify-center py-8 relative z-10">
              {locationLoading ? <div className="animate-pulse mb-4">
                  <div className="h-4 bg-gray-200 rounded w-52 mb-3"></div>
                  <div className="h-8 bg-gray-200 rounded w-28 mx-auto"></div>
                </div> : <div className="mb-4">
                  <p className="text-center mb-3 font-medium text-gray-700">Please enable location services</p>
                  <button onClick={fetchUserLocation} className="mx-auto block bg-gradient-to-r from-forest to-forest/80 text-white px-6 py-3 rounded-lg text-sm font-semibold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
                    Try Again
                  </button>
                </div>}
              <p className="text-sm text-gray-500 font-medium mb-4">{locationLoading ? "Detecting your location..." : "Location services required"}</p>

              <Button onClick={() => setShowLocationDialog(true)} variant="outline" className="bg-forest/5 border-forest/20 text-forest hover:bg-forest/10 transition-all duration-200 hover:scale-105">
                Enter Location Manually
              </Button>
            </div>}
        </div>

        {/* Content tabs - modified to remove predictions tab */}
        <Tabs defaultValue={activeTab} value={activeTab} onValueChange={handleTabChange} className="mb-6">
          <TabsList className="w-full mb-4 overflow-x-auto justify-start px-0.5">
            <TabsTrigger value="activity" className="flex items-center gap-1.5 flex-1">
              <Activity size={16} />
              <span>Activity</span>
            </TabsTrigger>
            <TabsTrigger value="weather" className="flex items-center gap-1.5 flex-1">
              <CloudSun size={16} />
              <span>Weather</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="activity" className="space-y-4">
            <DeerActivity key={`activity-${locationKey}-${locationString}`} data={rutData} isLoading={rutLoading} weatherData={weatherData} location={activeLocation} />
            
            <RutForm location={activeLocation} />
          </TabsContent>
          
          <TabsContent value="weather" className="space-y-4">
            <WeatherAlerts key={`weather-${locationKey}-${locationString}`} data={weatherData} isLoading={weatherLoading} location={activeLocation} />
          </TabsContent>
        </Tabs>
        
        {/* Location dialog */}
        <LocationDialog open={showLocationDialog} onOpenChange={setShowLocationDialog} onLocationChange={handleLocationChange} />
        
        {/* Bottom navigation */}
        <MainNavigation />
      </div>
    </div>
  );
};

export default RutTracker;