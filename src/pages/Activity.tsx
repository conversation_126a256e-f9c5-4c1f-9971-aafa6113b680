
import { <PERSON>, MessageCircle, Heart, User, AtSign, MapPin, Plus, X } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Avatar } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import MainNavigation from "@/components/MainNavigation";
import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate, useLocation } from "react-router-dom";
import LocationDialog from "@/components/rut-tracker/LocationDialog";
import { toast } from "sonner";

interface ActivityItem {
  id: string;
  type: "like" | "comment" | "follow" | "mention";
  user: {
    id: string;
    username: string;
    avatarUrl: string | null;
  };
  content: string;
  thumbnail?: string | null;
  timeAgo: string;
  created_at: string;
  videoId?: string;
}

// Store location in sessionStorage for persistence across pages
const LOCATION_STORAGE_KEY = "activity_location";
const ADDITIONAL_LOCATIONS_KEY = "activity_additional_locations";

const ActivityItem = ({ activity, onClick }: { activity: ActivityItem, onClick: (activity: ActivityItem) => void }) => {
  const getIcon = () => {
    switch (activity.type) {
      case "like":
        return <Heart size={16} className="text-red-500" />;
      case "comment":
        return <MessageCircle size={16} className="text-blue-500" />;
      case "follow":
        return <User size={16} className="text-forest" />;
      case "mention":
        return <AtSign size={16} className="text-purple-500" />;
      default:
        return <Bell size={16} />;
    }
  };

  return (
    <div className="flex items-center p-4 border-b hover:bg-forest/5 cursor-pointer transition-colors" onClick={() => onClick(activity)}>
      <Avatar className="h-10 w-10 mr-3">
        <img src={activity.user.avatarUrl || `https://ui-avatars.com/api/?name=${activity.user.username}`} alt={activity.user.username} className="object-cover" />
      </Avatar>
      
      <div className="flex-1">
        <p className="text-sm">
          <span className="font-semibold">@{activity.user.username}</span> {activity.content}
        </p>
        <p className="text-xs text-muted-foreground">{activity.timeAgo}</p>
      </div>
      
      <div className="flex items-center">
        {getIcon()}
        
        {activity.thumbnail && (
          <div className="ml-3 h-12 w-12 rounded overflow-hidden">
            <img src={activity.thumbnail} alt="Content" className="h-full w-full object-cover" />
          </div>
        )}
      </div>
    </div>
  );
};

const Activity = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [locationDialogOpen, setLocationDialogOpen] = useState(false);
  const [customLocation, setCustomLocation] = useState<{ state: string; county: string } | null>(null);
  const [additionalLocations, setAdditionalLocations] = useState<{ state: string; county: string }[]>([]);
  const [activeLocation, setActiveLocation] = useState<{ state: string; county: string } | null>(null);
  
  // Get the tab from URL query parameters
  const searchParams = new URLSearchParams(location.search);
  const defaultTab = searchParams.get('tab') || 'all';

  // Load custom location and additional locations from session storage on component mount
  useEffect(() => {
    const storedLocation = sessionStorage.getItem(LOCATION_STORAGE_KEY);
    if (storedLocation) {
      try {
        const parsedLocation = JSON.parse(storedLocation);
        setCustomLocation(parsedLocation);
        setActiveLocation(parsedLocation);
      } catch (error) {
        console.error("Failed to parse stored location:", error);
      }
    }
    
    const storedAdditionalLocations = sessionStorage.getItem(ADDITIONAL_LOCATIONS_KEY);
    if (storedAdditionalLocations) {
      try {
        setAdditionalLocations(JSON.parse(storedAdditionalLocations));
      } catch (error) {
        console.error("Failed to parse stored additional locations:", error);
      }
    }
  }, []);

  useEffect(() => {
    if (!user) return;
    
    const fetchActivities = async () => {
      try {
        setLoading(true);
        
        // Fetch user's videos
        const { data: userVideos, error: videosError } = await supabase
          .from('videos')
          .select('id, video_url')
          .eq('user_id', user.id);
          
        if (videosError) throw videosError;
        
        if (userVideos && userVideos.length > 0) {
          const videoIds = userVideos.map(video => video.id);
          
          // Get comments on user's videos from other users
          const { data: comments, error: commentsError } = await supabase
            .from('comments')
            .select('*')
            .in('video_id', videoIds)
            .neq('user_id', user.id) // Exclude user's own comments
            .order('created_at', { ascending: false });
            
          if (commentsError) throw commentsError;
          
          // Transform comments into activities
          const commentActivities: ActivityItem[] = comments ? comments.map(comment => {
            const video = userVideos.find(v => v.id === comment.video_id);
            return {
              id: comment.id,
              type: 'comment',
              user: {
                id: comment.user_id,
                username: comment.username,
                avatarUrl: comment.user_avatar
              },
              content: 'commented on your video',
              thumbnail: video?.video_url,
              timeAgo: formatTimeAgo(comment.created_at),
              created_at: comment.created_at,
              videoId: comment.video_id
            };
          }) : [];
          
          setActivities(commentActivities);
          
          // If active location is set, fetch deer reports for that location
          if (activeLocation) {
            await fetchDeerReports(activeLocation, commentActivities);
          }
        }
      } catch (error) {
        console.error("Error fetching activities:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchActivities();
    
    // Set up real-time listener for new comments
    const commentsChannel = supabase
      .channel('public:comments')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'comments'
      }, async (payload) => {
        // Check if this comment is on user's video and not by the user
        if (payload.new.user_id === user.id) return;
        
        const { data: video } = await supabase
          .from('videos')
          .select('user_id, video_url')
          .eq('id', payload.new.video_id)
          .single();
          
        if (video && video.user_id === user.id) {
          // This is a comment on the user's video
          const newActivity: ActivityItem = {
            id: payload.new.id,
            type: 'comment',
            user: {
              id: payload.new.user_id,
              username: payload.new.username,
              avatarUrl: payload.new.user_avatar
            },
            content: 'commented on your video',
            thumbnail: video.video_url,
            timeAgo: formatTimeAgo(payload.new.created_at),
            created_at: payload.new.created_at,
            videoId: payload.new.video_id
          };
          
          setActivities(prev => [newActivity, ...prev]);
        }
      })
      .subscribe();
      
    return () => {
      supabase.removeChannel(commentsChannel);
    };
  }, [user, activeLocation]);
  
  // Helper function to fetch deer reports for a location
  const fetchDeerReports = async (location: { state: string; county: string }, existingActivities: ActivityItem[]) => {
    try {
      const { data: deerReports, error: reportsError } = await supabase
        .from('deer_reports')
        .select('*')
        .eq('state', location.state)
        .eq('county', location.county)
        .order('created_at', { ascending: false })
        .limit(20);
        
      if (reportsError) {
        console.error("Error fetching deer reports:", reportsError);
        return;
      }
      
      if (deerReports && deerReports.length > 0) {
        // Transform deer reports into activities
        const reportActivities: ActivityItem[] = deerReports.map(report => {
          return {
            id: report.id,
            type: "mention",
            user: {
              id: "system",
              username: "DeerTracker",
              avatarUrl: "https://ui-avatars.com/api/?name=Deer+Tracker&background=5D8C3E&color=fff"
            },
            content: `New ${report.report_type} report in ${report.county} County, ${report.state}${report.notes ? `: "${report.notes}"` : ''}`,
            timeAgo: formatTimeAgo(report.created_at),
            created_at: report.created_at
          };
        });
        
        // Combine activities with deer report activities
        setActivities(prevActivities => {
          const combined = [...prevActivities, ...reportActivities];
          return combined.sort((a, b) => 
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
        });
        
        toast.info(`Found ${deerReports.length} reports from ${location.county} County, ${location.state}`);
      } else {
        toast.info(`No reports found for ${location.county} County, ${location.state}`);
      }
    } catch (error) {
      console.error("Error fetching deer reports:", error);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffSec < 60) return `${diffSec}s ago`;
    if (diffMin < 60) return `${diffMin}m ago`;
    if (diffHour < 24) return `${diffHour}h ago`;
    if (diffDay < 7) return `${diffDay}d ago`;
    
    return date.toLocaleDateString();
  };
  
  const handleActivityClick = (activity: ActivityItem) => {
    if (activity.type === 'comment' && activity.videoId) {
      navigate(`/comments/${activity.videoId}`, { 
        state: { videoId: activity.videoId } 
      });
    } else if (activity.type === 'like' && activity.videoId) {
      // Navigate to the video
      sessionStorage.setItem('currentVideoId', activity.videoId);
      navigate('/');
    } else if (activity.type === 'follow' && activity.user.id) {
      navigate(`/user/${activity.user.id}`);
    }
  };

  const handleAddLocation = (newLocation: { state: string; county: string }) => {
    // Add the new location to additional locations
    const updatedLocations = [...additionalLocations, newLocation];
    setAdditionalLocations(updatedLocations);
    
    // Store in session storage for persistence
    sessionStorage.setItem(ADDITIONAL_LOCATIONS_KEY, JSON.stringify(updatedLocations));
    
    // Set the new location as active
    setActiveLocation(newLocation);
    
    // Clear existing activities to prepare for new location data
    setActivities([]);
    setLoading(true);
  };

  const handleLocationReset = () => {
    sessionStorage.removeItem(LOCATION_STORAGE_KEY);
    sessionStorage.removeItem(ADDITIONAL_LOCATIONS_KEY);
    setCustomLocation(null);
    setAdditionalLocations([]);
    setActiveLocation(null);
    setActivities([]);
    setLoading(true);
    toast.info("All locations reset. Showing your personal activities.");
  };
  
  const handleRemoveLocation = (index: number) => {
    const locationToRemove = additionalLocations[index];
    const updatedLocations = additionalLocations.filter((_, i) => i !== index);
    setAdditionalLocations(updatedLocations);
    sessionStorage.setItem(ADDITIONAL_LOCATIONS_KEY, JSON.stringify(updatedLocations));
    
    // If removing the active location, set active to primary location
    if (activeLocation && 
        activeLocation.state === locationToRemove.state && 
        activeLocation.county === locationToRemove.county) {
      setActiveLocation(customLocation);
    }
    
    // Refresh activities
    setActivities([]);
    setLoading(true);
    
    toast.info("Location removed");
  };

  const handlePrimaryLocationChange = (newLocation: { state: string; county: string }) => {
    setCustomLocation(newLocation);
    setActiveLocation(newLocation);
    // Store in session storage for persistence
    sessionStorage.setItem(LOCATION_STORAGE_KEY, JSON.stringify(newLocation));
    // Clear existing activities to prepare for new location data
    setActivities([]);
    setLoading(true);
  };

  const handleSwitchLocation = (location: { state: string; county: string }) => {
    setActiveLocation(location);
    setActivities([]);
    setLoading(true);
    toast.info(`Switched to ${location.county} County, ${location.state}`);
  };

  return (
    <div className="pb-16 bg-background min-h-screen">
      <div className="p-4 pt-20"> {/* Adjusted for header */}
        <h1 className="text-2xl font-bold mb-4">Activity</h1>
        
        {/* Custom location display */}
        {customLocation && (
          <div className={`rounded-lg p-3 mb-2 flex justify-between items-center ${activeLocation?.state === customLocation.state && activeLocation?.county === customLocation.county ? 'bg-forest/10 border-l-4 border-forest' : 'bg-gray-50 border border-gray-200'}`}>
            <div 
              className="flex items-center cursor-pointer flex-1"
              onClick={() => handleSwitchLocation(customLocation)}
            >
              <MapPin className={`h-4 w-4 mr-2 ${activeLocation?.state === customLocation.state && activeLocation?.county === customLocation.county ? 'text-forest' : 'text-gray-500'}`} />
              <span className={`text-sm font-medium ${activeLocation?.state === customLocation.state && activeLocation?.county === customLocation.county ? 'text-forest' : 'text-gray-700'}`}>
                {customLocation.county} County, {customLocation.state}
                {activeLocation?.state === customLocation.state && activeLocation?.county === customLocation.county && (
                  <span className="ml-2 text-xs bg-forest/20 text-forest px-1.5 py-0.5 rounded-full">Active</span>
                )}
              </span>
            </div>
            <div className="flex space-x-2">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setLocationDialogOpen(true)}
                className="text-forest hover:text-forest/80 hover:bg-forest/10 flex items-center gap-1"
              >
                <Plus className="h-3 w-3" />
                Add Location
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={handleLocationReset}
                className="text-red-500 hover:text-red-600 hover:bg-red-50"
              >
                Reset
              </Button>
            </div>
          </div>
        )}
        
        {/* Additional locations display */}
        {additionalLocations.length > 0 && (
          <div className="space-y-2 mb-4">
            {additionalLocations.map((loc, index) => (
              <div 
                key={`${loc.state}-${loc.county}-${index}`}
                className={`rounded-lg p-3 flex justify-between items-center cursor-pointer ${activeLocation?.state === loc.state && activeLocation?.county === loc.county ? 'bg-blue-100 border-l-4 border-blue-500' : 'bg-blue-50 border border-blue-100'}`}
                onClick={() => handleSwitchLocation(loc)}
              >
                <div className="flex items-center flex-1">
                  <MapPin className={`h-4 w-4 mr-2 ${activeLocation?.state === loc.state && activeLocation?.county === loc.county ? 'text-blue-600' : 'text-blue-400'}`} />
                  <span className={`text-sm font-medium ${activeLocation?.state === loc.state && activeLocation?.county === loc.county ? 'text-blue-700' : 'text-blue-600'}`}>
                    {loc.county} County, {loc.state}
                    {activeLocation?.state === loc.state && activeLocation?.county === loc.county && (
                      <span className="ml-2 text-xs bg-blue-200 text-blue-700 px-1.5 py-0.5 rounded-full">Active</span>
                    )}
                  </span>
                </div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveLocation(index);
                  }}
                  className="text-gray-500 hover:text-red-600 hover:bg-red-50 h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
        
        {!customLocation && (
          <div className="mb-4">
            <Button 
              onClick={() => setLocationDialogOpen(true)} 
              variant="outline" 
              className="w-full flex items-center justify-center gap-2 bg-forest/5 border-forest/20 text-forest hover:bg-forest/10"
            >
              <MapPin className="h-4 w-4" />
              View Reports by Location
            </Button>
          </div>
        )}
        
        <Tabs defaultValue={defaultTab}>
          <TabsList className="w-full grid grid-cols-3 bg-gradient-to-r from-forest/10 to-sky-light/10">
            <TabsTrigger value="all" className="data-[state=active]:bg-forest/20 data-[state=active]:text-forest">All</TabsTrigger>
            <TabsTrigger value="mentions" className="data-[state=active]:bg-forest/20 data-[state=active]:text-forest">Mentions</TabsTrigger>
            <TabsTrigger value="comments" className="data-[state=active]:bg-forest/20 data-[state=active]:text-forest">Comments</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="mt-4">
            {loading ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Loading activities...</p>
              </div>
            ) : activities.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  {activeLocation
                    ? `No activity found for ${activeLocation.county} County, ${activeLocation.state}`
                    : "No activity yet"
                  }
                </p>
              </div>
            ) : (
              activities.map(activity => (
                <ActivityItem key={activity.id} activity={activity} onClick={handleActivityClick} />
              ))
            )}
          </TabsContent>
          
          <TabsContent value="mentions" className="mt-4">
            {activities.filter(a => a.type === "mention").length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  {activeLocation
                    ? `No mentions found for ${activeLocation.county} County, ${activeLocation.state}` 
                    : "No mentions yet"
                  }
                </p>
              </div>
            ) : (
              activities.filter(a => a.type === "mention").map(activity => (
                <ActivityItem key={activity.id} activity={activity} onClick={handleActivityClick} />
              ))
            )}
          </TabsContent>
          
          <TabsContent value="comments" className="mt-4">
            {activities.filter(a => a.type === "comment").length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  {activeLocation
                    ? `No comments found for ${activeLocation.county} County, ${activeLocation.state}` 
                    : "No comments yet"
                  }
                </p>
              </div>
            ) : (
              activities.filter(a => a.type === "comment").map(activity => (
                <ActivityItem key={activity.id} activity={activity} onClick={handleActivityClick} />
              ))
            )}
          </TabsContent>
        </Tabs>
      </div>
      
      <LocationDialog 
        open={locationDialogOpen}
        onOpenChange={setLocationDialogOpen}
        onLocationChange={handleAddLocation}
      />
      
      <MainNavigation />
    </div>
  );
};

export default Activity;
