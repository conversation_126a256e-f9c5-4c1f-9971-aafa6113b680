import { useState, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { ArrowLeft, Send } from "lucide-react";
import { Avatar } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import VideoPlayer from "@/components/VideoPlayer";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import CommentLikeButton from "@/components/video/CommentLikeButton";

interface CommentType {
  id: string;
  text: string;
  username: string;
  user_avatar: string | null;
  created_at: string;
  user_id: string;
  like_count: number;
}

const Comments = () => {
  const { videoId } = useParams<{ videoId: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  
  const [comments, setComments] = useState<CommentType[]>([]);
  const [newComment, setNewComment] = useState("");
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  
  // Extract video data from location state or fetch if not available
  const videoData = location.state || {};
  
  useEffect(() => {
    fetchComments();
    
    // Set up realtime subscription for comments
    const channel = supabase
      .channel('public:comments')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'comments',
        filter: `video_id=eq.${videoId}`
      }, (payload) => {
        console.log('Comment change received:', payload);
        
        if (payload.eventType === 'INSERT') {
          // Add new comment to the list
          setComments(prevComments => [payload.new as CommentType, ...prevComments]);
        } else if (payload.eventType === 'DELETE') {
          // Remove deleted comment
          setComments(prevComments => 
            prevComments.filter(comment => comment.id !== payload.old.id)
          );
        } else if (payload.eventType === 'UPDATE') {
          // Update modified comment
          setComments(prevComments => 
            prevComments.map(comment => 
              comment.id === payload.new.id ? payload.new as CommentType : comment
            )
          );
        }
      })
      .subscribe();
      
    return () => {
      supabase.removeChannel(channel);
    };
  }, [videoId]);
  
  const fetchComments = async () => {
    try {
      setLoading(true);
      
      if (!videoId) {
        toast.error("Video ID is missing");
        return;
      }
      
      const { data, error } = await supabase
        .from('comments')
        .select('*')
        .eq('video_id', videoId)
        .order('created_at', { ascending: false });
        
      if (error) {
        throw error;
      }
      
      setComments(data || []);
    } catch (error) {
      console.error("Error fetching comments:", error);
      toast.error("Failed to load comments");
    } finally {
      setLoading(false);
    }
  };
  
  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newComment.trim()) {
      toast.error("Comment cannot be empty");
      return;
    }
    
    if (!user) {
      toast.error("You must be logged in to comment");
      return;
    }
    
    try {
      setSubmitting(true);
      
      const commentData = {
        video_id: videoId,
        user_id: user.id,
        text: newComment.trim(),
        username: profile?.username || user.email?.split('@')[0] || 'Anonymous',
        user_avatar: profile?.avatar_url || null
      };
      
      const { error } = await supabase
        .from('comments')
        .insert(commentData);
        
      if (error) {
        throw error;
      }
      
      setNewComment("");
      toast.success("Comment posted successfully");
      
      // Update comment count in the videos table
      const { error: updateError } = await supabase
        .from('videos')
        .update({ comments: (videoData.comments || 0) + 1 })
        .eq('id', videoId);
        
      if (updateError) {
        console.error("Error updating comment count:", updateError);
      }
    } catch (error) {
      console.error("Error posting comment:", error);
      toast.error("Failed to post comment");
    } finally {
      setSubmitting(false);
    }
  };
  
  const handleBack = () => {
    navigate('/');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    }).format(date);
  };
  
  return (
    <div className="bg-black min-h-screen pb-16 text-white">
      <div className="fixed top-0 left-0 right-0 z-50 bg-black/90 backdrop-blur-sm p-4 border-b border-gray-800">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
            <ArrowLeft className="text-white" />
          </Button>
          <h1 className="text-lg font-medium">Comments</h1>
        </div>
      </div>
      
      <div className="pt-16 px-4">
        {videoData.videoUrl && (
          <div className="mb-4 mt-2">
            <div className="h-[200px] w-full relative rounded-lg overflow-hidden">
              <VideoPlayer url={videoData.videoUrl} />
            </div>
          </div>
        )}
        
        {videoData.username && videoData.caption && (
          <div className="mb-6">
            <div className="flex items-center mb-2">
              <Avatar className="w-8 h-8 mr-2">
                <img src={videoData.userAvatar} alt={videoData.username} className="object-cover" />
              </Avatar>
              <span className="font-bold">@{videoData.username}</span>
            </div>
            <p className="text-sm text-gray-300">{videoData.caption}</p>
          </div>
        )}
        
        {user ? (
          <form onSubmit={handleSubmitComment} className="mb-6">
            <div className="flex items-start gap-2">
              <Avatar className="w-8 h-8">
                <img 
                  src={profile?.avatar_url || "https://ui-avatars.com/api/?name=" + (user.email || "User")} 
                  alt={profile?.username || user.email || "User"} 
                  className="object-cover" 
                />
              </Avatar>
              <div className="flex-1">
                <Textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Add a comment..."
                  className="resize-none bg-gray-900 border-gray-800 focus:border-gray-700 text-white"
                  rows={2}
                />
              </div>
              <Button 
                type="submit" 
                size="icon" 
                disabled={submitting || !newComment.trim()}
                className="mt-1"
              >
                <Send size={18} />
              </Button>
            </div>
          </form>
        ) : (
          <div className="bg-gray-900 p-4 rounded-lg mb-6 text-center">
            <p className="text-gray-300 mb-2">Sign in to comment on this video</p>
            <Button onClick={() => navigate("/auth")} variant="secondary">
              Sign In
            </Button>
          </div>
        )}
        
        <div className="comments-section space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <p className="text-gray-400">Loading comments...</p>
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">No comments yet. Be the first to comment!</p>
            </div>
          ) : (
            comments.map((comment) => (
              <div key={comment.id} className="flex gap-3 pb-4 border-b border-gray-800">
                <Avatar className="w-8 h-8">
                  <img 
                    src={comment.user_avatar || "https://ui-avatars.com/api/?name=" + comment.username} 
                    alt={comment.username} 
                    className="object-cover" 
                  />
                </Avatar>
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <span className="font-bold text-sm">@{comment.username}</span>
                    <span className="text-xs text-gray-400">{formatDate(comment.created_at)}</span>
                  </div>
                  <p className="text-sm mt-1 text-gray-200">{comment.text}</p>
                  <div className="flex items-center mt-2">
                    <CommentLikeButton commentId={comment.id} initialLikes={comment.like_count || 0} />
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default Comments;
