
import { useEffect } from "react";
import ShotAnalyzerComponent from "@/components/shot-analyzer/ShotAnalyzer";
import MainNavigation from "@/components/MainNavigation";
import Header from "@/components/Header";

const ShotAnalyzerPage = () => {
  // Scroll to top when page loads
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="pb-16 bg-background min-h-screen">
      <Header />
      <div className="px-3 sm:px-4 pt-16 sm:pt-20 pb-2">
        <h1 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">Shot Analyzer</h1>
        <ShotAnalyzerComponent />
      </div>
      <MainNavigation />
    </div>
  );
};

export default ShotAnalyzerPage;
