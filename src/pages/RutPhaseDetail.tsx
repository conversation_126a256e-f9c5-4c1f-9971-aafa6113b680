
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import MainNavigation from "@/components/MainNavigation";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";

interface PhaseDetail {
  name: string;
  dates: string;
  description: string;
  longDescription: string;
  bestAreas: string[];
  bestMethods: string[];
  bucks: string;
  does: string;
  tips: string[];
  icon: string;
}

const phaseDetails: Record<string, PhaseDetail> = {
  "pre-rut": {
    name: "Pre-Rut",
    dates: "Oct 15 - Oct 24", 
    description: "Bucks establishing territories with scrapes and rubs",
    longDescription: "During the Pre-Rut phase, bucks begin to break away from bachelor groups and establish territories. They create rubs and scrapes to mark their territories and communicate with other deer. Testosterone levels are rising, but most does are not yet in estrus.",
    bestAreas: [
      "Transition corridors between bedding and feeding areas",
      "Areas with fresh rub lines and scrapes",
      "Field edges and food sources with good visibility",
      "Oak stands with falling acorns"
    ],
    bestMethods: [
      "Scout for fresh sign and set up along rub lines",
      "Light rattling sequences in the morning",
      "Use grunt calls sparingly",
      "Focus on evening hunts near food sources"
    ],
    bucks: "Bucks are beginning to establish dominance patterns and territories, making fresh rubs and scrapes. They remain somewhat predictable in their feeding patterns but are becoming more active during daylight hours.",
    does: "Does are still in family groups and following regular feeding patterns. They are not yet in estrus but may visit scrapes occasionally.",
    tips: [
      "Place cameras on fresh scrapes to pattern buck movement",
      "Pay attention to wind direction when setting up stands",
      "Hunt food sources in the evening and travel corridors in the morning",
      "Start using scent control measures as bucks become more aware"
    ],
    icon: "calendar-check"
  },
  "seeking-phase": {
    name: "Seeking Phase",
    dates: "Oct 25 - Nov 4",
    description: "Bucks actively seeking does, increasing home range",
    longDescription: "The Seeking Phase is characterized by bucks expanding their home range in search of receptive does. Buck activity increases significantly during daylight hours. They are checking scrapes frequently and may begin chasing the first does that come into estrus.",
    bestAreas: [
      "Doe bedding areas and their periphery",
      "Active scrape lines, especially those refreshed daily",
      "Pinch points and funnels between bedding areas",
      "Edge habitats where visibility is good"
    ],
    bestMethods: [
      "All-day sits become productive now",
      "Use grunt calls and rattling sequences",
      "Deploy buck decoys in open areas",
      "Early morning hunts near doe bedding areas"
    ],
    bucks: "Bucks are significantly increasing their movement patterns and expanding their home range up to 50% or more. They're actively searching for the first receptive does and are becoming more aggressive toward other bucks.",
    does: "Most does are not yet in estrus, though some early ones may be. Does may seem nervous and are often looking behind them as bucks begin pursuing them.",
    tips: [
      "This is when hunting pressure can shift deer to nocturnal patterns - hunt smart, not hard",
      "Focus on terrain features that funnel deer movement",
      "Consider using estrous scent sparingly near scrapes",
      "Be prepared for increased daylight buck movement"
    ],
    icon: "search"
  },
  "chasing-phase": {
    name: "Chasing Phase",
    dates: "Nov 5 - Nov 9",
    description: "Intense chasing activity, does beginning to come into estrus",
    longDescription: "The Chasing Phase is often the most visible and exciting part of the rut. Bucks are actively pursuing does that are coming into estrus. Movement increases dramatically during daylight hours, and bucks may chase does relentlessly through open areas.",
    bestAreas: [
      "Open areas where chases can be observed",
      "Doe family group core areas",
      "Funnels and pinch points",
      "Ridge tops where bucks cruise for does"
    ],
    bestMethods: [
      "All-day hunting is essential now",
      "Aggressive calling tactics (grunts, snort-wheezes)",
      "Rattling to simulate buck fights",
      "Use estrous doe bleats"
    ],
    bucks: "Bucks are in peak physical condition but starting to burn energy chasing does. They are highly aggressive and may respond well to calling. Dominant bucks will fight off competitors and may be seen with bloody antlers or wounds.",
    does: "More does are coming into estrus each day. They often run from pursuing bucks until they are fully receptive. Does separate from family groups when in heat.",
    tips: [
      "This is the time to be in the woods all day",
      "Focus on areas with the highest concentration of does",
      "Don't be afraid to call aggressively",
      "Pay attention to does acting nervous or looking behind them"
    ],
    icon: "arrow-up-right"
  },
  "peak-breeding": {
    name: "Peak Breeding",
    dates: "Nov 10 - Nov 18",
    description: "Maximum breeding activity, bucks locked down with does",
    longDescription: "During Peak Breeding, the majority of does come into estrus, and bucks are focused on breeding rather than chasing. This can result in a perceived 'lull' in deer activity as many bucks are 'locked down' with receptive does in thick cover for 24-48 hours at a time.",
    bestAreas: [
      "Thick cover and secure bedding areas",
      "Transition zones between thick cover and food",
      "Isolated doe bedding areas",
      "Secondary food sources"
    ],
    bestMethods: [
      "Hunt close to doe bedding areas",
      "Use estrous doe bleats and soft grunts",
      "Be patient - movement can be sporadic",
      "Focus on mid-day movement"
    ],
    bucks: "Bucks are with receptive does for 24-48 hour periods, often in thick cover. When not with a doe, they are aggressively searching for the next receptive one. They may appear suddenly at any time of day.",
    does: "Most adult does are in estrus during this period and will be receptive to bucks. They isolate themselves from their family groups while breeding.",
    tips: [
      "This can be a challenging time as deer movement seems to decrease",
      "Focus on thick cover where deer retreat to breed",
      "Hunt all day as bucks move at random times seeking new does",
      "Look for lone does - a buck may be nearby"
    ],
    icon: "target"
  },
  "post-rut": {
    name: "Post-Rut",
    dates: "Nov 19 - Nov 30",
    description: "Breeding activity declining, bucks recovering",
    longDescription: "The Post-Rut phase is characterized by declining breeding activity as most does have been bred. Bucks are fatigued from the rut and begin to focus on feeding to recover energy reserves. Some young does may still come into estrus for the first time.",
    bestAreas: [
      "Primary food sources (agricultural fields, food plots)",
      "Transition corridors between bedding and feeding",
      "Areas with late-dropping mast crops",
      "Thermal cover during cold snaps"
    ],
    bestMethods: [
      "Focus on evening hunts near food sources",
      "Continue rattling but with less intensity",
      "Use doe bleats to attract bucks seeking last receptive does",
      "Set up along trails between food and bedding"
    ],
    bucks: "Bucks have lost up to 20-30% of their body weight during the rut. They focus on replenishing fat reserves and may bed close to food sources. They're exhausted but will still respond to doe estrus scent or calls.",
    does: "Most adult does have been bred and return to normal feeding patterns. Young does may be experiencing their first estrus cycle.",
    tips: [
      "Focus on food sources, especially during cold weather",
      "Bucks are physically depleted and need to feed regularly",
      "All-day sits are less necessary - focus on prime time",
      "Pay attention to weather patterns - cold fronts increase deer movement"
    ],
    icon: "arrow-down-right"
  },
  "second-rut": {
    name: "Second Rut",
    dates: "Dec 5 - Dec 15",
    description: "Unfertilized does return to estrus, secondary breeding period",
    longDescription: "The Second Rut occurs approximately 28 days after the peak rut when does that were not successfully bred in their first cycle come back into estrus. Additionally, many doe fawns reach sexual maturity and have their first estrus cycle during this time.",
    bestAreas: [
      "Late-season food sources (winter wheat, brassicas)",
      "Areas with high concentrations of doe fawns",
      "Protected areas with thermal cover",
      "Isolated food sources with less hunting pressure"
    ],
    bestMethods: [
      "Return to hunting food sources",
      "Use estrous doe bleats and light rattling",
      "All-day sits during peak days",
      "Focus on warmer days and weather fronts"
    ],
    bucks: "Bucks have recovered some energy and will respond to receptive does, though with less enthusiasm than during the primary rut. Secondary bucks may be more active now that dominant bucks are fatigued.",
    does: "Unbred adult does return to estrus approximately 28 days after the peak rut. Many doe fawns (6-7 months old) experience their first estrus cycle.",
    tips: [
      "Look for areas with high doe fawn concentrations",
      "The second rut is much less intense but can still produce good hunting",
      "Pay close attention to trail camera data to spot renewed rutting activity",
      "Once again, weather plays a crucial role - hunt cold fronts"
    ],
    icon: "repeat"
  }
};

const RutPhaseDetail = () => {
  const { phase } = useParams<{ phase: string }>();
  const [phaseInfo, setPhaseInfo] = useState<PhaseDetail | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (phase && phaseDetails[phase]) {
      setPhaseInfo(phaseDetails[phase]);
    }
  }, [phase]);

  // Function to handle back button click
  const handleBackClick = () => {
    // Navigate back to rut tracker with the predict tab active
    // This uses the same URL that RutTracker component now looks for
    navigate("/rut-tracker?tab=predict");
  };

  if (!phaseInfo) {
    return (
      <div className="bg-background min-h-screen">
        <Header />
        <div className="container px-4 py-8 pt-16">
          <div className="flex items-center mb-4">
            <Button variant="outline" size="sm" className="mr-2" onClick={handleBackClick}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
            <h1 className="text-xl font-bold">Phase Not Found</h1>
          </div>
          <p>The rut phase you're looking for doesn't exist.</p>
        </div>
        <MainNavigation />
      </div>
    );
  }

  return (
    <div className="bg-background min-h-screen pb-20">
      <Header />
      <div className="container px-4 pt-16">
        {/* Back button - positioned below header */}
        <div className="mb-4">
          <Button 
            variant="default" 
            size="sm" 
            className="bg-forest hover:bg-forest/90 text-white flex items-center gap-1"
            onClick={handleBackClick}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Rut Tracker
          </Button>
        </div>

        <div className="flex items-center mb-4">
          <h1 className="text-xl font-bold text-forest">{phaseInfo.name}</h1>
          <span className="ml-2 text-sm bg-forest/10 text-forest px-3 py-1 rounded-full">
            {phaseInfo.dates}
          </span>
        </div>

        <div className="space-y-4">
          {/* Overview */}
          <Card>
            <CardContent className="pt-6">
              <h2 className="text-lg font-medium mb-2">Overview</h2>
              <p className="text-gray-700">{phaseInfo.longDescription}</p>
            </CardContent>
          </Card>

          {/* Best Hunting Areas */}
          <Card>
            <CardContent className="pt-6">
              <h2 className="text-lg font-medium mb-2">Best Hunting Areas</h2>
              <ul className="list-disc pl-5 space-y-2">
                {phaseInfo.bestAreas.map((area, i) => (
                  <li key={i} className="text-gray-700">{area}</li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Best Hunting Methods */}
          <Card>
            <CardContent className="pt-6">
              <h2 className="text-lg font-medium mb-2">Best Hunting Methods</h2>
              <ul className="list-disc pl-5 space-y-2">
                {phaseInfo.bestMethods.map((method, i) => (
                  <li key={i} className="text-gray-700">{method}</li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Deer Behavior */}
          <Card>
            <CardContent className="pt-6">
              <h2 className="text-lg font-medium mb-2">Deer Behavior</h2>
              
              <div className="mb-4">
                <h3 className="font-medium text-forest mb-1">Buck Behavior</h3>
                <p className="text-gray-700">{phaseInfo.bucks}</p>
              </div>
              
              <div>
                <h3 className="font-medium text-forest mb-1">Doe Behavior</h3>
                <p className="text-gray-700">{phaseInfo.does}</p>
              </div>
            </CardContent>
          </Card>

          {/* Pro Tips */}
          <Card>
            <CardContent className="pt-6">
              <h2 className="text-lg font-medium mb-2">Pro Tips</h2>
              <ul className="list-disc pl-5 space-y-2">
                {phaseInfo.tips.map((tip, i) => (
                  <li key={i} className="text-gray-700">{tip}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
      <MainNavigation />
    </div>
  );
};

export default RutPhaseDetail;
