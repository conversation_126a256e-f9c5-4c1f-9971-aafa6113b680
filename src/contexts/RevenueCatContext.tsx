import React, { createContext, useContext, useEffect } from 'react';
import { Capacitor } from '@capacitor/core';
import { useRevenueCat, RevenueCatState, RevenueCatProduct } from '@/hooks/useRevenueCat';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface RevenueCatContextType extends RevenueCatState {
  purchaseAnnualSubscription: () => Promise<boolean>;
  restorePurchases: () => Promise<boolean>;
  getProductInfo: () => RevenueCatProduct | null;
  reinitialize: () => Promise<void>;
  isNativePlatform: boolean;
  syncSubscriptionStatus: () => Promise<void>;
}

const RevenueCatContext = createContext<RevenueCatContextType | undefined>(undefined);

interface RevenueCatProviderProps {
  children: React.ReactNode;
}

export const RevenueCatProvider: React.FC<RevenueCatProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const revenueCatHook = useRevenueCat();
  const isNativePlatform = Capacitor.isNativePlatform();

  // Sync subscription status with Supabase
  const syncSubscriptionStatus = async () => {
    if (!user || !isNativePlatform || !revenueCatHook.isSubscribed) {
      return;
    }

    try {
      console.log('Syncing subscription status with Supabase...');
      
      // Calculate promotion end date (1 year from now)
      const promotedUntil = new Date();
      promotedUntil.setFullYear(promotedUntil.getFullYear() + 1);

      // Update user profile with promotion status
      const { error } = await supabase
        .from('profiles')
        .update({
          is_promoted: true,
          promoted_until: promotedUntil.toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) {
        console.error('Error syncing subscription status:', error);
        toast.error('Failed to sync subscription status');
      } else {
        console.log('Subscription status synced successfully');
        toast.success('Subscription activated and synced!');
      }
    } catch (error) {
      console.error('Error in syncSubscriptionStatus:', error);
      toast.error('Failed to sync subscription status');
    }
  };

  // Enhanced purchase function with Supabase sync
  const purchaseAnnualSubscription = async (): Promise<boolean> => {
    const success = await revenueCatHook.purchaseAnnualSubscription();
    
    if (success) {
      // Sync with Supabase after successful purchase
      await syncSubscriptionStatus();
    }
    
    return success;
  };

  // Enhanced restore function with Supabase sync
  const restorePurchases = async (): Promise<boolean> => {
    const success = await revenueCatHook.restorePurchases();
    
    if (success) {
      // Sync with Supabase after successful restore
      await syncSubscriptionStatus();
    }
    
    return success;
  };

  // Auto-sync subscription status when customer info changes
  useEffect(() => {
    if (revenueCatHook.isSubscribed && user && isNativePlatform) {
      syncSubscriptionStatus();
    }
  }, [revenueCatHook.isSubscribed, user?.id, isNativePlatform]);

  const contextValue: RevenueCatContextType = {
    ...revenueCatHook,
    purchaseAnnualSubscription,
    restorePurchases,
    isNativePlatform,
    syncSubscriptionStatus,
  };

  return (
    <RevenueCatContext.Provider value={contextValue}>
      {children}
    </RevenueCatContext.Provider>
  );
};

export const useRevenueCatContext = () => {
  const context = useContext(RevenueCatContext);
  if (context === undefined) {
    throw new Error('useRevenueCatContext must be used within a RevenueCatProvider');
  }
  return context;
};

// Utility function to check if user should see RevenueCat or web checkout
export const shouldUseRevenueCat = (): boolean => {
  return Capacitor.isNativePlatform();
};

// Utility function to get platform-specific pricing
export const getPlatformPricing = (revenueCatProduct?: RevenueCatProduct | null) => {
  if (shouldUseRevenueCat() && revenueCatProduct) {
    return {
      price: revenueCatProduct.priceString,
      originalPrice: revenueCatProduct.originalPrice || '$199.00',
      discountPercentage: revenueCatProduct.discountPercentage || 50,
      currencyCode: revenueCatProduct.currencyCode,
    };
  }

  // Web fallback pricing
  return {
    price: '$99.00',
    originalPrice: '$199.00',
    discountPercentage: 50,
    currencyCode: 'USD',
  };
};
