import React, { createContext, useState, useEffect, useContext } from "react";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

// Default profile avatar - deer antler image
const DEFAULT_AVATAR = "/lovable-uploads/1065063f-bbc7-4cf2-a4e9-c3958e021bef.png";

type ProfileData = {
  username: string;
  bio: string;
  avatar_url: string;
  region?: string | null;
  badges?: string[] | null;
  video_count?: number; // Add video_count to ProfileData
  is_premium?: boolean; // Add is_premium to ProfileData
  is_promoted?: boolean; // Add is_promoted to ProfileData
  promoted_until?: string | null; // Add promoted_until to ProfileData
};

type AuthContextType = {
  session: Session | null;
  user: User | null;
  profile: ProfileData | null;
  loading: boolean;
  signUp: (email: string, password: string, username: string, inviteCode?: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  checkUsernameAvailable: (username: string) => Promise<boolean>;
  updateProfile: (profileData: Partial<ProfileData>) => Promise<void>;
  updateEmail: (newEmail: string) => Promise<void>;
  refreshProfile: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error("Error fetching profile:", error);
        return;
      }

      if (data) {
        setProfile({
          username: data.username || '',
          bio: data.bio || 'Whitetail Livn User',
          avatar_url: data.avatar_url || DEFAULT_AVATAR,
          region: data.region || null,
          badges: data.badges || null,
          video_count: data.video_count || 0, // Include video_count
          is_premium: data.is_premium || false, // Include is_premium
          is_promoted: data.is_promoted || false, // Include is_promoted
          promoted_until: data.promoted_until || null, // Include promoted_until
        });
      }
    } catch (error) {
      console.error("Failed to fetch profile:", error);
    }
  };

  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log("Auth state changed:", event, session);
        setSession(session);
        setUser(session?.user ?? null);
        
        if (session?.user) {
          fetchProfile(session.user.id);
        } else {
          setProfile(null);
        }
        
        setLoading(false);
      }
    );

    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        fetchProfile(session.user.id);
      }
      
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkUsernameAvailable = async (username: string): Promise<boolean> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('username')
        .eq('username', username)
        .maybeSingle();

      if (error) {
        console.error("Error checking username:", error);
        throw error;
      }

      // If data exists, username is taken
      return !data;
    } catch (error) {
      console.error("Failed to check username availability:", error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string, username: string, inviteCode?: string) => {
    try {
      // First check if username is available
      const isAvailable = await checkUsernameAvailable(username);
      if (!isAvailable) {
        toast.error("Username is already taken. Please choose a different one.");
        throw new Error("Username is already taken");
      }

      // Include the invite code in user metadata if provided
      const userData = {
        username,
        avatar_url: DEFAULT_AVATAR,
        bio: 'Whitetail Livn User',
        ...(inviteCode && { invite_code: inviteCode })
      };

      // Verify password strength before signup
      if (password.length < 6) {
        toast.error("Password must be at least 6 characters long");
        throw new Error("Password too short");
      }

      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      });

      if (error) throw error;
      
      toast.success("Sign up successful! Please check your email for verification.");
    } catch (error: any) {
      toast.error(error.message || "An error occurred during sign up");
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;
      toast.success("Signed in successfully!");
    } catch (error: any) {
      toast.error(error.message || "Invalid login credentials");
      throw error;
    }
  };

  const signOut = async () => {
    try {
      console.log("Starting sign out process...");
      
      // Clear local state first
      setSession(null);
      setUser(null);
      setProfile(null);
      
      // Then sign out from Supabase
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error("Supabase sign out error:", error);
        // Even if there's an error, we've cleared local state
        // This prevents users from being stuck in a signed-in state
        toast.info("Signed out (with minor issues)");
      } else {
        console.log("Sign out successful");
        toast.info("Signed out successfully");
      }
    } catch (error: any) {
      console.error("Sign out error:", error);
      // Clear local state even if there's an error
      setSession(null);
      setUser(null);
      setProfile(null);
      toast.info("Signed out (local session cleared)");
    }
  };

  const resetPassword = async (email: string) => {
    try {
      // Use a more reliable redirect URL
      const redirectTo = window.location.hostname === 'localhost' 
        ? `${window.location.origin}/auth?reset=true`
        : `https://${window.location.host}/auth?reset=true`;
        
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo,
      });
      
      if (error) throw error;
      // Don't show success message here - we don't want to reveal if an email exists
      // Just let the component handle the UI feedback
    } catch (error: any) {
      console.error("Reset password error:", error);
      throw error;
    }
  };

  const updateProfile = async (profileData: Partial<ProfileData>): Promise<void> => {
    try {
      if (!user) {
        toast.error("You must be logged in to update your profile");
        throw new Error("User not authenticated");
      }

      const { error } = await supabase
        .from('profiles')
        .update(profileData)
        .eq('id', user.id);

      if (error) {
        console.error("Error updating profile:", error);
        toast.error(`Failed to update profile: ${error.message}`);
        throw error;
      }

      // Update local profile state
      if (profile) {
        setProfile({
          ...profile,
          ...profileData
        });
      }

      toast.success("Profile updated successfully");
    } catch (error: any) {
      console.error("Profile update error:", error);
      throw error;
    }
  };

  const updateEmail = async (newEmail: string): Promise<void> => {
    try {
      if (!user) {
        toast.error("You must be logged in to update your email");
        throw new Error("User not authenticated");
      }

      // Don't update if the email is the same
      if (user.email === newEmail) {
        return;
      }

      const { error } = await supabase.auth.updateUser({
        email: newEmail
      });

      if (error) {
        console.error("Error updating email:", error);
        toast.error(`Failed to update email: ${error.message}`);
        throw error;
      }

      toast.success("Email update initiated. Please check your new email for verification.");
    } catch (error: any) {
      console.error("Email update error:", error);
      throw error;
    }
  };

  const refreshProfile = async (): Promise<void> => {
    try {
      if (!user) {
        throw new Error("User not authenticated");
      }
      
      await fetchProfile(user.id);
    } catch (error: any) {
      console.error("Profile refresh error:", error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        profile,
        loading,
        signUp,
        signIn,
        signOut,
        resetPassword,
        checkUsernameAvailable,
        updateProfile,
        updateEmail,
        refreshProfile
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
