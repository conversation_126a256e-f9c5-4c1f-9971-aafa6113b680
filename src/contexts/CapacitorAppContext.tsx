import React, { createContext, useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCapacitorApp } from '@/hooks/useCapacitorApp';
import { parseUrlScheme, getNavigationPath, logUrlSchemeHandling, generateUrlSchemes } from '@/utils/urlSchemeHelper';
import { toast } from 'sonner';

interface CapacitorAppContextType {
  isNative: boolean;
  exitApp: () => Promise<void>;
  getAppInfo: () => Promise<any>;
  getAppState: () => Promise<any>;
  isAppActive: boolean;
}

const CapacitorAppContext = createContext<CapacitorAppContextType | undefined>(undefined);

interface CapacitorAppProviderProps {
  children: React.ReactNode;
}

export const CapacitorAppProvider: React.FC<CapacitorAppProviderProps> = ({ children }) => {
  const navigate = useNavigate();
  const [isAppActive, setIsAppActive] = useState(true);

  // Handle URL opening with navigation
  const handleUrlOpen = (url: string) => {
    try {
      console.log('Handling URL open:', url);

      // Check for empty or invalid URL
      if (!url || url.trim() === '') {
        console.warn('Empty URL provided, navigating to home');
        navigate('/');
        toast.info('Welcome back!');
        return;
      }

      const deepLinkData = parseUrlScheme(url);

      if (!deepLinkData) {
        throw new Error('Invalid URL format');
      }

      const navigationPath = getNavigationPath(deepLinkData);

      // Check if navigation path is empty or invalid
      if (!navigationPath || navigationPath.trim() === '') {
        console.warn('Empty navigation path, defaulting to home');
        navigate('/');
        toast.info('Welcome back!');
        return;
      }

      // Navigate to the appropriate path
      navigate(navigationPath);

      // Show appropriate toast message
      const messages = {
        video: 'Opening video...',
        profile: 'Opening profile...',
        discover: 'Opening discover...',
        create: 'Opening create...',
        activity: 'Opening activity...',
        home: 'Welcome back!'
      };

      toast.success(messages[deepLinkData.type] || 'Opening...');

      // Log successful handling
      logUrlSchemeHandling(url, true);

    } catch (error) {
      console.error('Error handling URL open:', error);

      // Log failed handling
      logUrlSchemeHandling(url, false, error instanceof Error ? error.message : 'Unknown error');

      // Fallback to home page
      navigate('/');
      toast.error('Error opening link, redirected to home');
    }
  };

  // Handle app state changes
  const handleAppStateChange = (isActive: boolean) => {
    setIsAppActive(isActive);
    
    if (isActive) {
      console.log('App became active');
      // You can add logic here for when app becomes active
      // e.g., refresh data, resume video playback, etc.
    } else {
      console.log('App went to background');
      // You can add logic here for when app goes to background
      // e.g., pause video playback, save state, etc.
    }
  };

  const { exitApp, getAppInfo, getAppState, isNative } = useCapacitorApp({
    enableBackButtonHandler: true,
    backButtonExitDelay: 2000,
    onUrlOpen: handleUrlOpen,
    onAppStateChange: handleAppStateChange,
  });

  const contextValue: CapacitorAppContextType = {
    isNative,
    exitApp,
    getAppInfo,
    getAppState,
    isAppActive,
  };

  return (
    <CapacitorAppContext.Provider value={contextValue}>
      {children}
    </CapacitorAppContext.Provider>
  );
};

export const useCapacitorAppContext = () => {
  const context = useContext(CapacitorAppContext);
  if (context === undefined) {
    throw new Error('useCapacitorAppContext must be used within a CapacitorAppProvider');
  }
  return context;
};

// Utility functions for generating shareable URLs
export const generateShareableUrl = (type: 'video' | 'profile', id: string) => {
  return generateUrlSchemes({
    type: type as 'video' | 'profile',
    id
  });
};

// Utility function to share content with proper URL schemes
export const shareContent = async (
  type: 'video' | 'profile',
  id: string,
  title: string,
  text?: string
) => {
  const urls = generateShareableUrl(type, id);

  if (navigator.share) {
    try {
      await navigator.share({
        title,
        text: text || title,
        url: urls.web, // Always use web URL for sharing
      });
      return true;
    } catch (error) {
      console.error('Error sharing:', error);
      return false;
    }
  } else {
    // Fallback to clipboard
    try {
      await navigator.clipboard.writeText(urls.web);
      toast.success('Link copied to clipboard!');
      return true;
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast.error('Failed to copy link');
      return false;
    }
  }
};
