
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { CapacitorAppProvider } from "@/contexts/CapacitorAppContext";
import { RevenueCatProvider } from "@/contexts/RevenueCatContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import Index from "./pages/Index";
import Discover from "./pages/Discover";
import Create from "./pages/Create";
import Inbox from "./pages/Inbox";
import Profile from "./pages/Profile";
import UserProfile from "./pages/UserProfile";
import ServicesDirectory from "./pages/ServicesDirectory";
import Comments from "./pages/Comments";
import Auth from "./pages/Auth";
import NotFound from "./pages/NotFound";
import RutTracker from "./pages/RutTracker";
import RutPhaseDetail from "./pages/RutPhaseDetail";
import AdminPanel from "./pages/AdminPanel";
import Activity from "./pages/Activity";
import ShotAnalyzer from "./pages/ShotAnalyzer";
import HuntingAI from "./pages/HuntingAI";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfService from "./pages/TermsOfService";
import VideoView from "./pages/VideoView";
import RevenueCatTest from "./components/RevenueCatTest";
import { StrictMode, useEffect } from "react";


// Create QueryClient inside the component
const App = () => {
  // Move queryClient inside the component to ensure React context is available
  const queryClient = new QueryClient();
  
  return (
    <StrictMode>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <CapacitorAppProvider>
              <AuthProvider>
                <RevenueCatProvider>
                    <Routes>
                  {/* Public routes */}
                  <Route path="/auth" element={<Auth />} />
                  <Route path="/comments/:videoId" element={<Comments />} />
                  <Route path="/user/:userId" element={<UserProfile />} />
                  <Route path="/privacy" element={<PrivacyPolicy />} />
                  <Route path="/terms" element={<TermsOfService />} />
                  <Route path="/video/:videoId" element={<VideoView />} />
                  
                  {/* Protected routes */}
                  <Route element={<ProtectedRoute />}>
                    <Route path="/" element={<Index />} />
                    <Route path="/discover" element={<Discover />} />
                    <Route path="/create" element={<Create />} />
                    <Route path="/inbox" element={<Inbox />} />
                    <Route path="/services" element={<ServicesDirectory />} />
                    <Route path="/profile" element={<Profile />} />
                    <Route path="/rut-tracker" element={<RutTracker />} />
                    <Route path="/rut-tracker/phase/:phase" element={<RutPhaseDetail />} />
                    <Route path="/activity" element={<Activity />} />
                    <Route path="/admin" element={<AdminPanel />} />
                    <Route path="/shot-analyzer" element={<ShotAnalyzer />} />
                    <Route path="/hunting-ai" element={<HuntingAI />} />
                    <Route path="/revenuecat-test" element={<RevenueCatTest />} />
                  </Route>
                  
                  <Route path="*" element={<NotFound />} />
                  </Routes>
                </RevenueCatProvider>
              </AuthProvider>
            </CapacitorAppProvider>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </StrictMode>
  );
};

export default App;
