import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

import { useState, useEffect } from 'react';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function useScreenRatio() {
  const [screenRatio, setScreenRatio] = useState(0);

  useEffect(() => {
    const updateScreenRatio = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      setScreenRatio(width / height);
    };

    // Set initial ratio
    updateScreenRatio();

    // Add event listener for window resize
    window.addEventListener('resize', updateScreenRatio);

    // Clean up event listener on component unmount
    return () => window.removeEventListener('resize', updateScreenRatio);
  }, []); // Empty dependency array ensures effect runs only once on mount

  return screenRatio.toFixed(2);
}
