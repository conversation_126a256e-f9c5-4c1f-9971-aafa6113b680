
import { supabase } from "@/integrations/supabase/client";

/**
 * Client-side encryption utilities that use the Supabase encryption edge function
 */

/**
 * Encrypt sensitive data before storing in the database
 * @param data The plaintext data to encrypt
 * @returns The encrypted data as a string
 */
export async function encryptData(data: string): Promise<string | null> {
  try {
    const { data: result, error } = await supabase.functions.invoke("encrypt-data", {
      body: { action: "encrypt", data }
    });

    if (error) {
      console.error("Encryption failed:", error);
      return null;
    }

    return result?.result || null;
  } catch (err) {
    console.error("Failed to encrypt data:", err);
    return null;
  }
}

/**
 * Decrypt sensitive data retrieved from the database
 * @param encryptedData The encrypted data string
 * @returns The decrypted plaintext data
 */
export async function decryptData(encryptedData: string): Promise<string | null> {
  try {
    const { data: result, error } = await supabase.functions.invoke("encrypt-data", {
      body: { action: "decrypt", data: encryptedData }
    });

    if (error) {
      console.error("Decryption failed:", error);
      return null;
    }

    return result?.result || null;
  } catch (err) {
    console.error("Failed to decrypt data:", err);
    return null;
  }
}

/**
 * Encrypt an object's sensitive fields
 * @param data The object containing sensitive data
 * @param sensitiveFields Array of field names that should be encrypted
 * @returns A new object with sensitive fields encrypted
 */
export async function encryptObject<T extends Record<string, any>>(
  data: T, 
  sensitiveFields: string[]
): Promise<Partial<T>> {
  const result = { ...data } as Partial<T>;
  
  for (const field of sensitiveFields) {
    if (field in data && typeof data[field as keyof T] === 'string') {
      const encryptedValue = await encryptData(data[field as keyof T] as string);
      if (encryptedValue !== null) {
        result[field as keyof T] = encryptedValue as any;
      }
    }
  }
  
  return result;
}

/**
 * Decrypt an object's encrypted fields
 * @param data The object containing encrypted data
 * @param encryptedFields Array of field names that are encrypted
 * @returns A new object with fields decrypted
 */
export async function decryptObject<T extends Record<string, any>>(
  data: T, 
  encryptedFields: string[]
): Promise<Partial<T>> {
  const result = { ...data } as Partial<T>;
  
  for (const field of encryptedFields) {
    if (field in data && typeof data[field as keyof T] === 'string') {
      const decryptedValue = await decryptData(data[field as keyof T] as string);
      if (decryptedValue !== null) {
        result[field as keyof T] = decryptedValue as any;
      }
    }
  }
  
  return result;
}
