
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface WhitetailScoreData {
  videoCount: number;
  tier: string;
  loading: boolean;
}

export const useWhitetailScore = (userId?: string) => {
  const { user } = useAuth();
  const [scoreData, setScoreData] = useState<WhitetailScoreData>({
    videoCount: 0,
    tier: 'Fawn',
    loading: true
  });

  const targetUserId = userId || user?.id;

  useEffect(() => {
    const fetchWhitetailScore = async () => {
      if (!targetUserId) {
        setScoreData(prev => ({ ...prev, loading: false }));
        return;
      }

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('video_count')
          .eq('id', targetUserId)
          .single();

        if (error) {
          console.error('Error fetching video count:', error);
          return;
        }

        const videoCount = data?.video_count || 0;
        const tier = getWhitetailTier(videoCount);
        
        setScoreData({
          videoCount,
          tier,
          loading: false
        });
      } catch (error) {
        console.error('Error in fetchWhitetailScore:', error);
        setScoreData(prev => ({ ...prev, loading: false }));
      }
    };

    fetchWhitetailScore();

    // Listen for real-time updates to video count
    if (targetUserId) {
      const channel = supabase
        .channel('whitetail-score-updates')
        .on('postgres_changes', {
          event: 'UPDATE',
          schema: 'public',
          table: 'profiles',
          filter: `id=eq.${targetUserId}`
        }, (payload) => {
          if (payload.new?.video_count !== undefined) {
            const videoCount = payload.new.video_count;
            const tier = getWhitetailTier(videoCount);
            setScoreData({
              videoCount,
              tier,
              loading: false
            });
          }
        })
        .subscribe();

      return () => {
        supabase.removeChannel(channel);
      };
    }
  }, [targetUserId]);

  return scoreData;
};

const getWhitetailTier = (videoCount: number): string => {
  if (videoCount >= 31) return 'Trophy';
  if (videoCount >= 16) return '8-Pointer';
  if (videoCount >= 6) return 'Spike';
  return 'Fawn';
};
