import { useEffect, useRef } from 'react';
import { App } from '@capacitor/app';
import { Capacitor } from '@capacitor/core';
import { SafeArea } from 'capacitor-plugin-safe-area';
import { Device } from "@capacitor/device";
import { toast } from 'sonner';

interface UseCapacitorAppOptions {
  enableBackButtonHandler?: boolean;
  backButtonExitDelay?: number;
  onUrlOpen?: (url: string) => void;
  onAppStateChange?: (isActive: boolean) => void;
}

/**
 * Custom hook for handling Capacitor App plugin functionality
 * - Hardware back button with double-tap to exit
 * - Custom URL scheme handling
 * - App state change monitoring
 */
export const useCapacitorApp = (options: UseCapacitorAppOptions = {}) => {
  const {
    enableBackButtonHandler = true,
    backButtonExitDelay = 2000, // 2 seconds
    onUrlOpen,
    onAppStateChange
  } = options;

  const lastBackPressRef = useRef<number>(0);
  const exitToastRef = useRef<string | number | null>(null);

  useEffect(() => {
    // Only run on native platforms
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    let backButtonListener: any;
    let urlOpenListener: any;
    let appStateListener: any;

    const setupListeners = async () => {
      try {
        // Hardware back button handler
        if (enableBackButtonHandler) {
          backButtonListener = await App.addListener('backButton', () => {
            const currentTime = Date.now();
            const timeDifference = currentTime - lastBackPressRef.current;

            // Check if we can navigate back in history
            const canGoBack = window.history.length > 1;
            const currentPath = window.location.pathname;
            const isEmptyOrRootPath = !currentPath || currentPath === '/' || currentPath.trim() === '';

            if (isEmptyOrRootPath && timeDifference < backButtonExitDelay) {
              // Double tap detected - exit app
              if (exitToastRef.current) {
                toast.dismiss(exitToastRef.current);
              }
              App.exitApp();
            } else {
              // First tap - check if we should navigate back or show exit prompt
              if (canGoBack && !isEmptyOrRootPath) {
                // Navigate back in history instead of showing exit prompt
                // window.history.back();
                window.location.replace('/');
                lastBackPressRef.current = 0; // Reset timer since we navigated back

                // Show brief feedback
                toast.info('Navigating back...', {
                  duration: 1000,
                  position: 'bottom-center',
                });
              } else {
                // Show exit prompt for root/empty paths or when no history
                lastBackPressRef.current = currentTime;

                if (exitToastRef.current) {
                  toast.dismiss(exitToastRef.current);
                }

                exitToastRef.current = toast.info(
                  'Press back again to exit app',
                  {
                    duration: backButtonExitDelay,
                    position: 'bottom-center',
                  }
                );
              }
            }
          });
        }

        // URL scheme handler
        urlOpenListener = await App.addListener('appUrlOpen', (event) => {
          console.log('App opened with URL:', event.url);
          
          if (onUrlOpen) {
            onUrlOpen(event.url);
          } else {
            // Default URL handling
            handleDefaultUrlOpen(event.url);
          }
        });

        // App state change handler
        appStateListener = await App.addListener('appStateChange', (state) => {
          console.log('App state changed:', state.isActive ? 'active' : 'background');
          
          if (onAppStateChange) {
            onAppStateChange(state.isActive);
          }
        });

        console.log('Capacitor App listeners initialized');
      } catch (error) {
        console.error('Error setting up Capacitor App listeners:', error);
      }
    };

    const setupSafeArea = async () => {
      const info = await Device.getInfo();
      if (Capacitor.getPlatform() == 'ios' || Capacitor.getPlatform() === 'android' && Number(info.osVersion) >= 15) {
        const safeAreaData = await SafeArea.getSafeAreaInsets();
        const {insets} = safeAreaData;
        for (const [key, value] of Object.entries(insets)) {
            document.documentElement.style.setProperty(
                `--safe-area-inset-${key}`,
                `${value}px`,
            );
        }
      }
    };

    setupListeners();
    setupSafeArea();

    // Cleanup listeners on unmount
    return () => {
      if (backButtonListener) {
        backButtonListener.remove();
      }
      if (urlOpenListener) {
        urlOpenListener.remove();
      }
      if (appStateListener) {
        appStateListener.remove();
      }
      
      if (exitToastRef.current) {
        toast.dismiss(exitToastRef.current);
      }
    };
  }, [enableBackButtonHandler, backButtonExitDelay, onUrlOpen, onAppStateChange]);

  /**
   * Default URL handling logic
   */
  const handleDefaultUrlOpen = (url: string) => {
    try {
      const urlObj = new URL(url);
      const path = urlObj.pathname;
      const params = new URLSearchParams(urlObj.search);

      // Handle different URL schemes
      if (path.startsWith('/video/')) {
        const videoId = path.split('/video/')[1];
        if (videoId) {
          // Navigate to video page
          window.location.href = `/video/${videoId}`;
        }
      } else if (path.startsWith('/profile/')) {
        const userId = path.split('/profile/')[1];
        if (userId) {
          // Navigate to user profile
          window.location.href = `/profile/${userId}`;
        }
      } else if (path === '/discover') {
        // Navigate to discover page
        window.location.href = '/discover';
      } else {
        // Default to home page
        window.location.href = '/';
      }
    } catch (error) {
      console.error('Error parsing URL:', error);
      // Fallback to home page
      window.location.href = '/';
    }
  };

  /**
   * Programmatically exit the app
   */
  const exitApp = async () => {
    if (Capacitor.isNativePlatform()) {
      await App.exitApp();
    }
  };

  /**
   * Get app info
   */
  const getAppInfo = async () => {
    if (Capacitor.isNativePlatform()) {
      return await App.getInfo();
    }
    return null;
  };

  /**
   * Get app state
   */
  const getAppState = async () => {
    if (Capacitor.isNativePlatform()) {
      return await App.getState();
    }
    return null;
  };

  return {
    exitApp,
    getAppInfo,
    getAppState,
    isNative: Capacitor.isNativePlatform()
  };
};
