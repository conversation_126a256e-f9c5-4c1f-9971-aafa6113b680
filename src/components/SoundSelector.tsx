
import { useState, useEffect, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, Play, Pause, Music } from "lucide-react";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SoundItem {
  id: string;
  title: string;
  description: string | null;
  sound_url: string;
  tags: string[];
  category?: string | null;
}

interface SoundSelectorProps {
  onSoundSelect: (sound: SoundItem | null) => void;
  selectedSound: SoundItem | null;
}

// Available sound categories
const SOUND_CATEGORIES = ["All", "Funny", "Motivational", "Educational"];

const SoundSelector = ({ onSoundSelect, selectedSound }: SoundSelectorProps) => {
  const [sounds, setSounds] = useState<SoundItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredSounds, setFilteredSounds] = useState<SoundItem[]>([]);
  const [playingId, setPlayingId] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>("All");
  const [isOpen, setIsOpen] = useState(false);
  
  // Add a ref for the audio element for better control
  const audioRef = useRef<HTMLAudioElement>(new Audio());
  
  // Handle component unmount to stop any playing audio
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = '';
      }
    };
  }, []);

  useEffect(() => {
    if (isOpen) {
      fetchSounds();
    } else {
      // Stop any playing sound when dialog closes
      if (audioRef.current && playingId) {
        audioRef.current.pause();
        setPlayingId(null);
      }
    }
  }, [isOpen]);

  useEffect(() => {
    // Filter sounds based on search query and category
    if (sounds.length === 0) {
      setFilteredSounds([]);
      return;
    }
    
    let filtered = [...sounds];
    
    // Apply category filter
    if (selectedCategory !== "All") {
      filtered = filtered.filter(sound => sound.category === selectedCategory);
    }
    
    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        sound =>
          sound.title.toLowerCase().includes(query) ||
          (sound.description && sound.description.toLowerCase().includes(query)) ||
          (sound.tags && sound.tags.some(tag => tag.toLowerCase().includes(query)))
      );
    }
    
    setFilteredSounds(filtered);
  }, [searchQuery, sounds, selectedCategory]);

  const fetchSounds = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('sound_library')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      // Ensure each sound item has a category property (can be null)
      const soundsWithCategory = data?.map(sound => ({
        ...sound,
        category: sound.category || null
      })) as SoundItem[];
      
      setSounds(soundsWithCategory);
      setFilteredSounds(soundsWithCategory);
      
    } catch (error) {
      console.error("Error fetching sounds:", error);
      toast.error("Failed to load sounds");
    } finally {
      setLoading(false);
    }
  };

  const handlePlayPause = (id: string, url: string) => {
    if (playingId === id) {
      // Currently playing this sound, so pause it
      if (audioRef.current) {
        audioRef.current.pause();
        setPlayingId(null);
      }
    } else {
      // If another sound is playing, pause it first
      if (playingId && audioRef.current) {
        audioRef.current.pause();
      }
      
      // Not playing this sound, so play it
      try {
        audioRef.current.src = url;
        
        // Set up event listeners for better error handling
        const playPromise = audioRef.current.play();
        
        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              setPlayingId(id);
            })
            .catch(error => {
              console.error("Error playing audio:", error);
              toast.error("Failed to play audio");
              setPlayingId(null);
            });
        }
        
        // Auto-stop when sound ends
        audioRef.current.onended = () => {
          setPlayingId(null);
        };
        
        // Handle errors during playback
        audioRef.current.onerror = () => {
          console.error("Audio playback error");
          toast.error("Error during audio playback");
          setPlayingId(null);
        };
      } catch (error) {
        console.error("Error setting up audio:", error);
        toast.error("Failed to set up audio playback");
      }
    }
  };

  const handleSelectSound = (sound: SoundItem) => {
    onSoundSelect(sound);
    setIsOpen(false);
    
    // Stop playing any sound
    if (audioRef.current && playingId) {
      audioRef.current.pause();
      audioRef.current.src = '';
      setPlayingId(null);
    }
    
    toast.success(`Selected sound: ${sound.title}`);
  };

  const handleRemoveSound = () => {
    onSoundSelect(null);
    toast.info("Using original audio");
  };

  return (
    <div>
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          Sound Track
        </label>
        
        <div className="flex gap-2 items-center">
          <div className="flex-1 border rounded-md p-2 bg-muted/30">
            {selectedSound ? (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Music size={16} className="text-forest" />
                  <span className="font-medium">{selectedSound.title}</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveSound}
                >
                  Remove
                </Button>
              </div>
            ) : (
              <span className="text-muted-foreground">Original Sound</span>
            )}
          </div>
          
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                Change Sound
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Select Sound</DialogTitle>
                <DialogDescription>
                  Choose a sound from the library to add to your video.
                </DialogDescription>
              </DialogHeader>
              
              <div className="my-4">
                <div className="relative">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search sounds..."
                    className="pl-10"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                
                <div className="mt-2">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {SOUND_CATEGORIES.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <ScrollArea className="h-[300px] pr-4">
                {loading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                  </div>
                ) : filteredSounds.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {sounds.length === 0 ? "No sounds available" : "No sounds match your search"}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredSounds.map((sound) => (
                      <div 
                        key={sound.id}
                        className={`p-3 rounded-md border cursor-pointer hover:bg-accent/50 transition-colors ${
                          selectedSound?.id === sound.id ? "bg-accent border-primary" : ""
                        }`}
                        onClick={() => handleSelectSound(sound)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium">{sound.title}</h4>
                            {sound.description && (
                              <p className="text-sm text-muted-foreground">{sound.description}</p>
                            )}
                            {sound.category && (
                              <span className="inline-block text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full mr-1">
                                {sound.category}
                              </span>
                            )}
                            {sound.tags && sound.tags.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {sound.tags.map((tag, i) => (
                                  <span key={i} className="text-xs bg-muted px-2 py-0.5 rounded-full">
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="ml-2"
                            onClick={(e) => {
                              e.stopPropagation();
                              handlePlayPause(sound.id, sound.sound_url);
                            }}
                          >
                            {playingId === sound.id ? <Pause size={16} /> : <Play size={16} />}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => onSoundSelect(null)}>
                  Use Original Audio
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default SoundSelector;
