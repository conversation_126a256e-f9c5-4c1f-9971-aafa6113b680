import { useState, useEffect } from "react";
import VideoPlayer from "./VideoPlayer";
import { supabase } from "@/integrations/supabase/client";
import VideoInfo from "./video/VideoInfo";
import VideoActions from "./video/VideoActions";
import { useAuth } from "@/contexts/AuthContext";

interface VideoCardProps {
  id: string;
  username: string;
  userAvatar: string;
  videoUrl: string;
  caption: string;
  soundtrack: string;
  likes: number;
  comments: number;
  shares: number;
  tags: string[];
  gameType?: string;
  userId: string;
  onDelete?: (videoId: string) => void;
  soundtrack_data?: {
    title: string;
    url: string;
  } | null;
  preload?: "auto" | "metadata" | "none";
  priority?: boolean;
  isScrolling?: boolean;
  forceAutoPlay?: boolean;
  color_filter?: {
    id: string;
    name: string;
    cssFilter: string;
  } | null;
}

const VideoCard = ({
  id,
  username,
  userAvatar,
  videoUrl,
  caption,
  soundtrack,
  likes,
  comments: initialComments,
  shares,
  tags,
  gameType,
  userId,
  onDelete,
  soundtrack_data,
  preload = "auto",
  priority = false,
  isScrolling = false,
  forceAutoPlay = false,
  color_filter = null
}: VideoCardProps) => {
  const [commentCount, setCommentCount] = useState(initialComments);
  const [likeCount, setLikeCount] = useState(likes);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | undefined>(undefined);
  const { user, profile } = useAuth();
  
  const isAdmin = profile?.badges?.includes("admin") || false;

  useEffect(() => {
    if (!priority && videoUrl) {
      const cachedThumbnail = sessionStorage.getItem(`thumbnail-${id}`);
      if (cachedThumbnail) {
        setThumbnailUrl(cachedThumbnail);
        return;
      }
      
      setThumbnailUrl(videoUrl);
    }
  }, [id, videoUrl, priority]);

  useEffect(() => {
    const fetchCommentCount = async () => {
      try {
        const { count, error } = await supabase
          .from('comments')
          .select('id', { count: 'exact', head: true })
          .eq('video_id', id);

        if (error) {
          console.error('Error fetching comment count:', error);
          return;
        }

        if (count !== null) {
          setCommentCount(count);
          
          if (count !== initialComments) {
            const { error: updateError } = await supabase
              .from('videos')
              .update({ comments: count })
              .eq('id', id);
              
            if (updateError) {
              console.error('Error updating video comment count:', updateError);
            }
          }
        }
      } catch (error) {
        console.error('Error in fetchCommentCount:', error);
      }
    };

    const fetchLikeCount = async () => {
      try {
        const { data, error } = await supabase
          .from('videos')
          .select('likes')
          .eq('id', id)
          .single();
          
        if (!error && data) {
          setLikeCount(data.likes || 0);
        }
      } catch (error) {
        console.error("Error fetching like count:", error);
      }
    };

    fetchCommentCount();
    fetchLikeCount();

    const commentsChannel = supabase
      .channel('public:comments')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'comments',
        filter: `video_id=eq.${id}`
      }, async (payload) => {
        try {
          const { count, error } = await supabase
            .from('comments')
            .select('id', { count: 'exact', head: true })
            .eq('video_id', id);

          if (error) {
            console.error('Error fetching comment count in realtime:', error);
            return;
          }

          if (count !== null) {
            setCommentCount(count);
            
            const { error: updateError } = await supabase
              .from('videos')
              .update({ comments: count })
              .eq('id', id);
              
            if (updateError) {
              console.error('Error updating video comment count in realtime:', updateError);
            }
          }
        } catch (error) {
          console.error('Error in realtime comment count fetch:', error);
        }
      })
      .subscribe();

    const likesChannel = supabase
      .channel(`video-likes-${id}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'videos',
        filter: `id=eq.${id}`
      }, (payload) => {
        if (payload.new && 'likes' in payload.new) {
          setLikeCount(payload.new.likes || 0);
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(commentsChannel);
      supabase.removeChannel(likesChannel);
    };
  }, [id, initialComments]);

  return (
    <div className="video-container h-screen w-full snap-start snap-always" style={{ height: 'calc(var(--vh, 1vh) * 100)' }}>
      <div className="relative h-full w-full">
        <VideoPlayer 
          url={videoUrl} 
          muted={false} 
          autoPlay={true}
          audioUrl={soundtrack_data?.url}
          preload={priority ? "auto" : preload}
          priority={priority}
          lazyLoad={!priority}
          poster={thumbnailUrl}
          forceAutoPlay={forceAutoPlay}
          colorFilter={color_filter}
        />
        
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute bottom-0 left-0 right-0 p-4 pointer-events-auto">
            <div className="video-overlay-gradient absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent pointer-events-none"></div>
            
            <div className="relative z-10 flex justify-between items-end">
              <div className="w-full max-w-[75%]">
                <VideoInfo 
                  username={username}
                  userAvatar={userAvatar}
                  caption={caption}
                  tags={tags}
                  gameType={gameType}
                  userId={userId}
                  soundtrack={soundtrack_data ? soundtrack_data.title : soundtrack || "Original Sound"}
                />
              </div>
              
              <div className="flex-shrink-0">
                <VideoActions 
                  id={id}
                  likes={likeCount}
                  comments={commentCount}
                  shares={shares}
                  userId={userId}
                  username={username}
                  videoUrl={videoUrl}
                  caption={caption}
                  userAvatar={userAvatar}
                  onDelete={onDelete}
                  isAdmin={isAdmin}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoCard;
