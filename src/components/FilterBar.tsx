import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import { Filter } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";

type FilterCategory = 'interest' | 'region' | 'huntStyle' | 'weapon' | 'tag';

interface Filter {
  category: FilterCategory;
  label: string;
  value: string;
}

// Updated filter categories with new tags
const defaultFilters: Filter[] = [
  { category: 'interest', label: 'Velvet Bucks', value: 'velvet bucks' },
  { category: 'interest', label: 'Bow Hunting', value: 'bow hunting' },
  { category: 'interest', label: 'Gun Hunting', value: 'gun hunting' },
  { category: 'interest', label: 'Shed Hunting', value: 'shed hunting' },
  { category: 'interest', label: 'Food Plots', value: 'food plots' },
  { category: 'interest', label: 'Trail Cam Videos', value: 'trail cam videos' },
  { category: 'region', label: 'Midwest', value: 'midwest' },
  { category: 'region', label: 'South', value: 'south' },
  { category: 'region', label: 'Northeast', value: 'northeast' },
  { category: 'region', label: 'West', value: 'west' },
  { category: 'huntStyle', label: 'DIY', value: 'diy' },
  { category: 'huntStyle', label: 'Outfitted', value: 'outfitted' },
  { category: 'huntStyle', label: 'Public Land', value: 'public' },
  { category: 'huntStyle', label: 'Private Land', value: 'private' },
  { category: 'weapon', label: 'Bow', value: 'bow' },
  { category: 'weapon', label: 'Rifle', value: 'rifle' },
  { category: 'weapon', label: 'Muzzleloader', value: 'muzzleloader' },
  { category: 'weapon', label: 'Crossbow', value: 'crossbow' },
  // Updated tag options to include new tags
  { category: 'tag', label: '#Velvet Bucks', value: 'velvet bucks' },
  { category: 'tag', label: '#Bow Hunting', value: 'bow hunting' },
  { category: 'tag', label: '#Gun Hunting', value: 'gun hunting' },
  { category: 'tag', label: '#Shed Hunting', value: 'shed hunting' },
  { category: 'tag', label: '#Food Plots', value: 'food plots' },
  { category: 'tag', label: '#Trail Cam Videos', value: 'trail cam videos' },
  { category: 'tag', label: '#The Rut', value: 'the rut' },
  { category: 'tag', label: '#Big Bucks', value: 'big bucks' },
  { category: 'tag', label: '#Harvest Shot', value: 'harvest shot' },
];

interface FilterBarProps {
  onFilterChange?: (selectedFilters: string[]) => void;
  activeFilters?: string[];
}

const FilterBar = ({ onFilterChange, activeFilters = [] }: FilterBarProps) => {
  const [selectedFilters, setSelectedFilters] = useState<string[]>(activeFilters);
  const [filters, setFilters] = useState<Filter[]>(defaultFilters);
  const isMobile = useIsMobile();
  
  // Group filters by category for better organization
  const filtersByCategory = filters.reduce((acc, filter) => {
    if (!acc[filter.category]) {
      acc[filter.category] = [];
    }
    acc[filter.category].push(filter);
    return acc;
  }, {} as Record<FilterCategory, Filter[]>);
  
  // Fetch popular tags from videos
  useEffect(() => {
    const fetchTags = async () => {
      try {
        console.log("Fetching tags for FilterBar");
        const { data, error } = await supabase
          .from('videos')
          .select('tags, game_type');
        
        if (error) {
          console.error("Error fetching tags:", error);
          return;
        }
        
        if (data && data.length > 0) {
          console.log("Retrieved video data for tags:", data);
          
          // Log game types for debugging
          const gameTypes = data
            .map(video => video.game_type)
            .filter(Boolean);
          console.log("Game types in videos:", gameTypes);
          
          // Extract unique tags from all videos
          const allTags = data
            .flatMap(video => video.tags || [])
            .filter(Boolean)
            .map(tag => tag.toLowerCase()); // Normalize tags to lowercase
          
          console.log("All tags extracted (lowercase):", allTags);
          
          // Get unique tags and limit to top 10 most frequent
          const tagCounts: Record<string, number> = {};
          
          allTags.forEach(tag => {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
          });
          
          const popularTags = Object.entries(tagCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .map(([tag]) => tag);
          
          console.log("Popular tags:", popularTags);
          
          // Create tag filters and combine with default filters
          const tagFilters: Filter[] = popularTags.map(tag => ({
            category: 'tag',
            label: `#${tag}`,
            value: tag
          }));
          
          setFilters([...defaultFilters, ...tagFilters]);
        }
      } catch (error) {
        console.error("Error in fetchTags:", error);
      }
    };
    
    fetchTags();
  }, []);
  
  // Update selectedFilters when activeFilters prop changes
  useEffect(() => {
    setSelectedFilters(activeFilters);
  }, [activeFilters]);

  const toggleFilter = (filter: Filter) => {
    setSelectedFilters(prev => {
      const newFilters = prev.includes(filter.value)
        ? prev.filter(f => f !== filter.value)
        : [...prev, filter.value];
        
      console.log("New selected filters:", newFilters);
        
      if (onFilterChange) {
        onFilterChange(newFilters);
      }
      
      return newFilters;
    });
  };

  // Get active filter count for button badge
  const activeFilterCount = selectedFilters.length;
  
  // Render filter groups for drawer/sheet
  const renderFilterGroups = () => {
    return Object.entries(filtersByCategory).map(([category, categoryFilters]) => (
      <div key={category} className="mb-4">
        <h3 className="text-sm font-medium mb-2 capitalize">
          {/* Update the display name for 'interest' category */}
          {category === 'tag' ? 'Tags' : 
           category === 'interest' ? 'Interest' : 
           category}
        </h3>
        <div className="flex flex-wrap gap-2">
          {categoryFilters.map((filter) => (
            <Badge
              key={filter.value + filter.category}
              variant="outline" 
              className={cn(
                "cursor-pointer whitespace-nowrap transition-colors",
                selectedFilters.includes(filter.value)
                  ? filter.category === 'tag' 
                    ? "bg-purple-600 text-white" 
                    : "bg-forest text-white"
                  : "bg-background hover:bg-accent hover:text-accent-foreground"
              )}
              onClick={() => toggleFilter(filter)}
            >
              {filter.label}
            </Badge>
          ))}
        </div>
      </div>
    ));
  };

  // Display a compact horizontal scrolling list of selected filters
  const renderSelectedFiltersList = () => {
    if (selectedFilters.length === 0) return null;
    
    return (
      <div className="flex overflow-x-auto gap-2 py-2 px-4 w-full no-scrollbar">
        {selectedFilters.map(value => {
          const filter = filters.find(f => f.value === value);
          if (!filter) return null;
          
          return (
            <Badge
              key={filter.value}
              variant="default"
              className={cn(
                "whitespace-nowrap cursor-pointer",
                filter.category === 'tag' ? "bg-purple-600" : "bg-forest"
              )}
              onClick={() => toggleFilter(filter)}
            >
              {filter.label} ✕
            </Badge>
          );
        })}
      </div>
    );
  };

  return (
    <div className="w-full">
      {isMobile ? (
        <>
          {/* Mobile UI with drawer */}
          <div className="flex items-center justify-between px-4 py-2">
            <Drawer>
              <DrawerTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Filter size={16} />
                  <span>Filters</span>
                  {activeFilterCount > 0 && (
                    <span className="bg-forest text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                      {activeFilterCount}
                    </span>
                  )}
                </Button>
              </DrawerTrigger>
              <DrawerContent>
                <DrawerHeader>
                  <DrawerTitle>Filters</DrawerTitle>
                </DrawerHeader>
                <div className="px-4 pb-8">
                  {renderFilterGroups()}
                  <DrawerClose asChild>
                    <Button className="w-full mt-4">Apply Filters</Button>
                  </DrawerClose>
                </div>
              </DrawerContent>
            </Drawer>
          </div>
          
          {/* Display selected filters in a horizontal scrollable row */}
          {renderSelectedFiltersList()}
        </>
      ) : (
        /* Desktop UI with sheet */
        <>
          <div className="flex items-center justify-between px-4 py-2">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Filter size={16} />
                  <span>Filters</span>
                  {activeFilterCount > 0 && (
                    <span className="bg-forest text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                      {activeFilterCount}
                    </span>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Filters</SheetTitle>
                </SheetHeader>
                <div className="py-4">
                  {renderFilterGroups()}
                </div>
              </SheetContent>
            </Sheet>
          </div>
          
          {/* Display selected filters in a horizontal scrollable row */}
          {renderSelectedFiltersList()}
        </>
      )}
    </div>
  );
};

export default FilterBar;
