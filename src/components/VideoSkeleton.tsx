
import { Skeleton } from "@/components/ui/skeleton";
import { AspectRatio } from "@/components/ui/aspect-ratio";

const VideoSkeleton = () => {
  return (
    <div className="h-screen w-full snap-start snap-always" style={{ height: 'calc(var(--vh, 1vh) * 100)' }}>
      <div className="relative h-full w-full">
        <AspectRatio ratio={9/16} className="h-full w-full">
          <div className="absolute inset-0 bg-black/90">
            <div className="h-full w-full flex items-center justify-center">
              <div className="animate-pulse space-y-3 w-full px-8">
                <Skeleton className="h-4 w-3/4 bg-gray-700/30 rounded-md mx-auto" />
                <Skeleton className="h-4 w-1/2 bg-gray-700/30 rounded-md mx-auto" />
              </div>
            </div>
          </div>
        </AspectRatio>
        
        {/* Video overlay with user info */}
        <div className="absolute bottom-0 left-0 right-0 p-4">
          <div className="video-overlay-gradient absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
          
          <div className="relative z-10 flex justify-between items-end">
            <div className="w-full max-w-[75%]">
              <div className="flex items-center mb-2">
                <Skeleton className="h-8 w-8 rounded-full bg-gray-700/40" />
                <Skeleton className="h-4 w-24 ml-2 bg-gray-700/40 rounded-md" />
              </div>
              <Skeleton className="h-3 w-full bg-gray-700/30 rounded-md mb-1" />
              <Skeleton className="h-3 w-3/4 bg-gray-700/30 rounded-md" />
            </div>
            
            <div className="flex-shrink-0">
              <div className="flex flex-col items-center space-y-4">
                <Skeleton className="h-10 w-10 rounded-full bg-gray-700/40" />
                <Skeleton className="h-10 w-10 rounded-full bg-gray-700/40" />
                <Skeleton className="h-10 w-10 rounded-full bg-gray-700/40" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoSkeleton;
