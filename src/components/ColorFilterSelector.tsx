
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Palette, RotateCcw } from "lucide-react";

interface ColorFilter {
  id: string;
  name: string;
  cssFilter: string;
  preview: string;
}

const colorFilters: ColorFilter[] = [
  {
    id: "none",
    name: "Original",
    cssFilter: "none",
    preview: "bg-gray-200"
  },
  {
    id: "warm",
    name: "Warm",
    cssFilter: "sepia(0.3) saturate(1.2) brightness(1.1)",
    preview: "bg-orange-200"
  },
  {
    id: "cool",
    name: "Cool",
    cssFilter: "hue-rotate(200deg) saturate(0.8) brightness(1.1)",
    preview: "bg-blue-200"
  },
  {
    id: "vintage",
    name: "Vintage",
    cssFilter: "sepia(0.5) contrast(1.2) brightness(1.1) saturate(0.8)",
    preview: "bg-amber-300"
  },
  {
    id: "dramatic",
    name: "Dramatic",
    cssFilter: "contrast(1.3) saturate(1.3) brightness(0.9)",
    preview: "bg-gray-800"
  },
  {
    id: "soft",
    name: "Soft",
    cssFilter: "brightness(1.15) saturate(0.7) contrast(0.85)",
    preview: "bg-pink-200"
  },
  {
    id: "vivid",
    name: "Vivid",
    cssFilter: "saturate(1.5) contrast(1.1) brightness(1.05)",
    preview: "bg-green-400"
  },
  {
    id: "blackwhite",
    name: "B&W",
    cssFilter: "grayscale(1) contrast(1.1)",
    preview: "bg-gray-400"
  }
];

interface ColorFilterSelectorProps {
  onFilterSelect: (filter: ColorFilter) => void;
  selectedFilter: ColorFilter | null;
}

const ColorFilterSelector = ({ onFilterSelect, selectedFilter }: ColorFilterSelectorProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleFilterSelect = (filter: ColorFilter) => {
    onFilterSelect(filter);
  };

  const resetFilter = () => {
    onFilterSelect(colorFilters[0]); // Reset to "Original"
  };

  return (
    <Card className="w-full">
      <CardHeader 
        className="cursor-pointer" 
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <CardTitle className="flex items-center justify-between text-base">
          <div className="flex items-center gap-2">
            <Palette size={18} className="text-forest" />
            Color Filters
          </div>
          <div className="flex items-center gap-2">
            {selectedFilter && selectedFilter.id !== "none" && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  resetFilter();
                }}
                className="text-xs"
              >
                <RotateCcw size={14} />
              </Button>
            )}
            <span className="text-xs text-muted-foreground">
              {selectedFilter?.name || "Original"}
            </span>
          </div>
        </CardTitle>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="pt-0">
          <div className="grid grid-cols-4 gap-3">
            {colorFilters.map((filter) => (
              <Button
                key={filter.id}
                variant={selectedFilter?.id === filter.id ? "default" : "outline"}
                className={`flex flex-col items-center p-3 h-auto ${
                  selectedFilter?.id === filter.id ? 'bg-forest hover:bg-forest-dark' : ''
                }`}
                onClick={() => handleFilterSelect(filter)}
              >
                <div 
                  className={`w-8 h-8 rounded-full mb-2 ${filter.preview}`}
                  style={{ filter: filter.id !== "none" ? filter.cssFilter : "none" }}
                />
                <span className="text-xs font-medium">{filter.name}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default ColorFilterSelector;
export type { ColorFilter };
