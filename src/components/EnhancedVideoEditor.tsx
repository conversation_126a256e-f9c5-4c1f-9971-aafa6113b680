import { useState, useRef, useEffect, useCallback } from "react";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { fetchFile, toBlobURL } from "@ffmpeg/util";
import { Check, X, Play, Pause, Crop, RotateCcw, Volume2, VolumeX, Scissors, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import VideoEditor from "./VideoEditor"; // Import the original editor as fallback

interface EnhancedVideoEditorProps {
  videoSrc: string;
  onSave: (blob: Blob) => void;
  onCancel: () => void;
  initialFilters?: VideoFilters;
  initialSettings?: { zoomLevel: number; cropX: number; aspectRatio: number };
  onSettingsChange?: (settings: { zoomLevel: number; cropX: number; aspectRatio: number }) => void;
}

export interface VideoFilters {
  brightness: number;
  contrast: number;
  saturation: number;
}

const EnhancedVideoEditor = ({ 
  videoSrc, 
  onSave, 
  onCancel, 
  initialFilters, 
  initialSettings,
  onSettingsChange 
}: EnhancedVideoEditorProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [ffmpeg, setFfmpeg] = useState<FFmpeg | null>(null);
  const [isFFmpegLoaded, setIsFFmpegLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<boolean>(false);
  const [playing, setPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [aspectRatio, setAspectRatio] = useState<number>(9/16);
  const [cropX, setCropX] = useState<number>(0);
  const [maxCropX, setMaxCropX] = useState<number>(0);
  const [videoWidth, setVideoWidth] = useState<number>(0);
  const [videoHeight, setVideoHeight] = useState<number>(0);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [showHelpDialog, setShowHelpDialog] = useState<boolean>(false);
  const [processing, setProcessing] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState("crop");
  const [brightness, setBrightness] = useState<number>(initialFilters?.brightness || 100);
  const [contrast, setContrast] = useState<number>(initialFilters?.contrast || 100);
  const [saturation, setSaturation] = useState<number>(initialFilters?.saturation || 100);
  const [zoomLevel, setZoomLevel] = useState<number>(initialSettings?.zoomLevel || 1);

  // Load FFmpeg
  useEffect(() => {
    const loadFFmpeg = async () => {
      try {
        setIsLoading(true);
        
        // Create FFmpeg instance
        const ffmpegInstance = new FFmpeg();
        
        // Load FFmpeg with the core URL
        await ffmpegInstance.load({
          coreURL: await toBlobURL(
            'https://unpkg.com/@ffmpeg/core@0.12.6/dist/ffmpeg-core.js',
            'application/javascript'
          )
        });
        
        setFfmpeg(ffmpegInstance);
        setIsFFmpegLoaded(true);
        toast.success("Video editing engine loaded successfully");
      } catch (error) {
        console.error("Error loading FFmpeg:", error);
        setLoadError(true);
        toast.error("Could not load video editing engine. Using basic editor instead.");
      } finally {
        setIsLoading(false);
      }
    };

    loadFFmpeg();
    
    return () => {
      // Clean up FFmpeg if needed
      if (ffmpeg) {
        try {
          ffmpeg.terminate();
        } catch (e) {
          console.error("Error terminating FFmpeg:", e);
        }
      }
    };
  }, []);

  // Handle video loading
  useEffect(() => {
    if (!videoRef.current) return;
    
    const video = videoRef.current;
    video.muted = isMuted;
    
    const handleMetadataLoaded = () => {
      if (!video) return;
      
      setDuration(video.duration);
      setIsLoading(false);
      
      const vWidth = video.videoWidth;
      const vHeight = video.videoHeight;
      setVideoWidth(vWidth);
      setVideoHeight(vHeight);
      
      const cropWidth = vHeight * aspectRatio;
      const newMaxCropX = Math.max(0, vWidth - cropWidth);
      setMaxCropX(newMaxCropX);
      
      // Center the crop area
      const optimalCropX = (vWidth - cropWidth) / 2;
      setCropX(Math.max(0, Math.min(optimalCropX, newMaxCropX)));
      
      requestAnimationFrame(() => drawFrameToCanvas());
      
      const hasSeenHelp = localStorage.getItem('video-editor-help-seen');
      if (!hasSeenHelp) {
        setShowHelpDialog(true);
        localStorage.setItem('video-editor-help-seen', 'true');
      }
    };
    
    const handleTimeUpdate = () => {
      if (!video) return;
      setCurrentTime(video.currentTime);
      
      if (!isDragging) {
        drawFrameToCanvas();
      }
    };
    
    video.addEventListener('loadedmetadata', handleMetadataLoaded);
    video.addEventListener('timeupdate', handleTimeUpdate);
    
    return () => {
      video.removeEventListener('loadedmetadata', handleMetadataLoaded);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.pause();
    };
  }, [videoSrc, aspectRatio, isMuted, isDragging]);

  // Update crop dimensions when aspect ratio changes
  useEffect(() => {
    if (!isLoading && videoWidth > 0 && videoHeight > 0) {
      const cropWidth = videoHeight * aspectRatio;
      const newMaxCropX = Math.max(0, videoWidth - cropWidth);
      
      setMaxCropX(newMaxCropX);
      
      // Center the crop area
      const optimalCropX = (videoWidth - cropWidth) / 2;
      setCropX(Math.max(0, Math.min(optimalCropX, newMaxCropX)));
      
      drawFrameToCanvas();
    }
  }, [aspectRatio, isLoading, videoWidth, videoHeight]);

  // Update filters when they change
  useEffect(() => {
    if (!isLoading) {
      drawFrameToCanvas();
    }
  }, [brightness, contrast, saturation, isLoading]);

  // Draw the current frame to the canvas with applied filters
  const drawFrameToCanvas = useCallback(() => {
    if (!videoRef.current || !canvasRef.current || isLoading) return;
    
    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Ensure we have valid dimensions
    if (videoWidth === 0 || videoHeight === 0) return;
    
    const cropWidth = videoHeight * aspectRatio;
    const boundedCropX = Math.max(0, Math.min(cropX, maxCropX));
    
    canvas.width = 320;
    canvas.height = 320 / aspectRatio;
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    try {
      // Apply filters
      ctx.filter = `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%)`;
      
      // FIX: Improved zoom calculations to match VideoEditor component
      if (zoomLevel && zoomLevel > 1) {
        // Calculate zoomed dimensions
        const zoomedCropWidth = cropWidth / zoomLevel;
        const zoomedVideoHeight = videoHeight / zoomLevel;
        
        // Center zoom based on crop position
        const cropCenter = boundedCropX + (cropWidth / 2);
        const zoomOffsetX = cropCenter - (zoomedCropWidth / 2);
        const zoomOffsetY = (videoHeight - zoomedVideoHeight) / 2;
        
        // Ensure zoom offset doesn't go outside video boundaries
        const clampedZoomOffsetX = Math.max(0, Math.min(zoomOffsetX, videoWidth - zoomedCropWidth));
        
        ctx.drawImage(
          video,
          clampedZoomOffsetX, zoomOffsetY, zoomedCropWidth, zoomedVideoHeight,
          0, 0, canvas.width, canvas.height
        );
      } else {
        // Regular drawing without zoom
        ctx.drawImage(
          video,
          boundedCropX, 0, cropWidth, videoHeight,
          0, 0, canvas.width, canvas.height
        );
      }
      
      // Reset filters
      ctx.filter = 'none';
    } catch (error) {
      console.error("Error drawing video frame:", error);
    }
  }, [cropX, maxCropX, videoWidth, videoHeight, aspectRatio, brightness, contrast, saturation, isLoading, zoomLevel]);

  // Toggle play/pause
  const togglePlay = () => {
    if (!videoRef.current || isLoading) return;
    
    if (playing) {
      videoRef.current.pause();
      setPlaying(false);
    } else {
      videoRef.current.play().catch(error => {
        console.error("Failed to play video:", error);
        toast.error("Couldn't play video. Try again.");
      });
      setPlaying(true);
    }
  };

  // Toggle mute
  const toggleMute = () => {
    if (!videoRef.current) return;
    
    setIsMuted(!isMuted);
    videoRef.current.muted = !isMuted;
    
    toast.info(isMuted ? "Sound enabled" : "Sound muted");
  };

  // Reset video to beginning
  const resetVideo = () => {
    if (!videoRef.current || isLoading) return;
    
    videoRef.current.currentTime = 0;
    setCurrentTime(0);
    drawFrameToCanvas();
    toast.info("Video reset to start");
  };

  // Handle crop position change
  const handleCropPositionChange = (newCropX: number[]) => {
    if (!videoRef.current || isLoading || newCropX.length === 0) return;
    
    const boundedCropX = Math.max(0, Math.min(newCropX[0], maxCropX));
    setCropX(boundedCropX);
    setIsDragging(true);
    
    // Ensure we redraw the frame with the current zoom level
    requestAnimationFrame(() => drawFrameToCanvas());
  };

  // FIX: Add handler for zoom level changes
  const handleZoomChange = (newZoom: number[]) => {
    if (isLoading || newZoom.length === 0) return;
    
    const newZoomLevel = newZoom[0];
    setZoomLevel(newZoomLevel);
    
    // Always force redraw after zoom change
    requestAnimationFrame(() => drawFrameToCanvas());
  };
  
  // FIX: Add handler for zoom level commit
  const handleZoomCommit = () => {
    // Force redraw on zoom commit to ensure zoom state is persisted
    requestAnimationFrame(() => drawFrameToCanvas());
    toast.info(`Zoom level: ${Math.round(zoomLevel * 100)}%`);
  };

  // Handle crop position commit (when user releases slider)
  const handleCropPositionCommit = () => {
    setIsDragging(false);
    
    // Final redraw to ensure canvas is updated with current zoom
    requestAnimationFrame(() => drawFrameToCanvas());
    
    // Provide visual feedback
    toast.info("Crop position updated");
  };

  // Handle seek
  const handleSeek = (newTime: number[]) => {
    if (!videoRef.current || isLoading || newTime.length === 0) return;
    
    const clampedTime = Math.max(0, Math.min(newTime[0], duration));
    videoRef.current.currentTime = clampedTime;
    setCurrentTime(clampedTime);
    drawFrameToCanvas();
  };

  // Change aspect ratio
  const changeAspectRatio = (newRatio: number) => {
    setAspectRatio(newRatio);
    toast.info(`Aspect ratio changed to ${newRatio === 9/16 ? "9:16" : newRatio === 4/5 ? "4:5" : "1:1"}`);
  };

  // Reset filters to default
  const resetFilters = () => {
    setBrightness(100);
    setContrast(100);
    setSaturation(100);
    toast.info("Filters reset to default");
  };

  // Apply preset filter
  const applyPresetFilter = (preset: 'normal' | 'warm' | 'cool' | 'vintage' | 'vivid' | 'bw') => {
    switch (preset) {
      case 'normal':
        setBrightness(100);
        setContrast(100);
        setSaturation(100);
        break;
      case 'warm':
        setBrightness(110);
        setContrast(120);
        setSaturation(120);
        break;
      case 'cool':
        setBrightness(105);
        setContrast(110);
        setSaturation(85);
        break;
      case 'vintage':
        setBrightness(115);
        setContrast(125);
        setSaturation(50);
        break;
      case 'vivid':
        setBrightness(90);
        setContrast(130);
        setSaturation(120);
        break;
      case 'bw':
        setBrightness(100);
        setContrast(100);
        setSaturation(0);
        break;
    }
    toast.info(`Applied ${preset} filter`);
  };

  // Save with basic cropping fallback if FFmpeg fails
  const saveWithBasicCropping = async () => {
    if (!videoRef.current || !canvasRef.current || isLoading) return;
    
    try {
      setProcessing(true);
      toast.info("Processing video with basic cropping...");
      
      const video = videoRef.current;
      
      if (playing) {
        video.pause();
        setPlaying(false);
      }
      
      const cropWidth = videoHeight * aspectRatio;
      const boundedCropX = Math.max(0, Math.min(cropX, maxCropX));
      
      const exportCanvas = document.createElement('canvas');
      const ctx = exportCanvas.getContext('2d');
      
      if (!ctx) {
        throw new Error("Failed to get export canvas context");
      }
      
      exportCanvas.width = cropWidth;
      exportCanvas.height = videoHeight;
      
      // Reset to beginning to ensure consistent cropping
      video.currentTime = 0;
      
      await new Promise<void>((resolve) => {
        const seekHandler = () => {
          video.removeEventListener('seeked', seekHandler);
          resolve();
        };
        video.addEventListener('seeked', seekHandler);
      });
      
      // FIX: Apply zoom when creating snapshot
      if (zoomLevel && zoomLevel > 1) {
        // Calculate zoomed dimensions
        const zoomedCropWidth = cropWidth / zoomLevel;
        const zoomedVideoHeight = videoHeight / zoomLevel;
        
        // Center zoom on crop area
        const zoomCenterX = boundedCropX + (cropWidth / 2);
        const zoomOffsetX = zoomCenterX - (zoomedCropWidth / 2);
        const zoomOffsetY = (videoHeight - zoomedVideoHeight) / 2;
        
        // Ensure zoom offset doesn't go outside video boundaries
        const clampedZoomOffsetX = Math.max(0, Math.min(zoomOffsetX, videoWidth - zoomedCropWidth));
        
        ctx.drawImage(
          video,
          clampedZoomOffsetX, zoomOffsetY, zoomedCropWidth, zoomedVideoHeight,
          0, 0, exportCanvas.width, exportCanvas.height
        );
      } else {
        ctx.drawImage(
          video,
          boundedCropX, 0, cropWidth, videoHeight,
          0, 0, exportCanvas.width, exportCanvas.height
        );
      }
      
      // Create a static snapshot
      exportCanvas.toBlob((blob) => {
        if (blob) {
          onSave(blob);
          setProcessing(false);
          toast.success("Video snapshot created successfully!");
        } else {
          throw new Error("Failed to create snapshot");
        }
      }, 'image/jpeg', 0.95);
      
    } catch (error) {
      console.error("Error creating snapshot:", error);
      setProcessing(false);
      toast.error("Failed to process video. Please try again with the basic editor.");
    }
  };

  // Process and save the video
  const saveVideo = async () => {
    if (!videoRef.current || !canvasRef.current || isLoading || !isFFmpegLoaded || !ffmpeg) {
      // If FFmpeg is not available, use basic cropping
      if (!isFFmpegLoaded || !ffmpeg) {
        toast.warning("Advanced video processing not available. Using basic cropping instead.");
        saveWithBasicCropping();
        return;
      }
      return;
    }
    
    try {
      setProcessing(true);
      toast.info("Processing video...");
      
      const video = videoRef.current;
      
      if (playing) {
        video.pause();
        setPlaying(false);
      }
      
      // Get the original video file as a blob
      const response = await fetch(videoSrc);
      const videoBlob = await response.blob();
      
      // Write the input file to FFmpeg's virtual file system
      await ffmpeg.writeFile('input.mp4', await fetchFile(videoBlob));
      
      // Basic command setup
      const outputFileName = 'output.mp4';
      const filters = [];
      
      // Add crop filter if we're using crop
      if (activeTab === "crop" || activeTab === "all") {
        const cropWidth = videoHeight * aspectRatio;
        const boundedCropX = Math.max(0, Math.min(cropX, maxCropX));
        filters.push(`crop=${cropWidth}:${videoHeight}:${boundedCropX}:0`);
      }
      
      // Add color correction filters if we're using them
      if (activeTab === "filters" || activeTab === "all") {
        if (brightness !== 100) {
          filters.push(`eq=brightness=${(brightness - 100) / 100}`);
        }
        if (contrast !== 100) {
          filters.push(`eq=contrast=${contrast / 100}`);
        }
        if (saturation !== 100) {
          filters.push(`eq=saturation=${saturation / 100}`);
        }
      }
      
      // When constructing filter string, include the zoom
      if (zoomLevel > 1) {
        // Calculate the zoom center based on crop position
        const cropWidth = videoHeight * aspectRatio;
        const zoomCenterX = cropX + (cropWidth / 2); 
        const zoomCenterY = videoHeight / 2;
        
        // Add zoom filter if zoom level is greater than 1
        filters.push(`zoompan=z=${zoomLevel}:x='iw/2-(iw/zoom)*(${zoomCenterX}/iw)':y='ih/2-(ih/zoom)/2':d=1:s=${cropWidth}x${videoHeight}`);
      }
      
      // Construct the ffmpeg command
      const filterString = filters.length > 0 ? filters.join(',') : 'copy';
      
      // Run the FFmpeg command with the proper syntax for version 0.12.x
      await ffmpeg.exec([
        '-i', 'input.mp4',
        '-filter_complex', filterString,
        '-c:v', 'libx264',
        '-preset', 'fast',
        '-pix_fmt', 'yuv420p',
        '-c:a', 'copy',
        outputFileName
      ]);
      
      // Read the output file from FFmpeg's virtual file system
      const outputData = await ffmpeg.readFile(outputFileName);
      
      // Create a Blob from the output data
      const outputBlob = new Blob([outputData], { type: 'video/mp4' });
      
      // Save the processed video
      onSave(outputBlob);
      setProcessing(false);
      toast.success("Video processed successfully!");
    } catch (error) {
      console.error("Error processing video:", error);
      setProcessing(false);
      toast.error("Failed to process video with advanced features. Trying basic cropping instead.");
      // Fall back to basic cropping if FFmpeg fails
      saveWithBasicCropping();
    }
  };

  // Return filters for the parent component
  const getFilters = (): VideoFilters => {
    return {
      brightness,
      contrast,
      saturation
    };
  };

  // If loading takes too long, show a helpful message
  if (loadError) {
    return <VideoEditor videoSrc={videoSrc} onSave={onSave} onCancel={onCancel} />;
  }

  if (isLoading) {
    return (
      <div className="bg-background border border-border rounded-lg p-8 space-y-6 text-center">
        <h2 className="text-xl font-semibold">Loading Video Editor</h2>
        <div className="flex justify-center">
          <div className="animate-spin h-8 w-8 border-4 border-forest border-t-transparent rounded-full"></div>
        </div>
        <p className="text-muted-foreground">This may take a moment to load the first time...</p>
        <Button 
          variant="outline" 
          onClick={() => setLoadError(true)}
          className="mt-4"
        >
          Use Basic Editor Instead
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-background border border-border rounded-lg p-4 space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold flex items-center">
          <Scissors size={18} className="mr-2 text-forest" />
          Enhanced Video Editor
        </h2>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={() => setShowHelpDialog(true)}>
            ?
          </Button>
          <Button variant="ghost" size="icon" onClick={onCancel} disabled={processing}>
            <X size={18} />
          </Button>
        </div>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4">
        <video 
          ref={videoRef}
          src={videoSrc}
          className="hidden"
          muted={isMuted}
          playsInline
          preload="auto"
          controls={false}
        />
        
        <div className="relative border border-border rounded-lg overflow-hidden bg-black flex justify-center items-center min-h-[320px]">
          <canvas ref={canvasRef} className="max-w-full" />
          <div className="absolute bottom-2 right-2 flex gap-2">
            <Button 
              variant="secondary" 
              size="icon" 
              className="bg-white/80 hover:bg-white text-forest rounded-full h-8 w-8"
              onClick={toggleMute}
            >
              {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
            </Button>
            <Button 
              variant="secondary" 
              size="icon" 
              className="bg-white/80 hover:bg-white text-forest rounded-full h-8 w-8"
              onClick={togglePlay}
            >
              {playing ? <Pause size={16} /> : <Play size={16} />}
            </Button>
            <Button 
              variant="secondary" 
              size="icon" 
              className="bg-white/80 hover:bg-white text-forest rounded-full h-8 w-8"
              onClick={resetVideo}
            >
              <RotateCcw size={16} />
            </Button>
          </div>
        </div>
        
        <div className="space-y-4 min-w-[200px] flex-1">
          <Tabs defaultValue="crop" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="w-full">
              <TabsTrigger value="crop" className="flex-1">Crop</TabsTrigger>
              <TabsTrigger value="filters" className="flex-1">Filters</TabsTrigger>
            </TabsList>
            
            <TabsContent value="crop" className="space-y-4 pt-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Aspect Ratio</h3>
                <div className="flex gap-2">
                  <Button 
                    variant={aspectRatio === 9/16 ? "default" : "outline"}
                    onClick={() => changeAspectRatio(9/16)}
                    className={`w-full ${aspectRatio === 9/16 ? 'bg-forest hover:bg-forest-dark' : ''}`}
                    disabled={processing}
                  >
                    9:16
                  </Button>
                  <Button 
                    variant={aspectRatio === 1 ? "default" : "outline"}
                    onClick={() => changeAspectRatio(1)}
                    className={`w-full ${aspectRatio === 1 ? 'bg-forest hover:bg-forest-dark' : ''}`}
                    disabled={processing}
                  >
                    1:1
                  </Button>
                  <Button 
                    variant={aspectRatio === 4/5 ? "default" : "outline"}
                    onClick={() => changeAspectRatio(4/5)}
                    className={`w-full ${aspectRatio === 4/5 ? 'bg-forest hover:bg-forest-dark' : ''}`}
                    disabled={processing}
                  >
                    4:5
                  </Button>
                </div>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium">Horizontal Position</h3>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Crop size={16} className="mr-1" />
                    <span>Drag to adjust</span>
                  </div>
                </div>
                <Slider 
                  value={[cropX]} 
                  min={0} 
                  max={maxCropX || 1}
                  step={1}
                  onValueChange={handleCropPositionChange}
                  onValueCommit={() => handleCropPositionCommit()}
                  disabled={processing}
                  className="mt-2"
                  aria-label="Horizontal crop position"
                />
                <div className="text-xs text-muted-foreground mt-1 text-center">
                  {isDragging ? "Release to apply" : "Position: " + Math.round(cropX)}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="filters" className="space-y-4 pt-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium">Color Adjustment</h3>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={resetFilters}
                  disabled={processing}
                  className="text-xs flex items-center"
                >
                  <RefreshCw size={12} className="mr-1" />
                  Reset
                </Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm">Brightness</span>
                    <span className="text-sm text-muted-foreground">{brightness}%</span>
                  </div>
                  <Slider 
                    value={[brightness]} 
                    min={50} 
                    max={150}
                    step={1}
                    onValueChange={(val) => setBrightness(val[0])}
                    disabled={processing}
                    aria-label="Brightness"
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm">Contrast</span>
                    <span className="text-sm text-muted-foreground">{contrast}%</span>
                  </div>
                  <Slider 
                    value={[contrast]} 
                    min={50} 
                    max={150}
                    step={1}
                    onValueChange={(val) => setContrast(val[0])}
                    disabled={processing}
                    aria-label="Contrast"
                  />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm">Saturation</span>
                    <span className="text-sm text-muted-foreground">{saturation}%</span>
                  </div>
                  <Slider 
                    value={[saturation]} 
                    min={0} 
                    max={200}
                    step={1}
                    onValueChange={(val) => setSaturation(val[0])}
                    disabled={processing}
                    aria-label="Saturation"
                  />
                </div>
              </div>

              <div className="mt-4">
                <h3 className="text-sm font-medium mb-2">Presets</h3>
                <div className="grid grid-cols-3 gap-3">
                  <Button 
                    variant="outline" 
                    className="flex flex-col items-center p-3 h-auto" 
                    onClick={() => applyPresetFilter('normal')}
                  >
                    <div className="w-12 h-12 rounded bg-gray-200 mb-2"></div>
                    <span className="text-xs">Normal</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="flex flex-col items-center p-3 h-auto" 
                    onClick={() => applyPresetFilter('warm')}
                  >
                    <div className="w-12 h-12 rounded bg-amber-200 mb-2"></div>
                    <span className="text-xs">Warm</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="flex flex-col items-center p-3 h-auto" 
                    onClick={() => applyPresetFilter('cool')}
                  >
                    <div className="w-12 h-12 rounded bg-blue-200 mb-2"></div>
                    <span className="text-xs">Cool</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="flex flex-col items-center p-3 h-auto" 
                    onClick={() => applyPresetFilter('vintage')}
                  >
                    <div className="w-12 h-12 rounded bg-gray-300 mb-2"></div>
                    <span className="text-xs">Vintage</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="flex flex-col items-center p-3 h-auto" 
                    onClick={() => applyPresetFilter('vivid')}
                  >
                    <div className="w-12 h-12 rounded bg-green-200 mb-2"></div>
                    <span className="text-xs">Vivid</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="flex flex-col items-center p-3 h-auto" 
                    onClick={() => applyPresetFilter('bw')}
                  >
                    <div className="w-12 h-12 rounded bg-gray-400 mb-2"></div>
                    <span className="text-xs">B&W</span>
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
          
          <div>
            <h3 className="text-sm font-medium mb-2">Timeline</h3>
            <div className="flex items-center gap-2 mb-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={togglePlay}
                disabled={processing}
                className="gap-1"
              >
                {playing ? <Pause size={14} /> : <Play size={14} />}
                {playing ? "Pause" : "Play"}
              </Button>
              <div className="text-sm font-mono">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>
            
            <Slider 
              value={[currentTime]} 
              min={0} 
              max={duration || 100}
              step={0.01}
              onValueChange={handleSeek}
              disabled={processing}
              className="mt-2"
            />
          </div>
        </div>
      </div>
      
      <div className="flex justify-end gap-2 mt-4">
        <Button 
          variant="outline" 
          onClick={onCancel}
          disabled={processing}
        >
          Cancel
        </Button>
        <Button 
          className="bg-forest hover:bg-forest-dark" 
          onClick={saveVideo}
          disabled={processing}
        >
          {processing ? (
            <span className="flex items-center">
              <span className="animate-spin mr-2">◌</span>
              Processing...
            </span>
          ) : (
            <>
              <Check size={16} className="mr-2" />
              Apply Changes
            </>
          )}
        </Button>
      </div>

      <Dialog open={showHelpDialog} onOpenChange={setShowHelpDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Enhanced Video Editor Help</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div>
              <h4 className="font-medium">Crop Tab</h4>
              <p className="text-sm text-muted-foreground">
                Adjust aspect ratio and horizontal position to crop your video.
                Choose between 9:16 (vertical), 1:1 (square), or 4:5 aspect ratios.
              </p>
            </div>
            <div>
              <h4 className="font-medium">Filters Tab</h4>
              <p className="text-sm text-muted-foreground">
                Adjust brightness, contrast, and saturation to enhance the look of your video.
                Use the reset button to revert to default settings or try preset filters.
              </p>
            </div>
            <div>
              <h4 className="font-medium">Timeline</h4>
              <p className="text-sm text-muted-foreground">
                Use the slider to preview different parts of your video. Click Play/Pause to control playback.
              </p>
            </div>
            <div>
              <h4 className="font-medium">Tips</h4>
              <ul className="text-sm text-muted-foreground list-disc pl-5 space-y-1">
                <li>Use the mute button to toggle sound</li>
                <li>Preview your changes in real-time before applying</li>
                <li>For best results, use videos with good lighting</li>
                <li>If advanced processing fails, a basic crop will be applied</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowHelpDialog(false)}>Got it</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export default EnhancedVideoEditor;
