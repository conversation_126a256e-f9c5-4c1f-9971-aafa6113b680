import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Crown, Check, Smartphone, Globe, RefreshCw } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useRevenueCatContext, shouldUseRevenueCat, getPlatformPricing } from "@/contexts/RevenueCatContext";
import { toast } from "sonner";
const YEARLY_OPTION = {
  id: "yearly",
  priceId: "price_1RjhctF1bISMFshisXLZHRMG",
  name: "Yearly Plan",
  price: "$99.00",
  originalPrice: "$199.00",
  duration: "/year",
  features: ["Reach Serious Whitetail Hunters Only", "Get in Front of Buyers, Not Browsers", "Generate Direct Leads Instantly", "Stand Out at the Top of Searches", "Be Part of the Hunting Community, Not Just Another Ad", "Turn Exposure Into Bookings and Sales", "Build Brand Loyalty with Real Outdoorsmen"]
};

const ServicesPromotionCard = () => {
  const [loading, setLoading] = useState(false);
  const isNative = shouldUseRevenueCat();

  // RevenueCat context (only used on mobile)
  const revenueCatContext = isNative ? useRevenueCatContext() : null;
  const handlePromoteServices = async () => {
    try {
      setLoading(true);

      if (isNative && revenueCatContext) {
        // Use RevenueCat for mobile platforms (when available)
        console.log('Using RevenueCat for mobile purchase');
        const success = await revenueCatContext.purchaseAnnualSubscription();

        if (!success) {
          // Error handling is done in the RevenueCat hook
          return;
        }
      } else {
        // Use Stripe for web platform
        console.log('Using Stripe for web purchase');
        const {
          data,
          error
        } = await supabase.functions.invoke('create-promotion-checkout', {
          body: {
            priceId: YEARLY_OPTION.priceId
          }
        });

        if (error) {
          console.error('Error creating checkout:', error);
          toast.error("Failed to create checkout session");
          return;
        }

        if (data?.url) {
          // Open Stripe checkout in a new tab
          window.open(data.url, '_blank');
        } else {
          toast.error("No checkout URL received");
        }
      }
    } catch (error) {
      console.error('Error in handlePromoteServices:', error);
      toast.error("Failed to start promotion checkout");
    } finally {
      setLoading(false);
    }
  };

  const handleRestorePurchases = async () => {
    if (!isNative || !revenueCatContext) return;

    try {
      setLoading(true);
      await revenueCatContext.restorePurchases();
    } catch (error) {
      console.error('Error restoring purchases:', error);
    } finally {
      setLoading(false);
    }
  };
  // Get platform-specific pricing
  const productInfo = isNative && revenueCatContext ? revenueCatContext.getProductInfo() : null;
  const pricing = getPlatformPricing(productInfo);

  // Check if user is already subscribed (mobile only)
  const isSubscribed = isNative && revenueCatContext ? revenueCatContext.isSubscribed : false;

  // Show loading state for RevenueCat initialization
  const isRevenueCatLoading = isNative && revenueCatContext ? revenueCatContext.isLoading : false;

  // Check if RevenueCat has errors (fallback to web)
  const hasRevenueCatError = isNative && revenueCatContext ? !!revenueCatContext.error : false;
  const shouldFallbackToWeb = isNative && hasRevenueCatError;

  return <Card className="bg-gradient-to-br from-yellow-50 to-amber-50 border-yellow-200">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-yellow-600" />
            <CardTitle className="text-lg font-semibold text-yellow-800">
              Promote Your Services
            </CardTitle>
          </div>
          <div className="flex items-center gap-1">
            {isNative ? (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs">
                <Smartphone className="h-3 w-3 mr-1" />
                Mobile
              </Badge>
            ) : (
              <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                <Globe className="h-3 w-3 mr-1" />
                Web
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-yellow-700">Promote Your Services and put your business front and center. Gain priority placement, reach more hunters, and turn views into bookings. It's time to stand out—let us help you get there.</p>

        {/* Show subscription status for mobile users */}
        {isNative && isSubscribed && (
          <div className="rounded-lg border border-green-400 bg-green-50 p-4">
            <div className="flex items-center gap-2 text-green-800">
              <Check className="h-5 w-5" />
              <span className="font-medium">Active Subscription</span>
            </div>
            <p className="text-sm text-green-700 mt-1">
              Your services are currently being promoted!
            </p>
          </div>
        )}

        {/* Show pricing and purchase options */}
        {(!isNative || !isSubscribed) && (
          <>
            <div className="rounded-lg border border-yellow-400 bg-yellow-50 p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-yellow-800">{YEARLY_OPTION.name}</span>
                    <span className="px-2 py-1 text-xs font-medium bg-red-200 text-red-800 rounded-full">
                      Early Launch Discount
                    </span>
                  </div>
                  <div className="text-sm text-yellow-600 mt-1">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">{pricing.price}</span>
                      <span className="text-xs line-through text-gray-500">{pricing.originalPrice}</span>
                      <span>{YEARLY_OPTION.duration}</span>
                    </div>
                    {isNative && productInfo && (
                      <p className="text-xs text-yellow-600 mt-1">
                        {pricing.discountPercentage}% off • {productInfo.currencyCode}
                      </p>
                    )}
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                {YEARLY_OPTION.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm text-yellow-600">
                    <Check className="h-4 w-4" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Button
                className="w-full bg-yellow-600 hover:bg-yellow-700 text-white"
                size="sm"
                onClick={handlePromoteServices}
                disabled={loading || isRevenueCatLoading}
              >
                {loading ? (
                  isNative ? "Processing purchase..." : "Creating checkout..."
                ) : (
                  `Promote Services - ${pricing.price}/year`
                )}
              </Button>

              {/* Restore purchases button for mobile */}
              {isNative && (
                <Button
                  variant="outline"
                  className="w-full text-yellow-700 border-yellow-300 hover:bg-yellow-50"
                  size="sm"
                  onClick={handleRestorePurchases}
                  disabled={loading || isRevenueCatLoading}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Restore Purchases
                </Button>
              )}
            </div>
          </>
        )}

        {/* RevenueCat loading state */}
        {isNative && isRevenueCatLoading && (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-600 mx-auto mb-2"></div>
            <p className="text-sm text-yellow-700">Loading subscription info...</p>
          </div>
        )}

        {/* RevenueCat error state with fallback */}
        {isNative && hasRevenueCatError && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center gap-2 text-orange-800 mb-2">
              <RefreshCw className="h-4 w-4" />
              <span className="font-medium">Mobile Store Unavailable</span>
            </div>
            <p className="text-sm text-orange-700 mb-3">
              {revenueCatContext?.error || 'Unable to load mobile subscription options.'}
            </p>
            <p className="text-xs text-orange-600 mb-3">
              Using web checkout as fallback. You can still purchase the subscription through our website.
            </p>
            <Button
              className="w-full bg-orange-600 hover:bg-orange-700 text-white"
              size="sm"
              onClick={handlePromoteServices}
              disabled={loading}
            >
              {loading ? "Creating checkout..." : `Continue with Web Checkout - ${pricing.price}/year`}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>;
};
export default ServicesPromotionCard;