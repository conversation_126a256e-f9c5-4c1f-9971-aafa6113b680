
import { useState } from "react";
import { Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface MultiDeleteButtonProps {
  videoIds: string[];
  userId: string;
  onDeleteSuccess?: () => void;
  disabled?: boolean;
}

const MultiDeleteButton = ({ 
  videoIds, 
  userId, 
  onDeleteSuccess,
  disabled = false
}: MultiDeleteButtonProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleDelete = async () => {
    if (videoIds.length === 0 || !userId) {
      console.error("No videos selected or missing userId");
      return;
    }
    
    try {
      setIsDeleting(true);
      console.log(`Attempting to delete ${videoIds.length} videos for user ID: ${userId}`);
      console.log("Video IDs to delete:", videoIds);

      // Delete videos one by one
      let successCount = 0;
      let errorCount = 0;

      for (const videoId of videoIds) {
        // First delete any comments associated with the video
        const { error: commentsError } = await supabase
          .from('comments')
          .delete()
          .eq('video_id', videoId);
          
        if (commentsError) {
          console.error(`Error deleting comments for video ${videoId}:`, commentsError);
          // Continue with video deletion even if comment deletion fails
        } else {
          console.log(`Successfully deleted comments for video ${videoId}`);
        }
        
        // Delete any likes associated with the video
        const { error: likesError } = await supabase
          .from('likes')
          .delete()
          .eq('video_id', videoId);
          
        if (likesError) {
          console.error(`Error deleting likes for video ${videoId}:`, likesError);
          // Continue with video deletion even if likes deletion fails
        } else {
          console.log(`Successfully deleted likes for video ${videoId}`);
        }

        // Delete the video
        const { error } = await supabase
          .from('videos')
          .delete()
          .eq('id', videoId)
          .eq('user_id', userId);

        if (error) {
          console.error(`Error deleting video ${videoId}:`, error);
          errorCount++;
        } else {
          console.log(`Successfully deleted video ${videoId}`);
          successCount++;
        }
      }

      if (successCount > 0) {
        toast.success(`Successfully deleted ${successCount} video${successCount !== 1 ? 's' : ''}`);
        if (onDeleteSuccess) {
          onDeleteSuccess();
        }
      }
      
      if (errorCount > 0) {
        toast.error(`Failed to delete ${errorCount} video${errorCount !== 1 ? 's' : ''}`);
      }
      
      setDialogOpen(false);
    } catch (error) {
      console.error("Unexpected error deleting videos:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <AlertDialogTrigger asChild>
        <Button 
          variant="destructive"
          size="sm"
          disabled={disabled || videoIds.length === 0}
          className="flex items-center gap-2"
        >
          <Trash2 size={16} />
          <span className="text-xs">Delete Selected ({videoIds.length})</span>
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete {videoIds.length} selected {videoIds.length === 1 ? 'video' : 'videos'}.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction 
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-500 hover:bg-red-600"
          >
            {isDeleting ? "Deleting..." : "Delete Selected"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default MultiDeleteButton;
