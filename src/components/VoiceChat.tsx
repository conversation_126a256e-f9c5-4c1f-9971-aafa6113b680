import React, { useRef, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>c<PERSON>ff, Loader2, MessageCircle, AlertCircle, Brain, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import { VoiceRecorder } from "capacitor-voice-recorder";
import { supabase } from '@/integrations/supabase/client';

interface VoiceChatProps {
  isOpen: boolean;
  onClose: () => void;
  onTextReceived?: (text: string) => void;
}

const VoiceChat: React.FC<VoiceChatProps> = ({ isOpen, onClose, onTextReceived }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  

  const startRecording = async () => {
    try {
      setError(null);
      
      // Check if device can record voice
      const canDeviceRecord = await VoiceRecorder.canDeviceVoiceRecord();
      if (canDeviceRecord.value) {
        let hasPermission = await VoiceRecorder.hasAudioRecordingPermission();
        if (!hasPermission.value) {
          hasPermission = await VoiceRecorder.requestAudioRecordingPermission();
        } 
        if (hasPermission.value) {
          await VoiceRecorder.startRecording();
        }
      }

      setIsRecording(true);
      toast.success('🎤 Recording started');

    } catch (error) {
      console.error('Error starting recording:', error);
      setError('Failed to access microphone. Please check permissions.');
      toast.error('🎤 Microphone access denied');
    }
  };

  const stopRecording = async () => {
    const result = await VoiceRecorder.getCurrentStatus();
    if (result.status === 'RECORDING') {
      const result = await VoiceRecorder.stopRecording();
      processAudio(result.value.recordDataBase64);
      setIsRecording(false);
      setIsProcessing(true);
      toast.info('🔄 Processing audio...');
    }
  };

  const processAudio = async (base64Audio: string) => {
    try {

      // // Create audio blob
      // const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
      
      // // Convert to base64
      // const arrayBuffer = await audioBlob.arrayBuffer();
      // const uint8Array = new Uint8Array(arrayBuffer);
      // const base64Audio = btoa(String.fromCharCode(...uint8Array));

      console.log('Sending audio for transcription...');

      // Send to voice-to-text Edge Function
      const { data, error } = await supabase.functions.invoke('voice-to-text', {
        body: { audio: base64Audio }
      });

      if (error) {
        console.error('Supabase function error:', error);
        throw new Error(error.message || 'Failed to transcribe audio');
      }

      if (data?.error) {
        console.error('Voice-to-text error:', data.error);
        throw new Error(data.error);
      }

      const transcribedText = data.text || '';
      console.log('Transcription result:', transcribedText);

      if (transcribedText.trim()) {
        setTranscript(transcribedText);
        
        // Send the transcribed text to the parent component
        if (onTextReceived) {
          onTextReceived(transcribedText);
        }
        
        toast.success('🎯 Voice converted to text successfully!');
        
        // Clear transcript after a delay
        setTimeout(() => {
          setTranscript('');
        }, 3000);
      } else {
        toast.error('No speech detected. Please try again.');
      }

    } catch (error) {
      console.error('Error processing audio:', error);
      setError(error instanceof Error ? error.message : 'Failed to process audio');
      toast.error('❌ Failed to convert voice to text');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRecordingToggle = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Back Button Header */}
      <div className="flex items-center justify-between mb-4">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onClose}
          className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
      </div>

      {/* Status Header */}
      <div className="flex items-center justify-center mb-4">
        <div className="flex items-center space-x-3 px-4 py-2 rounded-full bg-muted/50 text-sm">
          <Brain className="w-4 h-4 text-forest" />
          <div className={`w-2 h-2 rounded-full transition-all duration-300 ${
            isRecording ? 'bg-red-500 animate-pulse' : 
            isProcessing ? 'bg-yellow-500 animate-pulse' : 
            'bg-green-500'
          }`} />
          <span className="text-muted-foreground font-medium">
            {isRecording ? 'Recording Voice...' : 
             isProcessing ? 'Converting to Text...' : 
             'Voice to Text Ready'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div>
              <p className="font-medium text-red-800">Voice Recording Error</p>
              <p className="text-red-600 text-sm mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Main Interface */}
      <Card className="border border-border/50 shadow-lg">
        <CardContent className="p-4 md:p-6">
          {/* Recording Status */}
          <div className="flex items-center justify-center mb-4 md:mb-6">
            <div className="relative">
              <div className={`w-16 h-16 md:w-20 md:h-20 rounded-full flex items-center justify-center transition-all duration-300 ${
                isProcessing 
                  ? 'bg-gradient-to-br from-yellow-500 to-yellow-600 shadow-xl' 
                  : isRecording 
                    ? 'bg-gradient-to-br from-red-500 to-red-600 shadow-xl' 
                    : 'bg-gradient-to-br from-forest to-forest-light shadow-xl'
              }`}>
                {isProcessing ? (
                  <Loader2 className="w-6 h-6 md:w-8 md:h-8 text-white animate-spin" />
                ) : isRecording ? (
                  <Mic className="w-6 h-6 md:w-8 md:h-8 text-white animate-pulse" />
                ) : (
                  <Mic className="w-6 h-6 md:w-8 md:h-8 text-white" />
                )}
              </div>
              
              {/* Animated rings for recording */}
              {isRecording && (
                <>
                  <div className="absolute inset-0 rounded-full border-2 border-red-500 animate-ping opacity-20" />
                  <div className="absolute inset-0 rounded-full border border-red-500 animate-ping delay-150 opacity-10" />
                </>
              )}
            </div>
          </div>

          {/* Status Text */}
          <div className="text-center mb-4 md:mb-6">
            {isProcessing ? (
              <div>
                <p className="font-medium text-yellow-700 mb-1">🔄 Converting Voice to Text</p>
                <p className="text-sm text-muted-foreground">Processing your speech with AI...</p>
              </div>
            ) : isRecording ? (
              <div>
                <p className="font-medium text-red-700 mb-1">🎤 Recording...</p>
                <p className="text-sm text-muted-foreground">Speak clearly about your hunting question</p>
              </div>
            ) : (
              <div>
                <p className="font-medium mb-1">🎯 Voice to Text Ready</p>
                <p className="text-sm text-muted-foreground">Tap to record your hunting question</p>
              </div>
            )}
          </div>

          {/* Transcript Display */}
          {transcript && (
            <div className="mb-4 md:mb-6 p-3 md:p-4 bg-gradient-to-r from-forest/5 to-forest-light/5 rounded-lg border border-forest/20">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-forest to-forest-light rounded-full flex items-center justify-center flex-shrink-0">
                  <MessageCircle className="w-3 h-3 md:w-4 md:h-4 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-forest mb-1">Transcribed Text:</p>
                  <p className="text-sm text-foreground leading-relaxed">{transcript}</p>
                </div>
              </div>
            </div>
          )}

          {/* Recording Button */}
          <div className="text-center">
            <Button 
              onClick={handleRecordingToggle}
              disabled={isProcessing}
              size="lg"
              className={`px-6 md:px-8 py-3 shadow-lg ${
                isRecording 
                  ? 'bg-red-500 hover:bg-red-600 text-white' 
                  : 'bg-gradient-to-r from-forest to-forest-light hover:from-forest/90 hover:to-forest-light/90 text-white'
              }`}
            >
              {isRecording ? (
                <>
                  <MicOff className="w-4 h-4 mr-2" />
                  Stop Recording
                </>
              ) : (
                <>
                  <Mic className="w-4 h-4 mr-2" />
                  Start Recording
                </>
              )}
            </Button>
          </div>

          {/* Instructions */}
          <div className="mt-4 md:mt-6 p-3 md:p-4 bg-muted/30 rounded-lg">
            <h4 className="font-medium text-sm mb-2 text-center">📝 How it Works</h4>
            <div className="text-xs text-muted-foreground space-y-1">
              <div>• Tap "Start Recording" and speak your hunting question</div>
              <div>• Your voice will be converted to text using AI</div>
              <div>• The text will be sent to the hunting AI for response</div>
              <div>• Speak clearly for best transcription results</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VoiceChat;
