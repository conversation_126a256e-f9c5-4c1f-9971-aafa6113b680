import { useState, useRef, useEffect } from "react";
import { Check, X, Play, Pause, Crop, RotateCcw, Volume2, VolumeX, Scissors, Timer, ZoomIn, ZoomOut, Move } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { toast } from "sonner";

// Define the editor settings interface to pass between components
export interface VideoEditorSettings {
  zoomLevel: number;
  cropX: number;
  aspectRatio: number;
}

interface VideoEditorProps {
  videoSrc: string;
  onSave: (blob: Blob) => void;
  onCancel: () => void;
  // Add optional props to persist settings
  initialSettings?: VideoEditorSettings;
  onSettingsChange?: (settings: VideoEditorSettings) => void;
}

const VideoEditor = ({ 
  videoSrc, 
  onSave, 
  onCancel,
  initialSettings,
  onSettingsChange 
}: VideoEditorProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const timelineCanvasRef = useRef<HTMLCanvasElement>(null);
  const [aspectRatio, setAspectRatio] = useState<number>(initialSettings?.aspectRatio || 9/16);
  const [cropX, setCropX] = useState<number>(initialSettings?.cropX || 0);
  const [playing, setPlaying] = useState<boolean>(true); // Set initial state to true
  const [duration, setDuration] = useState<number>(0);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [videoLoaded, setVideoLoaded] = useState<boolean>(false);
  const [processing, setProcessing] = useState<boolean>(false);
  const [maxCropX, setMaxCropX] = useState<number>(0);
  const [thumbnails, setThumbnails] = useState<string[]>([]);
  const [showHelpDialog, setShowHelpDialog] = useState<boolean>(false);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [videoWidth, setVideoWidth] = useState<number>(0);
  const [videoHeight, setVideoHeight] = useState<number>(0);
  const [initialFrameDrawn, setInitialFrameDrawn] = useState<boolean>(false);
  const [startTime, setStartTime] = useState<number>(0);
  const [endTime, setEndTime] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<"crop" | "trim">("crop");
  const [isDraggingTrim, setIsDraggingTrim] = useState<boolean>(false);
  const [zoomLevel, setZoomLevel] = useState<number>(initialSettings?.zoomLevel || 1);
  const [maxZoomLevel, setMaxZoomLevel] = useState<number>(2); // Maximum zoom level
  const [isDraggingZoom, setIsDraggingZoom] = useState<boolean>(false);

  // Notify parent component when settings change
  useEffect(() => {
    if (onSettingsChange && videoLoaded) {
      onSettingsChange({
        zoomLevel,
        cropX,
        aspectRatio
      });
    }
  }, [zoomLevel, cropX, aspectRatio, videoLoaded, onSettingsChange]);
  
  useEffect(() => {
    if (!videoRef.current) return;
    
    const video = videoRef.current;
    video.muted = isMuted;
    
    const handleMetadataLoaded = () => {
      if (!video) return;
      
      setDuration(video.duration);
      setEndTime(video.duration); // Initialize end time to full duration
      
      const vWidth = video.videoWidth;
      const vHeight = video.videoHeight;
      setVideoWidth(vWidth);
      setVideoHeight(vHeight);
      
      // Adjust max zoom based on video dimensions
      // Wider videos can be zoomed in more
      const calculatedMaxZoom = Math.max(2, vWidth / vHeight);
      setMaxZoomLevel(Math.min(calculatedMaxZoom, 4)); // Limit max zoom to 4x
      
      const cropWidth = vHeight * aspectRatio;
      const newMaxCropX = Math.max(0, vWidth - cropWidth);
      setMaxCropX(newMaxCropX);
      
      // Center the crop area
      const optimalCropX = (vWidth - cropWidth) / 2;
      setCropX(Math.max(0, Math.min(optimalCropX, newMaxCropX)));
      
      // Ensure we draw an initial frame before declaring video as loaded
      video.currentTime = 0.1; // Set to a slight offset to avoid black frame
    };
    
    const handleTimeUpdate = () => {
      if (!video) return;
      setCurrentTime(video.currentTime);
      
      if (!initialFrameDrawn) {
        drawFrameToCanvas();
        setInitialFrameDrawn(true);
        setVideoLoaded(true);
        generateThumbnails();
        
        // Auto-play when first frame is drawn
        video.play().catch(error => {
          console.error("Failed to auto-play video:", error);
          // Don't show error toast as this is often browser policy related
          setPlaying(false);
        });
        
        const hasSeenHelp = localStorage.getItem('video-editor-help-seen');
        if (!hasSeenHelp) {
          setShowHelpDialog(true);
          localStorage.setItem('video-editor-help-seen', 'true');
        }
      } else if (!isDragging) {
        drawFrameToCanvas();
      }
      
      // Enforce trim boundaries during playback
      if (video.currentTime < startTime) {
        video.currentTime = startTime;
      }
      
      if (video.currentTime >= endTime) {
        video.currentTime = startTime;
        if (playing) {
          video.play().catch(error => {
            console.error("Failed to loop video within trim bounds:", error);
          });
        }
      }
    };
    
    const handleCanPlay = () => {
      if (!initialFrameDrawn) {
        // Draw the first frame when the video is ready to play
        drawFrameToCanvas();
        setInitialFrameDrawn(true);
        setVideoLoaded(true);
      }
    };
    
    video.addEventListener('loadedmetadata', handleMetadataLoaded);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('canplay', handleCanPlay);
    
    // Add event listener for when play actually starts
    const handlePlay = () => {
      setPlaying(true);
    };
    
    // Add event listener for when video pauses
    const handlePause = () => {
      setPlaying(false);
    };
    
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    
    return () => {
      video.removeEventListener('loadedmetadata', handleMetadataLoaded);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.pause();
    };
  }, [videoSrc, aspectRatio, isMuted, isDragging]);
  
  // Effect to update crop dimensions when aspect ratio changes
  useEffect(() => {
    if (videoLoaded && videoWidth > 0 && videoHeight > 0) {
      const cropWidth = videoHeight * aspectRatio;
      const newMaxCropX = Math.max(0, videoWidth - cropWidth);
      
      setMaxCropX(newMaxCropX);
      
      // Center the crop area
      const optimalCropX = (videoWidth - cropWidth) / 2;
      setCropX(Math.max(0, Math.min(optimalCropX, newMaxCropX)));
      
      drawFrameToCanvas();
    }
  }, [aspectRatio, videoLoaded, videoWidth, videoHeight]);
  
  // Effect to update when zoom level changes
  useEffect(() => {
    if (videoLoaded && videoWidth > 0 && videoHeight > 0) {
      drawFrameToCanvas();
      
      // Adjust max crop X based on zoom level
      // As we zoom in, the visible area gets smaller, so we need to reduce max crop X
      const cropWidth = videoHeight * aspectRatio;
      const zoomedWidth = cropWidth / zoomLevel; // The actual visible width gets smaller as we zoom
      const newMaxCropX = Math.max(0, videoWidth - zoomedWidth);
      
      // Ensure current crop X is valid with new zoom level
      if (cropX > newMaxCropX) {
        setCropX(newMaxCropX);
      }
      
      setMaxCropX(newMaxCropX);
    }
  }, [zoomLevel, videoLoaded]);
  
  const generateThumbnails = async () => {
    if (!videoRef.current || !videoLoaded) return;
    
    const video = videoRef.current;
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const thumbWidth = 80;
    const thumbHeight = 45;
    canvas.width = thumbWidth;
    canvas.height = thumbHeight;
    
    const numThumbnails = Math.min(10, Math.ceil(video.duration / 2));
    const newThumbnails: string[] = [];
    
    try {
      for (let i = 0; i < numThumbnails; i++) {
        const time = i === 0 ? 0.1 : (i / (numThumbnails - 1)) * video.duration;
        
        video.currentTime = time;
        
        await new Promise<void>((resolve) => {
          const seekHandler = () => {
            video.removeEventListener('seeked', seekHandler);
            resolve();
          };
          video.addEventListener('seeked', seekHandler);
        });
        
        ctx.drawImage(video, 0, 0, thumbWidth, thumbHeight);
        const dataUrl = canvas.toDataURL('image/jpeg', 0.7);
        newThumbnails.push(dataUrl);
      }
      
      video.currentTime = 0;
      setThumbnails(newThumbnails);
      
      setTimeout(() => {
        drawTimelineCanvas();
      }, 100);
    } catch (error) {
      console.error("Error generating thumbnails:", error);
      toast.error("Couldn't generate thumbnails. Please try again.");
    }
  };
  
  const drawTimelineCanvas = () => {
    if (!timelineCanvasRef.current || thumbnails.length === 0) return;
    
    const canvas = timelineCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const thumbWidth = canvas.width / thumbnails.length;
    
    thumbnails.forEach((thumbnail, index) => {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, index * thumbWidth, 0, thumbWidth, canvas.height);
        
        const time = (index / (thumbnails.length - 1)) * duration;
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(index * thumbWidth, canvas.height - 15, thumbWidth, 15);
        ctx.fillStyle = 'white';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(formatTime(time), index * thumbWidth + (thumbWidth / 2), canvas.height - 4);
      };
      img.src = thumbnail;
    });
    
    drawCurrentTimeIndicator();
    drawTrimIndicators();
  };
  
  const drawFrameToCanvas = () => {
    if (!videoRef.current || !canvasRef.current || !videoLoaded) return;
    
    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Ensure we have valid dimensions
    if (videoWidth === 0 || videoHeight === 0) return;
    
    const cropWidth = videoHeight * aspectRatio;
    const boundedCropX = Math.max(0, Math.min(cropX, maxCropX));
    
    canvas.width = 320;
    canvas.height = 320 / aspectRatio;
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    try {
      // FIX: Improved zoom calculations for precise zoom center
      // When zooming, we take a smaller portion of the source image
      if (zoomLevel > 1) {
        const zoomedCropWidth = cropWidth / zoomLevel;
        const zoomedVideoHeight = videoHeight / zoomLevel;
        
        // Center the zoom based on current crop position
        const cropCenter = boundedCropX + (cropWidth / 2);
        const zoomOffsetX = cropCenter - (zoomedCropWidth / 2);
        const zoomOffsetY = (videoHeight - zoomedVideoHeight) / 2;
        
        // Ensure zoom offset doesn't go outside video boundaries
        const clampedZoomOffsetX = Math.max(0, Math.min(zoomOffsetX, videoWidth - zoomedCropWidth));
        
        ctx.drawImage(
          video,
          clampedZoomOffsetX, zoomOffsetY, zoomedCropWidth, zoomedVideoHeight,
          0, 0, canvas.width, canvas.height
        );
      } else {
        // Regular drawing without zoom
        ctx.drawImage(
          video,
          boundedCropX, 0, cropWidth, videoHeight,
          0, 0, canvas.width, canvas.height
        );
      }
    } catch (error) {
      console.error("Error drawing video frame:", error);
    }
  };
  
  const drawCurrentTimeIndicator = () => {
    if (!timelineCanvasRef.current || !videoLoaded || duration === 0) return;
    
    const canvas = timelineCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // First draw the trim region
    drawTrimIndicators();
    
    // Then redraw thumbnails to avoid erasing them
    thumbnails.forEach((thumbnail, index) => {
      const thumbWidth = canvas.width / thumbnails.length;
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, index * thumbWidth, 0, thumbWidth, canvas.height);
        
        const time = (index / (thumbnails.length - 1)) * duration;
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(index * thumbWidth, canvas.height - 15, thumbWidth, 15);
        ctx.fillStyle = 'white';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(formatTime(time), index * thumbWidth + (thumbWidth / 2), canvas.height - 4);
        
        // Only draw the time indicator after the last thumbnail is drawn
        if (index === thumbnails.length - 1) {
          const position = (currentTime / duration) * canvas.width;
          ctx.fillStyle = 'red';
          ctx.fillRect(position - 1, 0, 2, canvas.height);
        }
      };
      img.src = thumbnail;
    });
  };
  
  const drawTrimIndicators = () => {
    if (!timelineCanvasRef.current || !videoLoaded || duration === 0) return;
    
    const canvas = timelineCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Calculate positions
    const startPosition = (startTime / duration) * canvas.width;
    const endPosition = (endTime / duration) * canvas.width;
    
    // Draw semi-transparent overlay for trimmed areas
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    // Left trimmed area
    ctx.fillRect(0, 0, startPosition, canvas.height);
    // Right trimmed area
    ctx.fillRect(endPosition, 0, canvas.width - endPosition, canvas.height);
    
    // Draw trim handles
    ctx.fillStyle = '#10b981'; // Forest green
    // Start handle
    ctx.fillRect(startPosition - 2, 0, 4, canvas.height);
    // End handle
    ctx.fillRect(endPosition - 2, 0, 4, canvas.height);
    
    // Draw trim time indicators
    ctx.fillStyle = 'white';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(formatTime(startTime), startPosition, 12);
    ctx.fillText(formatTime(endTime), endPosition, 12);
  };
  
  const togglePlay = () => {
    if (!videoRef.current || !videoLoaded) return;
    
    if (playing) {
      videoRef.current.pause();
      setPlaying(false);
    } else {
      // Set to start time if we're at end time
      if (videoRef.current.currentTime >= endTime) {
        videoRef.current.currentTime = startTime;
      }
      
      videoRef.current.play().catch(error => {
        console.error("Failed to play video:", error);
        toast.error("Couldn't play video. Try again.");
      });
      setPlaying(true);
    }
  };

  const toggleMute = () => {
    if (!videoRef.current) return;
    
    setIsMuted(!isMuted);
    videoRef.current.muted = !isMuted;
    
    toast.info(isMuted ? "Sound enabled" : "Sound muted");
  };

  const resetVideo = () => {
    if (!videoRef.current || !videoLoaded) return;
    
    videoRef.current.currentTime = startTime;
    setCurrentTime(startTime);
    drawFrameToCanvas();
    toast.info("Video reset to start trim point");
  };
  
  const handleCropPositionChange = (newCropX: number[]) => {
    if (!videoRef.current || !videoLoaded || newCropX.length === 0) return;
    
    const boundedCropX = Math.max(0, Math.min(newCropX[0], maxCropX));
    setCropX(boundedCropX);
    setIsDragging(true);
    
    requestAnimationFrame(() => drawFrameToCanvas());
  };
  
  const handleZoomChange = (newZoom: number[]) => {
    if (!videoLoaded || newZoom.length === 0) return;
    
    const newZoomLevel = newZoom[0];
    setZoomLevel(newZoomLevel);
    setIsDraggingZoom(true);
    
    // FIX: Always force a redraw when changing zoom level
    requestAnimationFrame(() => drawFrameToCanvas());
  };
  
  const handleZoomCommit = () => {
    setIsDraggingZoom(false);
    
    // FIX: Force a redraw on zoom commit to ensure settings persist
    requestAnimationFrame(() => drawFrameToCanvas());
    toast.info(`Zoom level: ${Math.round(zoomLevel * 100)}%`);
  };
  
  const handleCropPositionCommit = () => {
    setIsDragging(false);
    
    // Final redraw to ensure canvas is updated
    requestAnimationFrame(() => drawFrameToCanvas());
    
    // Provide visual feedback
    toast.info("Crop position updated");
  };
  
  const handleSeek = (newTime: number[]) => {
    if (!videoRef.current || !videoLoaded || newTime.length === 0) return;
    
    const clampedTime = Math.max(startTime, Math.min(newTime[0], endTime));
    videoRef.current.currentTime = clampedTime;
    setCurrentTime(clampedTime);
    drawFrameToCanvas();
  };
  
  const handleStartTimeChange = (newTime: number[]) => {
    if (!videoLoaded || newTime.length === 0) return;
    
    const clampedTime = Math.max(0, Math.min(newTime[0], endTime - 0.5));
    setStartTime(clampedTime);
    setIsDraggingTrim(true);
    
    // Update video current time if it's less than new start time
    if (videoRef.current && videoRef.current.currentTime < clampedTime) {
      videoRef.current.currentTime = clampedTime;
      setCurrentTime(clampedTime);
    }
    
    drawTimelineCanvas();
  };
  
  const handleEndTimeChange = (newTime: number[]) => {
    if (!videoLoaded || newTime.length === 0) return;
    
    const clampedTime = Math.max(startTime + 0.5, Math.min(newTime[0], duration));
    setEndTime(clampedTime);
    setIsDraggingTrim(true);
    
    // Update video current time if it's greater than new end time
    if (videoRef.current && videoRef.current.currentTime > clampedTime) {
      videoRef.current.currentTime = clampedTime;
      setCurrentTime(clampedTime);
    }
    
    drawTimelineCanvas();
  };
  
  const handleTrimCommit = () => {
    setIsDraggingTrim(false);
    toast.info(`Video trimmed to ${formatTime(endTime - startTime)} duration`);
    drawTimelineCanvas();
  };
  
  const changeAspectRatio = (newRatio: number) => {
    setAspectRatio(newRatio);
    toast.info(`Aspect ratio changed to ${newRatio === 9/16 ? "9:16" : newRatio === 4/5 ? "4:5" : "1:1"}`);
  };
  
  const saveVideo = async () => {
    if (!videoRef.current || !canvasRef.current || !videoLoaded) return;
    
    try {
      setProcessing(true);
      toast.info("Processing video...");
      
      const video = videoRef.current;
      
      if (playing) {
        video.pause();
        setPlaying(false);
      }
      
      // Use our stored dimensions to avoid issues
      const cropWidth = videoHeight * aspectRatio;
      const boundedCropX = Math.max(0, Math.min(cropX, maxCropX));
      
      const exportCanvas = document.createElement('canvas');
      const ctx = exportCanvas.getContext('2d');
      
      if (!ctx) {
        throw new Error("Failed to get export canvas context");
      }
      
      exportCanvas.width = cropWidth;
      exportCanvas.height = videoHeight;
      
      // Reset to beginning to ensure consistent cropping
      video.currentTime = startTime;
      
      await new Promise<void>((resolve) => {
        const seekHandler = () => {
          video.removeEventListener('seeked', seekHandler);
          resolve();
        };
        video.addEventListener('seeked', seekHandler);
      });
      
      let stream: MediaStream;
      let mediaRecorder: MediaRecorder;
      
      try {
        stream = exportCanvas.captureStream(30);
      } catch (error) {
        console.error("Canvas captureStream not supported, falling back to alternative method");
        toast.error("Video cropping not supported in this browser");
        setProcessing(false);
        return;
      }
      
      const chunks: BlobPart[] = [];
      
      const mimeTypes = [
        'video/webm;codecs=vp9',
        'video/webm;codecs=vp8',
        'video/webm',
        'video/mp4'
      ];
      
      let mimeType = '';
      for (const type of mimeTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          mimeType = type;
          break;
        }
      }
      
      if (!mimeType) {
        throw new Error("No supported media recorder format found");
      }
      
      mediaRecorder = new MediaRecorder(stream, { 
        mimeType, 
        videoBitsPerSecond: 2500000
      });
      
      mediaRecorder.ondataavailable = (e) => {
        if (e.data && e.data.size > 0) {
          chunks.push(e.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: mimeType });
        onSave(blob);
        setProcessing(false);
        toast.success("Video processed successfully!");
      };
      
      // Set the duration for recording
      const recordingDuration = endTime - startTime;
      
      // Reset to the start time
      video.currentTime = startTime;
      
      // Wait for the video to be ready at the start time
      await new Promise<void>((resolve) => {
        const onSeeked = () => {
          video.removeEventListener('seeked', onSeeked);
          resolve();
        };
        video.addEventListener('seeked', onSeeked);
      });
      
      mediaRecorder.start();
      
      // Play the video to process all frames
      video.play();
      
      // Set a timeout to stop recording based on trim duration
      setTimeout(() => {
        mediaRecorder.stop();
        video.pause();
      }, recordingDuration * 1000);
      
      // Render each frame with the crop and zoom applied
      const renderFrame = () => {
        // Apply zoom to exported video
        if (zoomLevel > 1) {
          const zoomedCropWidth = cropWidth / zoomLevel;
          const zoomedVideoHeight = videoHeight / zoomLevel;
          
          // Center the zoom around current crop position
          const zoomCenterX = boundedCropX + (cropWidth / 2);
          const zoomOffsetX = zoomCenterX - (zoomedCropWidth / 2);
          const zoomOffsetY = (videoHeight - zoomedVideoHeight) / 2;
          
          // Ensure zoom offset doesn't go outside video boundaries
          const clampedZoomOffsetX = Math.max(0, Math.min(zoomOffsetX, videoWidth - zoomedCropWidth));
          
          ctx.drawImage(
            video,
            clampedZoomOffsetX, zoomOffsetY, zoomedCropWidth, zoomedVideoHeight,
            0, 0, exportCanvas.width, exportCanvas.height
          );
        } else {
          // Regular drawing without zoom
          ctx.drawImage(
            video,
            boundedCropX, 0, cropWidth, videoHeight,
            0, 0, exportCanvas.width, exportCanvas.height
          );
        }
        
        if (!video.paused && !video.ended && video.currentTime < endTime) {
          requestAnimationFrame(renderFrame);
        }
      };
      
      requestAnimationFrame(renderFrame);
    } catch (error) {
      console.error("Error exporting video:", error);
      setProcessing(false);
      toast.error("There was an error processing the video. Please try again or use a different video format.");
    }
  };
  
  return (
    <div className="bg-background border border-border rounded-lg p-4 space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold flex items-center">
          <Scissors size={18} className="mr-2 text-forest" />
          Edit Video
        </h2>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={() => setShowHelpDialog(true)}>
            ?
          </Button>
          <Button variant="ghost" size="icon" onClick={onCancel} disabled={processing}>
            <X size={18} />
          </Button>
        </div>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4">
        <video 
          ref={videoRef}
          src={videoSrc}
          className="hidden"
          muted={isMuted}
          playsInline
          preload="auto"
          controls={false}
        />
        
        <div className="relative border border-border rounded-lg overflow-hidden bg-black flex justify-center items-center min-h-[320px]">
          {!videoLoaded ? (
            <div className="text-center p-4 animate-pulse">Loading video...</div>
          ) : (
            <>
              <canvas ref={canvasRef} className="max-w-full" />
              <div className="absolute bottom-2 right-2 flex gap-2">
                <Button 
                  variant="secondary" 
                  size="icon" 
                  className="bg-white/80 hover:bg-white text-forest rounded-full h-8 w-8"
                  onClick={toggleMute}
                >
                  {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
                </Button>
                <Button 
                  variant="secondary" 
                  size="icon" 
                  className="bg-white/80 hover:bg-white text-forest rounded-full h-8 w-8"
                  onClick={togglePlay}
                >
                  {playing ? <Pause size={16} /> : <Play size={16} />}
                </Button>
                <Button 
                  variant="secondary" 
                  size="icon" 
                  className="bg-white/80 hover:bg-white text-forest rounded-full h-8 w-8"
                  onClick={resetVideo}
                >
                  <RotateCcw size={16} />
                </Button>
              </div>
            </>
          )}
        </div>
        
        <div className="space-y-4 min-w-[200px]">
          <div className="flex gap-2 border-b pb-2">
            <Button
              variant={activeTab === "crop" ? "default" : "outline"}
              onClick={() => setActiveTab("crop")}
              className={`${activeTab === "crop" ? 'bg-forest hover:bg-forest-dark' : ''}`}
              disabled={processing}
            >
              <Crop size={16} className="mr-1" />
              Crop
            </Button>
            <Button
              variant={activeTab === "trim" ? "default" : "outline"}
              onClick={() => setActiveTab("trim")}
              className={`${activeTab === "trim" ? 'bg-forest hover:bg-forest-dark' : ''}`}
              disabled={processing}
            >
              <Timer size={16} className="mr-1" />
              Trim
            </Button>
          </div>
          
          {activeTab === "crop" && (
            <>
              <div>
                <h3 className="text-sm font-medium mb-2">Aspect Ratio</h3>
                <div className="flex gap-2">
                  <Button 
                    variant={aspectRatio === 9/16 ? "default" : "outline"}
                    onClick={() => changeAspectRatio(9/16)}
                    className={`w-full ${aspectRatio === 9/16 ? 'bg-forest hover:bg-forest-dark' : ''}`}
                    disabled={processing}
                  >
                    9:16
                  </Button>
                  <Button 
                    variant={aspectRatio === 1 ? "default" : "outline"}
                    onClick={() => changeAspectRatio(1)}
                    className={`w-full ${aspectRatio === 1 ? 'bg-forest hover:bg-forest-dark' : ''}`}
                    disabled={processing}
                  >
                    1:1
                  </Button>
                  <Button 
                    variant={aspectRatio === 4/5 ? "default" : "outline"}
                    onClick={() => changeAspectRatio(4/5)}
                    className={`w-full ${aspectRatio === 4/5 ? 'bg-forest hover:bg-forest-dark' : ''}`}
                    disabled={processing}
                  >
                    4:5
                  </Button>
                </div>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium">Zoom Level</h3>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <ZoomIn size={16} className="mr-1" />
                    <span>{Math.round(zoomLevel * 100)}%</span>
                  </div>
                </div>
                <Slider 
                  value={[zoomLevel]} 
                  min={1} 
                  max={maxZoomLevel}
                  step={0.1}
                  onValueChange={handleZoomChange}
                  onValueCommit={handleZoomCommit}
                  disabled={processing}
                  className="mt-2"
                  aria-label="Zoom level"
                />
                <div className="text-xs text-muted-foreground mt-1 flex justify-between">
                  <span className="flex items-center"><ZoomOut size={12} /> 100%</span>
                  <span className="flex items-center">{Math.round(maxZoomLevel * 100)}% <ZoomIn size={12} /></span>
                </div>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium">Horizontal Position</h3>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Move size={16} className="mr-1" />
                    <span>Drag to adjust</span>
                  </div>
                </div>
                <Slider 
                  value={[cropX]} 
                  min={0} 
                  max={maxCropX}
                  step={1}
                  onValueChange={handleCropPositionChange}
                  onValueCommit={() => handleCropPositionCommit()}
                  disabled={processing}
                  className="mt-2"
                  aria-label="Horizontal crop position"
                />
                <div className="text-xs text-muted-foreground mt-1 text-center">
                  {isDragging ? "Release to apply" : "Position: " + Math.round(cropX)}
                </div>
              </div>
            </>
          )}
          
          {activeTab === "trim" && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Trim Video</h3>
                <div className="text-sm">
                  Duration: {formatTime(endTime - startTime)}
                </div>
              </div>
              
              <div className="space-y-5">
                <div>
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span>Start Time:</span>
                    <span>{formatTime(startTime)}</span>
                  </div>
                  <Slider 
                    value={[startTime]} 
                    min={0} 
                    max={duration - 0.5}
                    step={0.1}
                    onValueChange={handleStartTimeChange}
                    onValueCommit={handleTrimCommit}
                    disabled={processing}
                    aria-label="Video start time"
                  />
                </div>
                
                <div>
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span>End Time:</span>
                    <span>{formatTime(endTime)}</span>
                  </div>
                  <Slider 
                    value={[endTime]} 
                    min={0.5} 
                    max={duration}
                    step={0.1}
                    onValueChange={handleEndTimeChange}
                    onValueCommit={handleTrimCommit}
                    disabled={processing}
                    aria-label="Video end time"
                  />
                </div>
                
                <div className="flex justify-between items-center text-sm text-muted-foreground">
                  <span>Original: {formatTime(duration)}</span>
                  <span>Trimmed: {formatTime(endTime - startTime)}</span>
                </div>
              </div>
            </div>
          )}
          
          <div>
            <h3 className="text-sm font-medium mb-2">Timeline</h3>
            <div className="flex items-center gap-2 mb-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={togglePlay}
                disabled={!videoLoaded || processing}
                className="gap-1"
              >
                {playing ? <Pause size={14} /> : <Play size={14} />}
                {playing ? "Pause" : "Play"}
              </Button>
              <div className="text-sm font-mono">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>
            
            <div className="relative mb-2 border border-border rounded-lg overflow-hidden h-12 bg-gray-100">
              <canvas 
                ref={timelineCanvasRef} 
                className="w-full h-full" 
                width={300}
                height={48}
              />
            </div>
            
            <Slider 
              value={[currentTime]} 
              min={startTime} 
              max={endTime}
              step={0.01}
              onValueChange={handleSeek}
              disabled={!videoLoaded || processing}
              className="mt-2"
            />
          </div>
        </div>
      </div>
      
      <div className="flex justify-end gap-2 mt-4">
        <Button 
          variant="outline" 
          onClick={onCancel}
          disabled={processing}
        >
          Cancel
        </Button>
        <Button 
          className="bg-forest hover:bg-forest-dark" 
          onClick={saveVideo}
          disabled={!videoLoaded || processing}
        >
          {processing ? (
            <span className="flex items-center">
              <span className="animate-spin mr-2">◌</span>
              Processing...
            </span>
          ) : (
            <>
              <Check size={16} className="mr-2" />
              Apply Changes
            </>
          )}
        </Button>
      </div>

      <Dialog open={showHelpDialog} onOpenChange={setShowHelpDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Video Editor Help</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div>
              <h4 className="font-medium">Crop Tab</h4>
              <p className="text-sm text-muted-foreground">Choose between 9:16 (vertical), 1:1 (square), or 4:5 aspect ratios for your video. Use the zoom slider to zoom in and the horizontal position slider to adjust the crop area.</p>
            </div>
            <div>
              <h4 className="font-medium">Trim Tab</h4>
              <p className="text-sm text-muted-foreground">Adjust the start and end points to keep only the parts of your video you want. Use the sliders to set precise start and end times.</p>
            </div>
            <div>
              <h4 className="font-medium">Timeline</h4>
              <p className="text-sm text-muted-foreground">Use the timeline to preview different parts of your video. The slider will only move within your trimmed section.</p>
            </div>
            <div>
              <h4 className="font-medium">Tips</h4>
              <ul className="text-sm text-muted-foreground list-disc pl-5 space-y-1">
                <li>Use the zoom slider to focus on specific parts of your video</li>
                <li>Use the mute button to toggle sound</li>
                <li>Trim your video first to focus on the best part</li>
                <li>Then crop and zoom to adjust the framing</li>
                <li>Processing may take a few moments depending on video length</li>
                <li>For best results, use videos with good lighting</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowHelpDialog(false)}>Got it</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export default VideoEditor;
