
import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

interface MobileScrollAreaProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
}

const MobileScrollArea: React.FC<MobileScrollAreaProps> = ({
  children,
  className,
  id
}) => {
  return (
    <ScrollArea 
      className={cn("h-full w-full overflow-y-auto -webkit-overflow-scrolling-touch page-content", className)}
      id={id}
    >
      {children}
    </ScrollArea>
  );
};

export default MobileScrollArea;
