
import { useState } from "react";
import { Save, Delete, Droplet } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ShotDetails from "./ShotDetails";
import TrackingRecommendations from "./TrackingRecommendations";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from "@/components/ui/select";

export type ShotPoint = {
  x: number;
  y: number;
  type: "entry" | "exit";
};

export type ShotAnalysis = {
  vitalHit: string;
  bloodColor: string;
  bloodDescription: string; 
  waitTime: string;
  trackingDistance: string;
  confidence: "high" | "medium" | "low";
};

export type DeerView = "broadside" | "quartering-away" | "quartering-to" | "frontal" | "rear";

// Enhanced blood colors with more accurate hunting data and field-tested analysis
export const BLOOD_COLORS = {
  "bright-arterial": {
    label: "Bright Red Arterial",
    color: "#FF0000",
    description: "Bright red, oxygen-rich blood that may spray or pump rhythmically",
    vitalHit: "Major Artery",
    waitTime: "Follow immediately but quietly - listen for crash",
    trackingDistance: "Usually 30-100 yards",
    confidence: "high" as const
  },
  "heart-blood": {
    label: "Deep Red Heart Blood",
    color: "#B22222",
    description: "Deep red, heavy flow blood, may initially spray then pool heavily",
    vitalHit: "Heart",
    waitTime: "Wait 30 minutes, then track - should be close",
    trackingDistance: "Typically 25-75 yards",
    confidence: "high" as const
  },
  "lung-frothy": {
    label: "Pink Frothy Lung Blood",
    color: "#FFB6C1",
    description: "Pink to light red with visible air bubbles or froth, may foam",
    vitalHit: "Double Lung",
    waitTime: "Wait 45 minutes - deer usually beds down quickly",
    trackingDistance: "50-125 yards typical",
    confidence: "high" as const
  },
  "single-lung": {
    label: "Medium Red Single Lung",
    color: "#DC143C",
    description: "Medium red blood, less frothy than double lung, steady flow",
    vitalHit: "Single Lung",
    waitTime: "Wait 3-4 hours minimum - critical not to bump",
    trackingDistance: "200-500+ yards possible",
    confidence: "medium" as const
  },
  "liver-blood": {
    label: "Dark Maroon Liver",
    color: "#800000",
    description: "Very dark red to maroon, thick consistency, may be sparse initially",
    vitalHit: "Liver",
    waitTime: "Wait 4-6 hours minimum - patience is key",
    trackingDistance: "100-400 yards if undisturbed",
    confidence: "medium" as const
  },
  "kidney-blood": {
    label: "Dark Red Kidney",
    color: "#8B0000",
    description: "Dark red blood, often mixed with urine, strong metallic smell",
    vitalHit: "Kidney",
    waitTime: "Wait 2-3 hours - deer will likely bed",
    trackingDistance: "150-300 yards typically",
    confidence: "medium" as const
  },
  "paunch-gut": {
    label: "Greenish Brown Gut Shot",
    color: "#8B4513",
    description: "Little blood, green/brown fluid, strong smell of stomach contents",
    vitalHit: "Paunch/Stomach",
    waitTime: "Wait 8-12 hours - overnight if possible",
    trackingDistance: "300-800+ yards if pushed",
    confidence: "low" as const
  },
  "intestinal": {
    label: "Brown Intestinal",
    color: "#654321",
    description: "Dark blood mixed with brown matter, foul odor, food particles",
    vitalHit: "Intestines",
    waitTime: "Wait 6-8 hours minimum - don't push",
    trackingDistance: "200-600 yards if left alone",
    confidence: "low" as const
  },
  "muscle-high": {
    label: "Bright Red Muscle (High)",
    color: "#FF4500",
    description: "Bright red but thin, intermittent drops, hair at impact site",
    vitalHit: "High Muscle Hit",
    waitTime: "Wait 4-6 hours - may recover if undisturbed",
    trackingDistance: "Could be 1000+ yards or recover completely",
    confidence: "low" as const
  },
  "muscle-low": {
    label: "Dark Red Muscle (Low)",
    color: "#A0522D",
    description: "Dark red, thick blood, steady drip, likely brisket or shoulder",
    vitalHit: "Low Muscle/Brisket",
    waitTime: "Wait 6-8 hours - deer may survive",
    trackingDistance: "Variable - could go miles",
    confidence: "low" as const
  },
  "leg-bone": {
    label: "Bright Red with Bone",
    color: "#FF1493",
    description: "Bright red blood with white bone fragments or marrow",
    vitalHit: "Leg Bone",
    waitTime: "Wait 8-12 hours - deer often survives",
    trackingDistance: "Highly variable - track carefully",
    confidence: "low" as const
  },
  "spine-blood": {
    label: "Mixed Spine Hit",
    color: "#B8860B",
    description: "Variable blood with possible paralysis - deer may be down",
    vitalHit: "Spine",
    waitTime: "Check immediately - deer may be paralyzed",
    trackingDistance: "Usually found quickly or not at all",
    confidence: "medium" as const
  }
};

const ShotAnalyzer = () => {
  const { user } = useAuth();
  const [selectedBloodColor, setSelectedBloodColor] = useState<string>("");
  const [shotAnalysis, setShotAnalysis] = useState<ShotAnalysis | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  const analyzeBloodColor = (colorKey: string) => {
    const colorData = BLOOD_COLORS[colorKey as keyof typeof BLOOD_COLORS];
    
    if (!colorData) return;
    
    setShotAnalysis({
      vitalHit: colorData.vitalHit,
      bloodColor: colorData.label,
      bloodDescription: colorData.description,
      waitTime: colorData.waitTime,
      trackingDistance: colorData.trackingDistance,
      confidence: colorData.confidence
    });
  };

  const handleBloodColorChange = (value: string) => {
    setSelectedBloodColor(value);
    analyzeBloodColor(value);
  };

  const resetAnalysis = () => {
    setSelectedBloodColor("");
    setShotAnalysis(null);
  };

  const handleSaveShot = async () => {
    if (!user || !shotAnalysis) return;
    
    setIsSaving(true);
    
    try {
      // Save the shot to the user's profile
      // Using type assertion to bypass the type checking since the table exists in the database
      // but not in the TypeScript definitions yet
      const { error } = await (supabase
        .from('user_shots' as any)
        .insert({
          user_id: user.id,
          blood_color: selectedBloodColor,
          shot_analysis: shotAnalysis,
          created_at: new Date().toISOString()
        }) as any);
        
      if (error) throw error;
      
      toast.success("Shot analysis saved to your hunt log");
    } catch (error) {
      console.error("Error saving shot:", error);
      toast.error("Failed to save shot analysis");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Main card with blood color selection */}
      <div className="bg-white rounded-lg shadow-md p-3 sm:p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-base sm:text-lg font-medium">Blood Trail Analysis</h3>
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Blood Color & Consistency
          </label>
          
          <Select value={selectedBloodColor} onValueChange={handleBloodColorChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Choose blood color and consistency..." />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(BLOOD_COLORS).map(([key, data]) => (
                <SelectItem key={key} value={key} className="flex items-center">
                  <div className="flex items-center gap-2">
                    <Droplet className="h-4 w-4" fill={data.color} color="transparent" />
                    <span>{data.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Color Preview */}
        {selectedBloodColor && (
          <div className="mb-4 p-4 bg-gray-50 rounded-md">
            <div className="flex items-center gap-3 mb-2">
              <Droplet 
                size={32} 
                fill={BLOOD_COLORS[selectedBloodColor as keyof typeof BLOOD_COLORS]?.color} 
                color="transparent"
              />
              <div>
                <h4 className="font-medium">
                  {BLOOD_COLORS[selectedBloodColor as keyof typeof BLOOD_COLORS]?.label}
                </h4>
                <p className="text-sm text-gray-600">
                  {BLOOD_COLORS[selectedBloodColor as keyof typeof BLOOD_COLORS]?.description}
                </p>
              </div>
            </div>
          </div>
        )}
        
        <div className="flex justify-between mt-3 gap-2">
          <Button 
            variant="outline" 
            onClick={resetAnalysis}
            className="text-sm h-9 px-3 flex-1"
            disabled={!selectedBloodColor}
          >
            <Delete size={14} className="mr-1" />
            Reset
          </Button>
          
          <Button 
            disabled={!shotAnalysis || isSaving}
            onClick={handleSaveShot}
            className="bg-forest hover:bg-forest-dark text-white text-sm h-9 px-3 flex-1"
          >
            {isSaving ? (
              <>Saving...</>
            ) : (
              <>
                <Save size={14} className="mr-1" />
                Save Analysis
              </>
            )}
          </Button>
        </div>
      </div>
      
      {/* Analysis results - only shown when analysis is complete */}
      {shotAnalysis && (
        <div className="space-y-3">
          <ShotDetails 
            shotPoints={[]}
            shotAnalysis={shotAnalysis}
          />
          
          <TrackingRecommendations 
            shotAnalysis={shotAnalysis}
          />
        </div>
      )}
    </div>
  );
};

export default ShotAnalyzer;
