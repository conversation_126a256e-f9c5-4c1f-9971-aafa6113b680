
import React from "react";
import { Timer, Clock, ThermometerSun, Droplet, Target, AlertCircle, Heart, Eye, Wind, MapPin } from "lucide-react";
import type { ShotAnalysis } from "./ShotAnalyzer";

interface TrackingRecommendationsProps {
  shotAnalysis: ShotAnalysis;
}

const TrackingRecommendations: React.FC<TrackingRecommendationsProps> = ({ 
  shotAnalysis 
}) => {
  // Generate weather considerations based on the type of hit - More specific recommendations
  const getWeatherConsiderations = (): string => {
    if (shotAnalysis.vitalHit === "Paunch/Stomach" || shotAnalysis.vitalHit === "Intestines") {
      return "Heat above 65°F accelerates spoilage - reduce wait time by 2-3 hours. Cold below 40°F allows longer wait times.";
    } else if (shotAnalysis.vitalHit.includes("Muscle") || shotAnalysis.vitalHit === "Leg Bone") {
      return "Rain washes away sparse blood trail quickly. Wind disperses scent. Track immediately before weather changes.";
    } else if (shotAnalysis.bloodColor.toLowerCase().includes("frothy") || shotAnalysis.vitalHit.includes("Lung")) {
      return "Cold weather preserves frothy blood evidence. Look for pink snow or ice. Humid weather may make froth less visible.";
    } else if (shotAnalysis.vitalHit === "Heart" || shotAnalysis.vitalHit === "Major Artery") {
      return "Heavy rain may wash away blood pools. Mark blood immediately. Weather less critical due to short tracking distance.";
    } else {
      return "Monitor weather changes - plan tracking before precipitation. Cold extends safe wait times, heat reduces them.";
    }
  };

  // Generate special tracking tips based on blood color and vital hit - Field-tested advice
  const getSpecialTrackingTips = (): string[] => {
    const tips = [];
    
    if (shotAnalysis.vitalHit === "Heart") {
      tips.push("Look for heavy blood pools every 10-20 yards - heart shots bleed profusely");
      tips.push("Listen for crashing within 30-60 seconds of the shot");
      tips.push("May run in straight line initially, then curve as blood pressure drops");
      tips.push("Recovery rate 95%+ if tracking begins within 2 hours");
    }
    
    if (shotAnalysis.vitalHit === "Major Artery") {
      tips.push("Follow arterial spray patterns - may be 3-6 feet from track on vegetation");
      tips.push("Blood will be rhythmic pumping pattern initially, then steady flow");
      tips.push("Expect to find deer within 75 yards in most cases");
      tips.push("Look for large blood pools where deer paused");
    }
    
    if (shotAnalysis.vitalHit === "Double Lung") {
      tips.push("Pink, frothy blood on both sides of trail indicates pass-through");
      tips.push("Deer typically runs 75-150 yards then beds down quickly");
      tips.push("Look for blood at nose/mouth level on vegetation");
      tips.push("May hear wheezing or labored breathing sounds");
      tips.push("Recovery rate 90%+ with proper wait time");
    }
    
    if (shotAnalysis.vitalHit === "Single Lung") {
      tips.push("Less frothy blood, often one-sided bleeding pattern");
      tips.push("Deer may travel 400-800 yards if pushed - patience critical");
      tips.push("Look for beds with blood pools - deer rests frequently");
      tips.push("Grid search around last blood if trail goes cold");
      tips.push("Recovery rate drops to 40-60% if deer is bumped early");
    }
    
    if (shotAnalysis.vitalHit === "Liver") {
      tips.push("Blood trail often starts sparse, increases as deer travels");
      tips.push("Look for dark blood clots and pools in bedding areas");
      tips.push("Deer may hunch back and move slowly - sign of liver hit");
      tips.push("Often beds within 200 yards if left undisturbed");
      tips.push("Recovery rate 75-85% with 4+ hour wait time");
    }
    
    if (shotAnalysis.vitalHit === "Kidney") {
      tips.push("Look for blood mixed with urine - strong indicator");
      tips.push("Deer often hunches and moves with stiff gait");
      tips.push("Blood trail may be intermittent but deer usually beds");
      tips.push("Check thick cover where deer seeks security");
    }
    
    if (shotAnalysis.vitalHit === "Paunch/Stomach" || shotAnalysis.vitalHit === "Intestines") {
      tips.push("Look for stomach contents, partially digested food, and green matter");
      tips.push("Strong smell at impact site and along trail is key indicator");
      tips.push("Deer may travel in large circle, returning toward origin");
      tips.push("Check water sources - gut-shot deer seek water");
      tips.push("May bed multiple times, creating several blood pools");
      tips.push("Recovery rate 60-75% with overnight wait - patience saves deer");
    }

    if (shotAnalysis.vitalHit.includes("Muscle")) {
      tips.push("Look for hair at impact site to determine exact hit location");
      tips.push("High hits (shoulder) often produce more blood than low hits (brisket)");
      tips.push("Deer may act normal initially then stiffen up");
      tips.push("Grid search if blood trail disappears - deer may bed in thick cover");
      tips.push("Consider using tracking dogs if legal in your area");
      tips.push("Recovery rate highly variable (10-40%) depending on exact placement");
    }

    if (shotAnalysis.vitalHit === "Leg Bone") {
      tips.push("Look for white bone chips or marrow at impact site");
      tips.push("Deer will likely favor the injured leg but may still travel far");
      tips.push("Blood trail may be sporadic but deer usually survives with 3 legs");
      tips.push("Consider discontinuing pursuit if deer appears to be coping well");
    }

    if (shotAnalysis.vitalHit === "Spine") {
      tips.push("Deer may be immediately paralyzed in hindquarters");
      tips.push("Look for deer dragging hindquarters or unable to stand");
      tips.push("If deer is down but alive, approach carefully for follow-up shot");
      tips.push("High spine hits may cause immediate collapse");
    }
    
    if (tips.length === 0) {
      tips.push("Mark last blood with flagging tape");
      tips.push("Move slowly and quietly on the trail");
      tips.push("Use grid search pattern if blood trail is lost");
    }
    
    return tips;
  };

  // Get visual clues based on blood color and hit location - More detailed field signs
  const getVisualClues = (): string[] => {
    const clues = [];
    
    if (shotAnalysis.vitalHit === "Heart" || shotAnalysis.vitalHit === "Major Artery") {
      clues.push("Bright red, oxygen-rich blood in large quantities");
      clues.push("Blood may spray or pump in 2-3 foot patterns");
      clues.push("Large pools of blood where deer paused");
      clues.push("Blood trail typically wide and obvious");
    } else if (shotAnalysis.vitalHit.includes("Lung")) {
      clues.push("Pink to light red blood with visible air bubbles");
      clues.push("Frothy or foamy appearance, especially when fresh");
      clues.push("May find bloody foam on vegetation at chest height");
      clues.push("Blood often on both sides of trail if pass-through");
    } else if (shotAnalysis.vitalHit === "Liver") {
      clues.push("Very dark red to maroon blood, thick consistency");
      clues.push("May appear almost black when dried");
      clues.push("Often smaller drops initially, larger pools later");
      clues.push("Blood may have slight metallic smell");
    } else if (shotAnalysis.vitalHit === "Kidney") {
      clues.push("Dark red blood mixed with clear or yellow urine");
      clues.push("May have strong ammonia-like odor");
      clues.push("Blood and urine mixture creates distinctive scent trail");
    } else if (shotAnalysis.vitalHit === "Paunch/Stomach" || shotAnalysis.vitalHit === "Intestines") {
      clues.push("Very little bright red blood, mostly body fluids");
      clues.push("Green, brown, or yellow stomach contents");
      clues.push("Partially digested food matter (corn, acorns, etc.)");
      clues.push("Strong, distinctive odor of stomach acid and contents");
      clues.push("May see bile (greenish-yellow fluid)");
    } else if (shotAnalysis.vitalHit.includes("Muscle")) {
      clues.push("Bright red muscle blood but in smaller quantities");
      clues.push("Hair at impact site (color indicates hit location)");
      clues.push("Long gaps between blood drops");
      clues.push("Blood may be mixed with fat or tissue");
    } else if (shotAnalysis.vitalHit === "Leg Bone") {
      clues.push("Bright red blood with white bone fragments");
      clues.push("Possible marrow (white/yellowish) mixed with blood");
      clues.push("Hair and possibly white bone chips at impact");
    }
    
    if (clues.length === 0) {
      clues.push("Variable blood color and consistency");
      clues.push("Mark all blood locations with flagging tape");
    }
    
    return clues;
  };

  // Get arrow condition details - More specific field observations
  const getArrowCondition = (): string => {
    if (shotAnalysis.vitalHit === "Heart") {
      return "Deep red blood coating, may have muscle tissue. Usually good penetration.";
    } else if (shotAnalysis.vitalHit === "Major Artery") {
      return "Bright red arterial blood, usually clean pass-through with minimal tissue.";
    } else if (shotAnalysis.vitalHit.includes("Lung")) {
      return "Pink frothy blood, possible lung tissue (spongy gray material).";
    } else if (shotAnalysis.vitalHit === "Liver") {
      return "Very dark blood coating, may have brownish tint. Often sticky consistency.";
    } else if (shotAnalysis.vitalHit === "Paunch/Stomach") {
      return "Strong foul odor, greenish tint, food particles. Wash hands immediately.";
    } else if (shotAnalysis.vitalHit === "Intestines") {
      return "Brown matter, strong smell, possible food content. Clean arrow thoroughly.";
    } else if (shotAnalysis.vitalHit.includes("Muscle")) {
      return "Bright red muscle blood, possible fat or tissue. Usually good penetration.";
    } else if (shotAnalysis.vitalHit === "Leg Bone") {
      return "Blood with possible bone fragments or marrow. Arrow may be damaged.";
    }
    
    return "Examine arrow carefully for blood color, tissue, and odor clues.";
  };

  // Get sound of hit details - Specific audio cues hunters report
  const getSoundOfHit = (): string => {
    if (shotAnalysis.vitalHit === "Heart") {
      return "Solid 'thump' or 'whack' - unmistakable hit sound followed by crashing.";
    } else if (shotAnalysis.vitalHit === "Major Artery") {
      return "Sharp 'pop' or 'crack' sound, often followed by immediate flight response.";
    } else if (shotAnalysis.vitalHit.includes("Lung")) {
      return "Wet 'thud' or hollow sound, may hear wheezing or labored breathing after shot.";
    } else if (shotAnalysis.vitalHit === "Liver") {
      return "Soft 'thump' or muffled hit, deer may hunch and walk slowly away.";
    } else if (shotAnalysis.vitalHit === "Paunch/Stomach") {
      return "Soft slapping sound, less sharp than vital hits. Deer may kick at belly.";
    } else if (shotAnalysis.vitalHit === "Intestines") {
      return "Muffled hit sound, deer often hunches back and moves with tail down.";
    } else if (shotAnalysis.vitalHit.includes("Muscle")) {
      return "Sharp 'crack' for shoulder, softer 'thud' for brisket. Deer may flinch violently.";
    } else if (shotAnalysis.vitalHit === "Leg Bone") {
      return "Loud 'crack' of breaking bone, deer may immediately favor the leg.";
    }
    
    return "Listen carefully to the impact sound - it provides valuable hit location clues.";
  };

  const weatherConsideration = getWeatherConsiderations();
  const specialTips = getSpecialTrackingTips();
  const visualClues = getVisualClues();
  const arrowCondition = getArrowCondition();
  const soundOfHit = getSoundOfHit();

  // Get recovery likelihood percentage - Updated with field-tested statistics
  const getRecoveryLikelihood = (): string => {
    if (shotAnalysis.vitalHit === "Heart") {
      return "95%+ (near certain with proper tracking)";
    } else if (shotAnalysis.vitalHit === "Major Artery") {
      return "90-95% (excellent if tracked within 2 hours)";
    } else if (shotAnalysis.vitalHit === "Double Lung") {
      return "90-95% (excellent with 45+ minute wait)";
    } else if (shotAnalysis.vitalHit === "Single Lung") {
      return "40-60% (depends heavily on not bumping deer)";
    } else if (shotAnalysis.vitalHit === "Liver") {
      return "75-85% (good if left undisturbed 4+ hours)";
    } else if (shotAnalysis.vitalHit === "Kidney") {
      return "65-80% (good with 2-3 hour wait)";
    } else if (shotAnalysis.vitalHit === "Paunch/Stomach") {
      return "60-75% (fair if left overnight)";
    } else if (shotAnalysis.vitalHit === "Intestines") {
      return "50-70% (depends on exact location and wait time)";
    } else if (shotAnalysis.vitalHit === "High Muscle Hit") {
      return "25-40% (poor but possible if undisturbed)";
    } else if (shotAnalysis.vitalHit === "Low Muscle/Brisket") {
      return "10-25% (very poor - deer usually survives)";
    } else if (shotAnalysis.vitalHit === "Leg Bone") {
      return "15-30% (poor but deer may be found if tracked carefully)";
    } else if (shotAnalysis.vitalHit === "Spine") {
      return "Variable (immediate down or complete miss)";
    }
    
    return "Depends on shot placement and tracking execution";
  };

  const recoveryLikelihood = getRecoveryLikelihood();

  return (
    <div className="bg-white rounded-lg shadow-md p-3 sm:p-4">
      <h3 className="text-md font-medium mb-2">Detailed Tracking Analysis</h3>
      
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-forest/10 p-3 rounded">
            <div className="flex items-center mb-1">
              <Clock size={16} className="text-forest mr-1.5" />
              <h4 className="font-medium text-xs text-forest">Critical Wait Time</h4>
            </div>
            <p className="text-sm font-bold text-forest">{shotAnalysis.waitTime}</p>
          </div>
          
          <div className="bg-forest/10 p-3 rounded">
            <div className="flex items-center mb-1">
              <MapPin size={16} className="text-forest mr-1.5" />
              <h4 className="font-medium text-xs text-forest">Expected Distance</h4>
            </div>
            <p className="text-sm font-bold text-forest">{shotAnalysis.trackingDistance}</p>
          </div>
        </div>

        <div className="bg-forest/10 p-3 rounded-md">
          <div className="flex items-center mb-1">
            <Eye size={16} className="text-forest mr-1.5" />
            <h4 className="font-medium text-xs text-forest">Visual Blood Evidence</h4>
          </div>
          <ul className="list-disc list-inside text-gray-700 space-y-1 text-xs">
            {visualClues.map((clue, index) => (
              <li key={index}>{clue}</li>
            ))}
          </ul>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <div className="bg-forest/10 p-3 rounded-md">
            <div className="flex items-center mb-1">
              <Target size={16} className="text-forest mr-1.5" />
              <h4 className="font-medium text-xs text-forest">Arrow Evidence</h4>
            </div>
            <p className="text-xs text-gray-700">{arrowCondition}</p>
          </div>
          
          <div className="bg-forest/10 p-3 rounded-md">
            <div className="flex items-center mb-1">
              <Wind size={16} className="text-forest mr-1.5" />
              <h4 className="font-medium text-xs text-forest">Impact Sound</h4>
            </div>
            <p className="text-xs text-gray-700">{soundOfHit}</p>
          </div>
        </div>
        
        <div className="bg-forest/10 p-3 rounded-md">
          <div className="flex items-center mb-1">
            <ThermometerSun size={16} className="text-forest mr-1.5" />
            <h4 className="font-medium text-xs text-forest">Weather Impact</h4>
          </div>
          <p className="text-xs text-gray-700">{weatherConsideration}</p>
        </div>

        <div className="bg-forest/10 p-3 rounded-md">
          <div className="flex items-center mb-1">
            <Heart size={16} className="text-forest mr-1.5" />
            <h4 className="font-medium text-xs text-forest">Recovery Probability</h4>
          </div>
          <p className="text-sm font-bold text-gray-700">{recoveryLikelihood}</p>
        </div>

        <div className="bg-forest/10 p-3 rounded-md">
          <div className="flex items-center mb-1">
            <Target size={16} className="text-forest mr-1.5" />
            <h4 className="font-medium text-xs text-forest">Specialized Tracking Tips</h4>
          </div>
          <ul className="list-disc list-inside text-gray-700 space-y-1 text-xs">
            {specialTips.map((tip, index) => (
              <li key={index}>{tip}</li>
            ))}
          </ul>
        </div>
        
        <div className="bg-red-50 border border-red-200 p-3 rounded-md">
          <div className="flex items-center mb-1.5">
            <AlertCircle size={16} className="text-red-600 mr-1.5" />
            <h4 className="font-medium text-xs text-red-600">Critical Success Factors</h4>
          </div>
          <ul className="list-disc list-inside text-red-700 space-y-1 text-xs">
            <li>Wait the full recommended time - rushing ruins more recoveries than any other factor</li>
            <li>Mark your shot location and the deer's path with GPS or landmarks</li>
            <li>Move slowly and quietly - assume the deer is still alive and nearby</li>
            <li>Bring flagging tape to mark blood trail and use a good flashlight</li>
            <li>If you lose the trail, make expanding circles from the last blood sign</li>
            <li>Consider bringing help for tracking but keep the group small and quiet</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default TrackingRecommendations;
