
import React from "react";
import { Badge } from "@/components/ui/badge";
import type { ShotPoint, ShotAnalysis } from "./ShotAnalyzer";
import { Droplet, Heart, HeartPulse, Target } from "lucide-react";

interface ShotDetailsProps {
  shotPoints: ShotPoint[];
  shotAnalysis: ShotAnalysis;
}

const ShotDetails: React.FC<ShotDetailsProps> = ({ shotAnalysis }) => {
  // Helper function to get color code based on blood color name
  const getColorCode = (colorName: string): string => {
    switch (colorName.toLowerCase()) {
      case "bright arterial red":
        return "#FF0000";
      case "pinkish frothy":
        return "#FF9999";
      case "deep crimson":
        return "#B22222";
      case "dark maroon":
        return "#800000";
      case "brown/green tinted":
        return "#556B2F";
      case "dark with food matter":
        return "#654321";
      case "light red sparse":
        return "#CD5C5C";
      case "dark venous":
        return "#330000";
      default:
        return "#B22222";
    }
  };

  // Helper function to get the appropriate icon for the vital hit
  const getVitalIcon = (vitalHit: string) => {
    if (vitalHit.toLowerCase().includes("heart")) {
      return <Heart size={16} className="text-red-600" />;
    } else if (vitalHit.toLowerCase().includes("lung") || vitalHit.toLowerCase().includes("artery") || vitalHit.toLowerCase().includes("vein")) {
      return <HeartPulse size={16} className="text-red-500" />;
    } else {
      return <Target size={16} className="text-orange-500" />;
    }
  };

  // Function to get recovery chance info based on vital hit - Updated for accuracy
  const getRecoveryChance = (vitalHit: string): { text: string; class: string } => {
    if (["Major Artery", "Heart"].includes(vitalHit)) {
      return { text: "Very Low", class: "bg-red-100 text-red-800" };
    } else if (vitalHit === "Lung") {
      return { text: "Very Low to Low", class: "bg-red-100 text-red-800" };
    } else if (["Liver", "Major Vein"].includes(vitalHit)) {
      return { text: "Low to Moderate", class: "bg-orange-100 text-orange-800" };
    } else if (["Paunch/Stomach", "Intestines"].includes(vitalHit)) {
      return { text: "Moderate", class: "bg-yellow-100 text-yellow-800" };
    } else {
      return { text: "High", class: "bg-green-100 text-green-800" };
    }
  };

  const recoveryInfo = getRecoveryChance(shotAnalysis.vitalHit);

  return (
    <div className="bg-white rounded-lg shadow-md p-3 sm:p-4">
      <h3 className="text-md font-medium mb-3">Shot Analysis Details</h3>
      
      <div className="grid grid-cols-2 gap-2 sm:gap-3">
        <div className="bg-gray-50 p-2 rounded">
          <span className="text-xs text-gray-500 block">Vital Hit</span>
          <div className="flex items-center gap-1">
            {getVitalIcon(shotAnalysis.vitalHit)}
            <span className="font-medium text-sm">{shotAnalysis.vitalHit}</span>
          </div>
        </div>
        
        <div className="bg-gray-50 p-2 rounded">
          <span className="text-xs text-gray-500 block">Blood Color</span>
          <div className="flex items-center gap-1">
            <Droplet size={14} fill={getColorCode(shotAnalysis.bloodColor)} color="transparent" />
            <span className="font-medium text-sm">{shotAnalysis.bloodColor}</span>
          </div>
        </div>
        
        <div className="col-span-2 bg-gray-50 p-2 rounded">
          <span className="text-xs text-gray-500 block">Blood Characteristics</span>
          <span className="font-medium text-sm">{shotAnalysis.bloodDescription}</span>
        </div>
        
        <div className="col-span-2 mt-2 flex justify-between items-center">
          <span className="text-sm text-gray-600">Find Probability:</span>
          <Badge variant={
            shotAnalysis.confidence === "high" ? "default" : 
            shotAnalysis.confidence === "medium" ? "outline" : "destructive"
          } className={
            shotAnalysis.confidence === "high" ? "bg-forest" : 
            shotAnalysis.confidence === "medium" ? "text-forest border-forest" : ""
          }>
            {shotAnalysis.confidence.toUpperCase()}
          </Badge>
        </div>
      </div>
    </div>
  );
};

export default ShotDetails;
