
import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Camera, Upload, RotateCcw } from "lucide-react";

interface BloodSpot {
  x: number;
  y: number;
  color: 'bright-red' | 'dark-red' | 'pink-frothy';
}

interface BloodPhotoAnalyzerProps {
  onAnalysisComplete: (analysis: any) => void;
}

const BloodPhotoAnalyzer: React.FC<BloodPhotoAnalyzerProps> = ({ onAnalysisComplete }) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [bloodSpots, setBloodSpots] = useState<BloodSpot[]>([]);
  const [selectedColor, setSelectedColor] = useState<BloodSpot['color']>('bright-red');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setSelectedImage(e.target?.result as string);
        setBloodSpots([]);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!selectedImage) return;
    
    const rect = event.currentTarget.getBoundingClientRect();
    const x = (event.clientX - rect.left) / rect.width;
    const y = (event.clientY - rect.top) / rect.height;
    
    setBloodSpots(prev => [...prev, { x, y, color: selectedColor }]);
  };

  const analyzeBloodTrail = () => {
    const brightRed = bloodSpots.filter(spot => spot.color === 'bright-red').length;
    const darkRed = bloodSpots.filter(spot => spot.color === 'dark-red').length;
    const pinkFrothy = bloodSpots.filter(spot => spot.color === 'pink-frothy').length;

    let analysis = {
      hitType: 'Unknown',
      recommendation: 'Continue tracking carefully',
      details: ''
    };

    if (pinkFrothy > 0) {
      analysis = {
        hitType: 'Lung Shot',
        recommendation: 'Wait 30-45 minutes before tracking',
        details: 'Pink, frothy blood indicates lung hit. Deer will likely expire quickly.'
      };
    } else if (brightRed > darkRed) {
      analysis = {
        hitType: 'Muscle/Arterial Hit',
        recommendation: 'Begin tracking immediately',
        details: 'Bright red blood suggests muscle or arterial hit. Track quickly.'
      };
    } else if (darkRed > brightRed) {
      analysis = {
        hitType: 'Liver Hit',
        recommendation: 'Wait 4-6 hours before tracking',
        details: 'Dark red blood indicates liver hit. Be patient and wait before tracking.'
      };
    }

    onAnalysisComplete(analysis);
  };

  const resetAnalysis = () => {
    setSelectedImage(null);
    setBloodSpots([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getSpotSize = (color: BloodSpot['color']) => {
    switch (color) {
      case 'bright-red':
        return 'w-6 h-6'; // Increased from w-4 h-4
      case 'dark-red':
        return 'w-7 h-7'; // Increased from w-4 h-4
      case 'pink-frothy':
        return 'w-8 h-8'; // Increased from w-4 h-4
      default:
        return 'w-6 h-6';
    }
  };

  const getSpotColor = (color: BloodSpot['color']) => {
    switch (color) {
      case 'bright-red':
        return 'bg-red-500';
      case 'dark-red':
        return 'bg-red-900';
      case 'pink-frothy':
        return 'bg-pink-400';
      default:
        return 'bg-red-500';
    }
  };

  return (
    <Card className="p-4 space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Blood Trail Analysis</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Upload a photo and mark different types of blood spots to analyze the shot placement
        </p>
      </div>

      {!selectedImage ? (
        <div className="space-y-4">
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
            <Camera className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-4">
              Upload a photo of the blood trail
            </p>
            <Button onClick={() => fileInputRef.current?.click()}>
              <Upload className="w-4 h-4 mr-2" />
              Choose Photo
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Color Selection */}
          <div className="flex gap-2 justify-center flex-wrap">
            <Button
              size="sm"
              variant={selectedColor === 'bright-red' ? 'default' : 'outline'}
              onClick={() => setSelectedColor('bright-red')}
              className="text-xs"
            >
              <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
              Bright Red
            </Button>
            <Button
              size="sm"
              variant={selectedColor === 'dark-red' ? 'default' : 'outline'}
              onClick={() => setSelectedColor('dark-red')}
              className="text-xs"
            >
              <div className="w-3 h-3 bg-red-900 rounded-full mr-2"></div>
              Dark Red
            </Button>
            <Button
              size="sm"
              variant={selectedColor === 'pink-frothy' ? 'default' : 'outline'}
              onClick={() => setSelectedColor('pink-frothy')}
              className="text-xs"
            >
              <div className="w-3 h-3 bg-pink-400 rounded-full mr-2"></div>
              Pink Frothy
            </Button>
          </div>

          {/* Image with blood spots */}
          <div 
            className="relative aspect-video max-h-[300px] mx-auto border rounded-md overflow-hidden cursor-crosshair"
            onClick={handleImageClick}
          >
            <img 
              src={selectedImage} 
              alt="Blood trail analysis" 
              className="w-full h-full object-cover"
            />
            
            {/* Blood spot markers - now bigger */}
            {bloodSpots.map((spot, index) => (
              <div 
                key={index}
                className={`absolute ${getSpotSize(spot.color)} ${getSpotColor(spot.color)} rounded-full opacity-80 border-2 border-white shadow-lg`}
                style={{
                  left: `${spot.x * 100}%`,
                  top: `${spot.y * 100}%`,
                  transform: 'translate(-50%, -50%)',
                }}
              />
            ))}
          </div>

          {/* Instructions */}
          <p className="text-xs text-muted-foreground text-center">
            Click on blood spots in the image to mark them. Select the blood color first.
          </p>

          {/* Action buttons */}
          <div className="flex gap-2 justify-center">
            <Button onClick={resetAnalysis} variant="outline" size="sm">
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
            <Button 
              onClick={analyzeBloodTrail}
              disabled={bloodSpots.length === 0}
              size="sm"
            >
              Analyze Trail
            </Button>
          </div>

          {bloodSpots.length > 0 && (
            <div className="text-xs text-muted-foreground text-center">
              Spots marked: {bloodSpots.length}
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default BloodPhotoAnalyzer;
