
import { useState, useRef, useEffect } from "react";
import type { ShotPoint, <PERSON>View } from "./ShotAnalyzer";

interface DeerModelProps {
  view: DeerView;
  isDarkMode: boolean;
  shotPoints: ShotPoint[];
  onAddPoint: (point: ShotPoint) => void;
}

const DeerModel: React.FC<DeerModelProps> = ({ 
  view, 
  isDarkMode,
  shotPoints,
  onAddPoint
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectionMode, setSelectionMode] = useState<"entry" | "exit">(
    shotPoints.find(p => p.type === "entry") ? "exit" : "entry"
  );
  
  // Update selection mode when shot points change
  useEffect(() => {
    if (shotPoints.length === 0) {
      setSelectionMode("entry");
    } else if (shotPoints.length === 1 && shotPoints[0].type === "entry") {
      setSelectionMode("exit");
    }
  }, [shotPoints]);

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (shotPoints.length >= 2) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const x = (e.clientX - rect.left) / rect.width;
    const y = (e.clientY - rect.top) / rect.height;
    
    onAddPoint({ x, y, type: selectionMode });
  };

  const getImagePath = () => {
    // In a real app, you'd have separate images for each view and lighting condition
    const baseImage = `/lovable-uploads/320b8d21-5b6f-4ce2-a511-4a570d032d78.png`;
    return baseImage;
  };

  return (
    <div 
      className={`relative aspect-video max-h-[350px] mx-auto border rounded-md overflow-hidden touch-manipulation ${isDarkMode ? 'bg-gray-900' : 'bg-gray-100'}`}
      ref={containerRef}
      onClick={handleClick}
    >
      {/* Background - would be replaced with actual deer images */}
      <div 
        className="absolute inset-0 bg-cover bg-center"
        style={{ 
          backgroundImage: `url(${getImagePath()})`,
          filter: isDarkMode ? 'brightness(0.4)' : 'none',
        }}
      >
        {/* Removed text overlay to clean up UI */}
      </div>
      
      {/* Shot point markers */}
      {shotPoints.map((point, index) => (
        <div 
          key={index}
          className={`absolute w-6 h-6 flex items-center justify-center -ml-3 -mt-3 pointer-events-none
            ${point.type === "entry" ? "text-red-500" : "text-blue-500"}`}
          style={{
            left: `${point.x * 100}%`,
            top: `${point.y * 100}%`,
          }}
        >
          <div className={`w-3 h-3 rounded-full ${point.type === "entry" ? "bg-red-500" : "bg-blue-500"}`} />
          <div className={`absolute w-6 h-6 rounded-full animate-ping opacity-75 ${point.type === "entry" ? "bg-red-500" : "bg-blue-500"}`} />
        </div>
      ))}
      
      {/* Legend - more compact for mobile */}
      <div className="absolute bottom-1 right-1 bg-black/70 text-white text-[10px] p-1.5 rounded flex items-center space-x-3">
        <div className="flex items-center">
          <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
          <span>Entry</span>
        </div>
        <div className="flex items-center">
          <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
          <span>Exit</span>
        </div>
      </div>
    </div>
  );
};

export default DeerModel;
