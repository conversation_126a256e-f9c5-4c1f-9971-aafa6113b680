
import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber";
import { OrbitControls, Html, Environment, useTexture } from "@react-three/drei";
import * as THREE from "three";
import type { ShotPoint, DeerView } from "./ShotAnalyzer";

// Deer image component 
function DeerImage({ 
  view, 
  isDarkMode 
}: { 
  view: DeerView;
  isDarkMode: boolean;
}) {
  const groupRef = useRef<THREE.Group>(null);
  const deerTexture = useTexture("/lovable-uploads/c060fefb-0dd0-479b-97c1-002217611dab.png");
  
  // Rotate model based on view
  useEffect(() => {
    if (!groupRef.current) return;
    
    // Set rotation based on view
    if (view === "broadside") {
      groupRef.current.rotation.y = 0;
    } else if (view === "quartering-away") {
      groupRef.current.rotation.y = Math.PI / 4; // 45 degrees
    } else if (view === "quartering-to") {
      groupRef.current.rotation.y = -Math.PI / 4; // -45 degrees
    } else if (view === "frontal") {
      groupRef.current.rotation.y = Math.PI / 2; // 90 degrees
    } else if (view === "rear") {
      groupRef.current.rotation.y = -Math.PI / 2; // -90 degrees
    }
  }, [view]);

  // Add subtle animation
  useFrame((state) => {
    if (groupRef.current) {
      // Small breathing animation
      groupRef.current.position.y = Math.sin(state.clock.getElapsedTime() * 0.5) * 0.03;
    }
  });

  // Set the aspect ratio to match the image
  const planeWidth = 4; // Increased from 3
  const planeHeight = 2.7; // Increased from 2

  return (
    <group ref={groupRef} position={[0, 0, 0]}>
      <mesh>
        <planeGeometry args={[planeWidth, planeHeight]} />
        <meshBasicMaterial map={deerTexture} transparent={true} />
      </mesh>
    </group>
  );
}

// Shot markers component
function ShotMarkers({ 
  shotPoints 
}: { 
  shotPoints: ShotPoint[] 
}) {
  const { camera } = useThree();

  return (
    <>
      {shotPoints.map((point, index) => {
        // Convert normalized 2D coordinates to 3D world position
        // For a 2D plane with width 4 and height 2.7, centered at origin
        const x = (point.x - 0.5) * 4;  // Scale to match new plane width
        const y = (0.5 - point.y) * 2.7;  // Scale to match new plane height and flip Y (DOM Y is top-down)
        const z = 0.01; // Slightly in front of the deer image

        return (
          <group key={index} position={[x, y, z]}>
            <mesh>
              <sphereGeometry args={[0.08, 16, 16]} />
              <meshStandardMaterial 
                color={point.type === "entry" ? "#FF4040" : "#4080FF"} 
                emissive={point.type === "entry" ? "#FF0000" : "#0040FF"}
                emissiveIntensity={0.5}
              />
            </mesh>
            <Html position={[0, 0.15, 0]} center style={{ color: 'white', fontSize: '10px', textShadow: '0 0 3px black' }}>
              {point.type === "entry" ? "Entry" : "Exit"}
            </Html>
          </group>
        );
      })}
    </>
  );
}

interface DeerModel3DProps {
  view: DeerView;
  isDarkMode: boolean;
  shotPoints: ShotPoint[];
  onAddPoint: (point: ShotPoint) => void;
}

const DeerModel3D: React.FC<DeerModel3DProps> = ({ 
  view, 
  isDarkMode,
  shotPoints,
  onAddPoint
}) => {
  const [selectionMode, setSelectionMode] = useState<"entry" | "exit">(
    shotPoints.find(p => p.type === "entry") ? "exit" : "entry"
  );
  
  // Update selection mode when shot points change
  useEffect(() => {
    if (shotPoints.length === 0) {
      setSelectionMode("entry");
    } else if (shotPoints.length === 1 && shotPoints[0].type === "entry") {
      setSelectionMode("exit");
    }
  }, [shotPoints]);

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (shotPoints.length >= 2) return;
    
    // Calculate normalized coordinates
    const rect = event.currentTarget.getBoundingClientRect();
    const x = (event.clientX - rect.left) / rect.width;
    const y = (event.clientY - rect.top) / rect.height;
    
    onAddPoint({ x, y, type: selectionMode });
  };

  return (
    <div 
      className={`relative aspect-video max-h-[400px] mx-auto border rounded-md overflow-hidden ${isDarkMode ? 'bg-gray-900' : 'bg-gray-100'}`}
      onClick={handleClick}
    >
      <Canvas
        shadows
        camera={{ position: [0, 0, 4], fov: 50 }}
        style={{ width: '100%', height: '100%' }}
      >
        <ambientLight intensity={isDarkMode ? 0.3 : 0.5} />
        <pointLight position={[5, 5, 5]} intensity={isDarkMode ? 0.8 : 1.5} />
        <OrbitControls 
          enableZoom={true}
          enablePan={false}
          maxPolarAngle={Math.PI / 1.5}
          minPolarAngle={Math.PI / 6}
          makeDefault
        />
        <DeerImage view={view} isDarkMode={isDarkMode} />
        <ShotMarkers shotPoints={shotPoints} />
        <Environment preset={isDarkMode ? "night" : "park"} />
      </Canvas>
      
      {/* Legend - more compact for mobile */}
      <div className="absolute bottom-1 right-1 bg-black/70 text-white text-[10px] p-1.5 rounded flex items-center space-x-3 z-10">
        <div className="flex items-center">
          <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
          <span>Entry</span>
        </div>
        <div className="flex items-center">
          <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
          <span>Exit</span>
        </div>
      </div>
      
      {/* Instructions */}
      <div className="absolute top-1 left-1 bg-black/70 text-white text-[10px] p-1.5 rounded z-10">
        Drag to rotate • Scroll to zoom
      </div>
    </div>
  );
};

export default DeerModel3D;
