
import { useState, useRef } from "react";
import { Upload } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback, DEFAULT_AVATAR } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

interface ProfilePhotoUploadProps {
  currentAvatarUrl: string;
  username: string;
  onImageChange: (imageUrl: string, isPreview: boolean) => void;
  onUrlChange: (url: string) => void;
  avatarUrlValue: string;
}

const ProfilePhotoUpload = ({
  currentAvatarUrl,
  username,
  onImageChange,
  onUrlChange,
  avatarUrlValue
}: ProfilePhotoUploadProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Validate file
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File too large. Please upload an image smaller than 5MB.");
      return;
    }
    
    if (!file.type.startsWith('image/')) {
      toast.error("Invalid file type. Please upload an image file.");
      return;
    }
    
    setIsUploading(true);
    
    try {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        onImageChange(result, true);
        // Clear the URL field when using uploaded image
        onUrlChange("");
        toast.success("Profile picture updated!");
        setIsUploading(false);
      };
      reader.onerror = () => {
        toast.error("Failed to read file");
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      toast.error("Failed to process image");
      setIsUploading(false);
    }
  };

  const triggerFileInput = () => {
    if (isUploading) return;
    fileInputRef.current?.click();
  };

  // Determine which avatar URL to display with proper fallback
  const displayAvatarUrl = currentAvatarUrl || avatarUrlValue || DEFAULT_AVATAR;

  return (
    <div className="space-y-4">
      <Label>Profile Picture</Label>
      
      {/* Mobile-optimized layout */}
      <div className="flex flex-col items-center space-y-4 sm:flex-row sm:space-y-0 sm:space-x-6">
        {/* Avatar - centered on mobile, left-aligned on desktop */}
        <div className="flex justify-center">
          <Avatar className="w-24 h-24 sm:w-32 sm:h-32 border-2 border-forest">
            <AvatarImage
              src={displayAvatarUrl}
              alt={username}
              className="object-cover"
            />
            <AvatarFallback className="text-lg sm:text-2xl font-semibold bg-forest/10">
              {username.substring(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
        </div>
        
        {/* Action button */}
        <div className="flex flex-col space-y-2 w-full sm:w-auto sm:flex-1">
          <Button 
            type="button" 
            variant="outline" 
            size="sm"
            onClick={triggerFileInput}
            disabled={isUploading}
            className="flex items-center justify-center w-full sm:w-auto"
          >
            <Upload size={14} className="mr-1.5" />
            {isUploading ? "Uploading..." : "Upload Photo"}
          </Button>
          
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleFileChange}
          />
          
          <p className="text-xs text-muted-foreground text-center sm:text-left">
            Upload a photo to use as your profile picture.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProfilePhotoUpload;
