import { useEffect, useRef, useState } from "react";
import { Canvas as FabricCanvas, FabricImage } from "fabric";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Crop, RotateCw } from "lucide-react";

interface ImageCropperProps {
  imageUrl: string;
  isOpen: boolean;
  onClose: () => void;
  onCropComplete: (croppedImageUrl: string) => void;
}

const ImageCropper = ({ imageUrl, isOpen, onClose, onCropComplete }: ImageCropperProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [fabricCanvas, setFabricCanvas] = useState<FabricCanvas | null>(null);
  const [image, setImage] = useState<FabricImage | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!canvasRef.current || !isOpen) return;

    console.log("Initializing fabric canvas");
    const canvas = new FabricCanvas(canvasRef.current, {
      width: 400,
      height: 400,
      backgroundColor: "#f8f9fa",
    });

    setFabricCanvas(canvas);

    return () => {
      console.log("Disposing fabric canvas");
      canvas.dispose();
    };
  }, [isOpen]);

  useEffect(() => {
    if (!fabricCanvas || !imageUrl) {
      console.log("Missing fabric canvas or image URL:", { fabricCanvas: !!fabricCanvas, imageUrl });
      return;
    }

    setIsLoading(true);
    console.log("Starting image load process for URL:", imageUrl);

    // Clear existing objects
    fabricCanvas.clear();
    fabricCanvas.backgroundColor = "#f8f9fa";
    fabricCanvas.renderAll();

    // Check if it's a data URL (base64) or regular URL
    const isDataURL = imageUrl.startsWith('data:');
    console.log("Image type:", isDataURL ? "data URL" : "regular URL");

    if (isDataURL) {
      // Handle data URLs directly
      const img = new Image();
      img.onload = () => {
        console.log("Data URL image loaded successfully, dimensions:", img.width, img.height);
        
        const fabricImg = new FabricImage(img);
        
        const canvasWidth = fabricCanvas.width!;
        const canvasHeight = fabricCanvas.height!;
        const scale = Math.min(canvasWidth / fabricImg.width!, canvasHeight / fabricImg.height!) * 0.8;
        
        console.log("Calculated scale:", scale);
        
        fabricImg.scale(scale);
        fabricImg.set({
          left: (canvasWidth - fabricImg.getScaledWidth()) / 2,
          top: (canvasHeight - fabricImg.getScaledHeight()) / 2,
          selectable: true,
          moveCursor: 'move',
          hoverCursor: 'move'
        });
        
        fabricCanvas.add(fabricImg);
        fabricCanvas.setActiveObject(fabricImg);
        fabricCanvas.renderAll();
        setImage(fabricImg);
        setIsLoading(false);
        console.log("Image successfully added to canvas");
      };
      
      img.onerror = (error) => {
        console.error("Failed to load data URL image:", error);
        setIsLoading(false);
      };
      
      img.src = imageUrl;
    } else {
      // Handle regular URLs with Fabric.js
      console.log("Attempting to load with FabricImage.fromURL");
      
      FabricImage.fromURL(imageUrl, {
        crossOrigin: 'anonymous'
      }).then((img) => {
        console.log("FabricImage.fromURL succeeded, image dimensions:", img.width, img.height);
        
        const canvasWidth = fabricCanvas.width!;
        const canvasHeight = fabricCanvas.height!;
        const scale = Math.min(canvasWidth / img.width!, canvasHeight / img.height!) * 0.8;
        
        console.log("Calculated scale:", scale);
        
        img.scale(scale);
        img.set({
          left: (canvasWidth - img.getScaledWidth()) / 2,
          top: (canvasHeight - img.getScaledHeight()) / 2,
          selectable: true,
          moveCursor: 'move',
          hoverCursor: 'move'
        });
        
        fabricCanvas.add(img);
        fabricCanvas.setActiveObject(img);
        fabricCanvas.renderAll();
        setImage(img);
        setIsLoading(false);
        console.log("Image successfully added to canvas via FabricImage.fromURL");
      }).catch((error) => {
        console.error("FabricImage.fromURL failed:", error);
        
        // Fallback to native Image element
        console.log("Trying fallback with native Image element");
        const imgElement = new Image();
        imgElement.crossOrigin = 'anonymous';
        
        imgElement.onload = () => {
          console.log("Fallback image loaded successfully, dimensions:", imgElement.width, imgElement.height);
          
          const fabricImg = new FabricImage(imgElement);
          
          const canvasWidth = fabricCanvas.width!;
          const canvasHeight = fabricCanvas.height!;
          const scale = Math.min(canvasWidth / fabricImg.width!, canvasHeight / fabricImg.height!) * 0.8;
          
          console.log("Fallback calculated scale:", scale);
          
          fabricImg.scale(scale);
          fabricImg.set({
            left: (canvasWidth - fabricImg.getScaledWidth()) / 2,
            top: (canvasHeight - fabricImg.getScaledHeight()) / 2,
            selectable: true,
            moveCursor: 'move',
            hoverCursor: 'move'
          });
          
          fabricCanvas.add(fabricImg);
          fabricCanvas.setActiveObject(fabricImg);
          fabricCanvas.renderAll();
          setImage(fabricImg);
          setIsLoading(false);
          console.log("Fallback image successfully added to canvas");
        };
        
        imgElement.onerror = (fallbackError) => {
          console.error("Fallback image loading also failed:", fallbackError);
          setIsLoading(false);
        };
        
        imgElement.src = imageUrl;
      });
    }
  }, [fabricCanvas, imageUrl]);

  const handleCrop = () => {
    if (!fabricCanvas || !image) return;

    // Create a circular crop area
    const cropSize = 300;
    const centerX = fabricCanvas.width! / 2;
    const centerY = fabricCanvas.height! / 2;

    // Export the cropped area
    const croppedCanvas = document.createElement('canvas');
    croppedCanvas.width = cropSize;
    croppedCanvas.height = cropSize;
    const ctx = croppedCanvas.getContext('2d')!;

    // Create circular clipping path
    ctx.beginPath();
    ctx.arc(cropSize / 2, cropSize / 2, cropSize / 2, 0, 2 * Math.PI);
    ctx.clip();

    // Draw the fabric canvas content to the cropped canvas
    const fabricCanvasElement = fabricCanvas.getElement();
    ctx.drawImage(
      fabricCanvasElement,
      centerX - cropSize / 2,
      centerY - cropSize / 2,
      cropSize,
      cropSize,
      0,
      0,
      cropSize,
      cropSize
    );

    const croppedImageUrl = croppedCanvas.toDataURL('image/jpeg', 0.9);
    onCropComplete(croppedImageUrl);
    onClose();
  };

  const handleRotate = () => {
    if (!image) return;
    image.rotate((image.angle || 0) + 90);
    fabricCanvas?.renderAll();
  };

  const handleReset = () => {
    if (!fabricCanvas || !imageUrl) return;
    
    setIsLoading(true);
    fabricCanvas.clear();
    fabricCanvas.backgroundColor = "#f8f9fa";
    fabricCanvas.renderAll();
    
    // Use the same loading logic as in the main useEffect
    const isDataURL = imageUrl.startsWith('data:');
    
    if (isDataURL) {
      const img = new Image();
      img.onload = () => {
        const fabricImg = new FabricImage(img);
        
        const canvasWidth = fabricCanvas.width!;
        const canvasHeight = fabricCanvas.height!;
        const scale = Math.min(canvasWidth / fabricImg.width!, canvasHeight / fabricImg.height!) * 0.8;
        
        fabricImg.scale(scale);
        fabricImg.set({
          left: (canvasWidth - fabricImg.getScaledWidth()) / 2,
          top: (canvasHeight - fabricImg.getScaledHeight()) / 2,
          selectable: true,
          moveCursor: 'move',
          hoverCursor: 'move'
        });
        
        fabricCanvas.add(fabricImg);
        fabricCanvas.setActiveObject(fabricImg);
        fabricCanvas.renderAll();
        setImage(fabricImg);
        setIsLoading(false);
      };
      img.onerror = () => setIsLoading(false);
      img.src = imageUrl;
    } else {
      FabricImage.fromURL(imageUrl, {
        crossOrigin: 'anonymous'
      }).then((img) => {
        const canvasWidth = fabricCanvas.width!;
        const canvasHeight = fabricCanvas.height!;
        const scale = Math.min(canvasWidth / img.width!, canvasHeight / img.height!) * 0.8;
        
        img.scale(scale);
        img.set({
          left: (canvasWidth - img.getScaledWidth()) / 2,
          top: (canvasHeight - img.getScaledHeight()) / 2,
          selectable: true,
          moveCursor: 'move',
          hoverCursor: 'move'
        });
        
        fabricCanvas.add(img);
        fabricCanvas.setActiveObject(img);
        fabricCanvas.renderAll();
        setImage(img);
        setIsLoading(false);
      }).catch((error) => {
        console.error("Reset failed:", error);
        setIsLoading(false);
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]" aria-describedby="crop-dialog-description">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Crop size={20} />
            Crop Profile Picture
          </DialogTitle>
          <DialogDescription id="crop-dialog-description">
            Adjust your profile picture by dragging, rotating, or resizing it. The dashed circle shows the crop area.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="relative">
            <canvas
              ref={canvasRef}
              className="border border-gray-200 rounded-lg mx-auto"
            />
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-white/80 rounded-lg">
                <div className="text-sm text-gray-600">Loading image...</div>
              </div>
            )}
            <div className="absolute inset-0 pointer-events-none">
              <div className="w-full h-full flex items-center justify-center">
                <div className="w-[300px] h-[300px] border-2 border-forest border-dashed rounded-full opacity-50"></div>
              </div>
            </div>
          </div>
          
          <div className="flex gap-2 justify-center">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleRotate}
              disabled={!image || isLoading}
              className="flex items-center gap-1"
            >
              <RotateCw size={14} />
              Rotate
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={!image || isLoading}
            >
              Reset
            </Button>
          </div>
          
          <p className="text-xs text-muted-foreground text-center">
            Drag and resize your image. The dashed circle shows the crop area.
          </p>
        </div>
        
        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            type="button" 
            onClick={handleCrop} 
            disabled={!image || isLoading}
            className="bg-forest hover:bg-forest-dark"
          >
            Apply Crop
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ImageCropper;
