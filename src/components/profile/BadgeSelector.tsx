
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

interface BadgeSelectorProps {
  selectedBadges: string[];
  onBadgeToggle: (badge: string) => void;
}

const PRESET_BADGES = [
  "Buck Fever Survivor",
  "Backstrap Dreamer", 
  "Hang & Bang-ish",
  "Wrong Stand Specialist",
  "Tag Donation Club",
  "Bush Latte Archer",
  "Wrong Pin Warrior"
];

const BadgeSelector = ({ selectedBadges, onBadgeToggle }: BadgeSelectorProps) => {
  const handleBadgeToggle = (badge: string) => {
    if (selectedBadges.includes(badge)) {
      onBadgeToggle(badge);
    } else if (selectedBadges.length < 2) {
      onBadgeToggle(badge);
    } else {
      toast.error("You can only select up to 2 preset badges");
    }
  };

  return (
    <div className="space-y-3">
      <Label>Hunting Badges (select up to 2)</Label>
      <div className="grid grid-cols-1 gap-3">
        {PRESET_BADGES.map(badge => (
          <div key={badge} className="flex items-center space-x-2">
            <Checkbox 
              id={badge}
              checked={selectedBadges.includes(badge)}
              onCheckedChange={() => handleBadgeToggle(badge)}
              disabled={!selectedBadges.includes(badge) && selectedBadges.length >= 2}
            />
            <Label 
              htmlFor={badge} 
              className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${
                !selectedBadges.includes(badge) && selectedBadges.length >= 2 ? 'text-muted-foreground' : ''
              }`}
            >
              {badge}
            </Label>
          </div>
        ))}
      </div>
      <p className="text-xs text-muted-foreground">
        Selected: {selectedBadges.length}/2
      </p>
    </div>
  );
};

export default BadgeSelector;
