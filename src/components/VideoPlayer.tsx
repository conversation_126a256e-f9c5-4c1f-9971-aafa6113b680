import { useRef, useState, useEffect, useCallback } from "react";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { useScreenRatio } from "@/lib/utils";
import { Play, Pause, Volume2, VolumeX } from "lucide-react";

interface VideoPlayerProps {
  url: string;
  onClick?: () => void;
  muted?: boolean;
  autoPlay?: boolean;
  audioUrl?: string | null;
  isCreatePagePreview?: boolean;
  preload?: "auto" | "metadata" | "none";
  priority?: boolean;
  lazyLoad?: boolean;
  poster?: string;
  enableAudioControl?: boolean; 
  controls?: boolean;
  onError?: () => void;
  forceAutoPlay?: boolean;
  colorFilter?: {
    id: string;
    name: string;
    cssFilter: string;
  } | null;
}

const VideoPlayer = ({ 
  url, 
  onClick, 
  muted = false, 
  autoPlay = true, 
  audioUrl, 
  isCreatePagePreview = false,
  preload = "auto", 
  priority = false,
  lazyLoad = true,
  poster,
  enableAudioControl = false,
  controls = false,
  onError,
  forceAutoPlay = false,
  colorFilter = null
}: VideoPlayerProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [isMuted, setIsMuted] = useState(muted);
  const [loadError, setLoadError] = useState(false);
  const [audioLoaded, setAudioLoaded] = useState(false);
  const [isBuffering, setIsBuffering] = useState(false);
  const audioSyncIntervalRef = useRef<number | null>(null);
  const attemptsRef = useRef<number>(0);
  const maxAttempts = 3;

  // Detect if we're dealing with audio only
  const isAudioOnly = !url || 
                      url?.includes("elevenlabs") || 
                      url?.includes("audio") || 
                      url?.endsWith(".mp3") ||
                      url?.endsWith(".wav") ||
                      url?.endsWith(".ogg");
  
  const effectiveAudioOnly = isAudioOnly && !isCreatePagePreview;
  const shouldMuteVideo = Boolean(audioUrl && audioLoaded);

  // Optimized intersection observer
  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries;
    setIsVisible(entry.isIntersecting);
  }, []);
  
  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '100px 0px',
      threshold: 0.1
    };
    
    const observer = new IntersectionObserver(handleIntersection, options);

    if (videoRef.current) {
      observer.observe(videoRef.current);
    }

    return () => {
      if (videoRef.current) {
        observer.unobserve(videoRef.current);
      }
    };
  }, [handleIntersection]);

  // Reset state when URL changes
  useEffect(() => {
    if (url) {
      setHasLoaded(false);
      setLoadError(false);
      setIsPlaying(false);
      setAudioLoaded(false);
      setIsBuffering(false);
      attemptsRef.current = 0;
      
      if (url.startsWith('blob:')) {
        setHasLoaded(true);
      }
    }
  }, [url, audioUrl]);

  // Enhanced media playback logic with force auto-play support
  const playMedia = useCallback(async () => {
    if (!hasLoaded || attemptsRef.current >= maxAttempts) return;
    
    // If forceAutoPlay is true, ignore visibility check
    if (!forceAutoPlay && !isVisible) return;
    
    try {
      // Play video if available and not audio-only
      if (videoRef.current && !effectiveAudioOnly) {
        videoRef.current.muted = shouldMuteVideo || isMuted;
        if (!shouldMuteVideo) {
          videoRef.current.volume = 0.8;
        }
        
        await videoRef.current.play();
        setIsPlaying(true);
        console.log('Video started playing');
        
        // Play audio if available
        if (audioRef.current && audioUrl && audioLoaded) {
          audioRef.current.currentTime = videoRef.current.currentTime;
          audioRef.current.volume = 1.0;
          audioRef.current.muted = isMuted;
          
          try {
            await audioRef.current.play();
          } catch (audioError) {
            console.log("Audio autoplay blocked, trying muted:", audioError);
            if (attemptsRef.current < maxAttempts) {
              audioRef.current.muted = true;
              await audioRef.current.play();
              
              if (!isMuted) {
                setTimeout(() => {
                  if (audioRef.current) {
                    audioRef.current.muted = false;
                  }
                }, 500);
              }
            }
          }
          
          startAudioSync();
        }
      } else if (effectiveAudioOnly && audioRef.current && audioUrl && audioLoaded) {
        // Audio-only mode
        audioRef.current.volume = 1.0;
        audioRef.current.muted = isMuted;
        await audioRef.current.play();
        setIsPlaying(true);
      }
      
    } catch (error) {
      console.log("Video autoplay blocked, trying muted:", error);
      
      if (attemptsRef.current < maxAttempts) {
        attemptsRef.current++;
        
        try {
          if (videoRef.current && !effectiveAudioOnly) {
            videoRef.current.muted = true;
            await videoRef.current.play();
            setIsPlaying(true);
            
            if (!isMuted && !shouldMuteVideo) {
              setTimeout(() => {
                if (videoRef.current) {
                  videoRef.current.muted = false;
                  videoRef.current.volume = 0.8;
                }
              }, 500);
            }
          }
        } catch (retryError) {
          console.error("Retry failed:", retryError);
        }
      }
    }
  }, [isVisible, hasLoaded, url, audioUrl, effectiveAudioOnly, isMuted, audioLoaded, shouldMuteVideo, forceAutoPlay]);

  // Audio sync function
  const startAudioSync = useCallback(() => {
    if (audioSyncIntervalRef.current) {
      window.clearInterval(audioSyncIntervalRef.current);
    }
    
    audioSyncIntervalRef.current = window.setInterval(() => {
      if (!videoRef.current || !audioRef.current || !isPlaying) {
        if (audioSyncIntervalRef.current) {
          window.clearInterval(audioSyncIntervalRef.current);
          audioSyncIntervalRef.current = null;
        }
        return;
      }
      
      const timeDifference = Math.abs(videoRef.current.currentTime - audioRef.current.currentTime);
      if (timeDifference > 0.1) {
        audioRef.current.currentTime = videoRef.current.currentTime;
      }
    }, 500);
  }, [isPlaying]);

  // Pause media when not visible
  const pauseMedia = useCallback(() => {
    if (audioSyncIntervalRef.current) {
      window.clearInterval(audioSyncIntervalRef.current);
      audioSyncIntervalRef.current = null;
    }
    
    if (videoRef.current) {
      videoRef.current.pause();
    }
    if (audioRef.current) {
      audioRef.current.pause();
    }
    setIsPlaying(false);
  }, []);

  // Control playback based on visibility and force auto-play
  useEffect(() => {
    if ((isVisible || forceAutoPlay) && hasLoaded && autoPlay) {
      console.log('Attempting to play video - visible:', isVisible, 'forceAutoPlay:', forceAutoPlay);
      playMedia();
    } else if (!isVisible && !forceAutoPlay && isPlaying) {
      pauseMedia();
    }
  }, [isVisible, hasLoaded, autoPlay, playMedia, pauseMedia, isPlaying, forceAutoPlay]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (audioSyncIntervalRef.current) {
        window.clearInterval(audioSyncIntervalRef.current);
      }
    };
  }, []);

  // Video loaded handler
  const handleVideoLoaded = () => {
    setHasLoaded(true);
    setLoadError(false);
    
    if (videoRef.current) {
      videoRef.current.muted = shouldMuteVideo || isMuted;
      if (!shouldMuteVideo) {
        videoRef.current.volume = 0.8;
      }
    }
  };

  // Error handling
  const handleLoadError = (type: 'video' | 'audio') => () => {
    console.error(`Error loading ${type}:`, type === 'video' ? url : audioUrl);
    
    if (type === 'video') {
      setLoadError(true);
      setHasLoaded(false);
      
      if (onError) {
        onError();
      }
    } else {
      setAudioLoaded(false);
    }
  };

  // Fixed toggle playback function
  const handleTogglePlayback = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    if (onClick) {
      onClick();
      return;
    }
    
    if (isPlaying) {
      pauseMedia();
    } else {
      attemptsRef.current = 0;
      playMedia();
    }
  };

  // Toggle mute
  const handleToggleMute = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsMuted(!isMuted);
  };

  // Audio loaded handler
  const handleAudioLoaded = () => {
    setAudioLoaded(true);
  };

  // Video event handlers
  const handleWaiting = () => setIsBuffering(true);
  const handlePlaying = () => setIsBuffering(false);

  const ratio = useScreenRatio();

  return (
    <div onClick={handleTogglePlayback} className="w-full h-full cursor-pointer relative">
      <AspectRatio ratio={parseFloat(ratio)} className="h-full w-full">
        {effectiveAudioOnly ? (
          <div className="absolute inset-0 w-full h-full bg-gradient-to-b from-forest/30 to-forest flex items-center justify-center">
            <div className="text-white text-center p-8">
              <div className="text-4xl mb-4">🎵</div>
              <div className="text-lg font-medium">Audio Track</div>
              <div className="text-sm opacity-80 mt-2">
                {isPlaying ? "Playing..." : "Tap to play"}
              </div>
            </div>
          </div>
        ) : (
          <>
            {isBuffering && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/40 z-10">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
              </div>
            )}
            
            <video
              ref={videoRef}
              src={lazyLoad && !isVisible && !priority && !forceAutoPlay ? undefined : url}
              className="absolute inset-0 w-full h-full object-cover"
              style={{ filter: colorFilter?.cssFilter || "none" }}
              loop
              muted={shouldMuteVideo || isMuted}
              playsInline
              preload={priority || forceAutoPlay ? "auto" : (isVisible ? "metadata" : preload)}
              poster={poster}
              // Additional mobile-specific attributes
              controls={false}
              disablePictureInPicture
              disableRemotePlayback
              onLoadedData={handleVideoLoaded}
              onError={handleLoadError('video')}
              onWaiting={handleWaiting}
              onPlaying={handlePlaying}
              // Add webkit-playsinline for better iOS compatibility
              {...({ 'webkit-playsinline': 'true' } as any)}
            />
            
            {loadError && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/70 z-10">
                <div className="text-white text-center p-4">
                  <p className="mb-2">Error loading video</p>
                  <button 
                    className="px-4 py-2 bg-forest rounded-md hover:bg-forest-light transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      setLoadError(false);
                      attemptsRef.current = 0;
                      if (videoRef.current) {
                        videoRef.current.load();
                      }
                    }}
                  >
                    Retry
                  </button>
                </div>
              </div>
            )}
            
            {!loadError && controls && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/10 opacity-0 hover:opacity-100 transition-opacity">
                <div className="text-white text-4xl">
                  {isPlaying ? <Pause size={48} /> : <Play size={48} />}
                </div>
              </div>
            )}
            
            {enableAudioControl && (
              <div 
                className="absolute bottom-20 right-4 text-white p-2 rounded-full bg-black/30 z-20"
                onClick={handleToggleMute}
              >
                {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
              </div>
            )}
          </>
        )}
      </AspectRatio>
      
      {audioUrl && (
        <audio
          ref={audioRef}
          src={isVisible || priority || forceAutoPlay ? audioUrl : undefined}
          className="hidden"
          muted={isMuted}
          preload={priority || forceAutoPlay ? "auto" : (isVisible ? "auto" : "metadata")}
          onLoadedData={handleAudioLoaded}
          onError={handleLoadError('audio')}
        />
      )}
    </div>
  );
};

export default VideoPlayer;
