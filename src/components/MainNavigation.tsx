import { Home, Target, User, Video, Shield } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";

interface MainNavigationProps {
  onHomeClick?: () => void;
}

// Custom Deer Icon Component
const DeerIcon = ({ size = 24, className = "", style }: { size?: number; className?: string; style?: React.CSSProperties }) => (
  <img 
    src="/lovable-uploads/f9cfa8b5-3287-45cf-b072-b8069c48e372.png" 
    alt="Deer" 
    width={size} 
    height={size} 
    className={`${className} object-contain`}
    style={{
      filter: 'brightness(0) saturate(100%)',
      ...style
    }}
  />
);

const MainNavigation = ({ onHomeClick }: MainNavigationProps) => {
  const location = useLocation();
  const { user, profile } = useAuth();
  const [activeTab, setActiveTab] = useState('/');
  const [positionIndicator, setPositionIndicator] = useState({ left: '10%', width: '10%' });
  const [isAdmin, setIsAdmin] = useState(false);
  
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  useEffect(() => {
    // Check if user has admin badge
    if (profile?.badges && Array.isArray(profile.badges)) {
      setIsAdmin(profile.badges.includes("admin"));
    }
  }, [profile]);

  useEffect(() => {
    // Update active tab based on location
    setActiveTab(location.pathname);
    
    // Update position indicator - now without messages tab
    switch(location.pathname) {
      case '/':
        setPositionIndicator({ left: '10%', width: '10%' });
        break;
      case '/services':
        setPositionIndicator({ left: '30%', width: '10%' });
        break;
      case '/create':
        setPositionIndicator({ left: '50%', width: '10%' });
        break;
      case '/rut-tracker':
        setPositionIndicator({ left: '70%', width: '10%' });
        break;
      case '/profile':
        setPositionIndicator({ left: '90%', width: '10%' });
        break;
      default:
        setPositionIndicator({ left: '10%', width: '10%' });
    }
  }, [location.pathname]);

  const handleHomeClick = (e: React.MouseEvent) => {
    // Only refresh if we're already on the home page
    if (location.pathname === '/') {
      e.preventDefault();
      if (onHomeClick) {
        onHomeClick();
      }
    }
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-background/90 via-background to-background/90 backdrop-blur-lg border-t border-border/30 flex justify-between items-center px-4 z-50 shadow-[0_-4px_10px_rgba(0,0,0,0.03)] pb-safe">
      {/* Animated indicator for active tab */}
      <div 
        className="absolute top-0 h-1 bg-gradient-to-r from-forest to-forest-light rounded-b-md transition-all duration-300 ease-in-out"
        style={{ left: positionIndicator.left, width: positionIndicator.width, transform: 'translateX(-50%)' }}
      ></div>
      
      <div className="flex items-center justify-center w-1/5">
        {/* Home */}
        <Link to="/" className="nav-item group" onClick={handleHomeClick}>
          <div className="relative">
            <Home 
              size={20} 
              className={`transform transition-all duration-300 group-hover:scale-110 ${
                isActive('/') 
                ? 'text-forest fill-forest/10' 
                : 'text-muted-foreground group-hover:text-forest/80'
              }`} 
            />
            {isActive('/') && (
              <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-5 h-1 bg-forest-light/20 rounded-full blur-sm"></span>
            )}
          </div>
          <span className={`nav-text transition-all duration-300 ${
            isActive('/') 
            ? 'text-forest font-semibold scale-105' 
            : 'text-muted-foreground group-hover:text-forest/80'
          }`}>Home</span>
        </Link>
      </div>
      
      <div className="flex items-center justify-center w-1/5">
        {/* Services */}
        <Link to="/services" className="nav-item group">
          <div className="relative">
            <Target 
              size={20} 
              className={`transform transition-all duration-300 group-hover:scale-110 ${
                isActive('/services') 
                ? 'text-forest fill-forest/10' 
                : 'text-muted-foreground group-hover:text-forest/80'
              }`} 
            />
            {isActive('/services') && (
              <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-5 h-1 bg-forest-light/20 rounded-full blur-sm"></span>
            )}
          </div>
          <span className={`nav-text transition-all duration-300 ${
            isActive('/services') 
            ? 'text-forest font-semibold scale-105' 
            : 'text-muted-foreground group-hover:text-forest/80'
          }`}>Services</span>
        </Link>
      </div>
      
      <div className="flex items-center justify-center w-1/5">
        {/* Create - Now in the center position with bigger icon */}
        <Link to="/create" className="nav-item group">
          <div className="bg-gradient-to-br from-forest-light to-forest rounded-full p-2 -mt-5 relative shadow-md shadow-forest/20 hover:shadow-forest/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1">
            <Video size={26} className="text-white" />
          </div>
        </Link>
      </div>
      
      <div className="flex items-center justify-center w-1/5">
        {/* Deer Activity with Custom Deer Icon */}
        <Link to="/rut-tracker" className="nav-item group">
          <div className="relative">
            <DeerIcon 
              size={24} 
              className={`transform transition-all duration-300 group-hover:scale-110`}
              style={{
                filter: isActive('/rut-tracker') 
                  ? 'brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)' 
                  : 'brightness(0) saturate(100%)',
                opacity: isActive('/rut-tracker') ? 1 : 0.6
              }}
            />
            {isActive('/rut-tracker') && (
              <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-5 h-1 bg-forest-light/20 rounded-full blur-sm"></span>
            )}
          </div>
          <span className={`nav-text transition-all duration-300 ${
            isActive('/rut-tracker') 
            ? 'text-forest font-semibold scale-105' 
            : 'text-muted-foreground group-hover:text-forest/80'
          }`}>Rut Report</span>
        </Link>
      </div>
      
      <div className="flex items-center justify-center w-1/5">
        {/* Profile with Admin Link */}
        <div className="relative group">
          <Link to="/profile" className="nav-item">
            <div className="relative">
              <User 
                size={20} 
                className={`transform transition-all duration-300 group-hover:scale-110 ${
                  isActive('/profile') 
                  ? 'text-forest fill-forest/10' 
                  : 'text-muted-foreground group-hover:text-forest/80'
                }`} 
              />
              {isActive('/profile') && (
                <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-5 h-1 bg-forest-light/20 rounded-full blur-sm"></span>
              )}
            </div>
            <span className={`nav-text transition-all duration-300 ${
              isActive('/profile') 
              ? 'text-forest font-semibold scale-105' 
              : 'text-muted-foreground group-hover:text-forest/80'
            }`}>Profile</span>
          </Link>
          
          {/* Admin Panel Link (only visible for admins) */}
          {isAdmin && (
            <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 w-28 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <Link 
                to="/admin" 
                className="flex flex-col items-center p-2 bg-background rounded-md border border-border shadow-md"
              >
                <Shield size={16} className="text-red-500" />
                <span className="text-xs mt-1 font-medium">Admin Panel</span>
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MainNavigation;
