import { useState } from "react";
import { Briefcase, Plus, Trash2, MapP<PERSON>, Edit, Crown } from "lucide-react";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  <PERSON><PERSON>T<PERSON>le, 
  <PERSON><PERSON>Footer,
  DialogTrigger 
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { ServiceItem } from "./ProfileHeader";
import { Separator } from "@/components/ui/separator";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ServicesDialogProps {
  services: ServiceItem[];
  onSave: (services: ServiceItem[]) => void;
}

// ServiceItem interface (removed video_url feature)
interface ExtendedServiceItem extends ServiceItem {}

const SERVICE_TYPES = [
  "Bow Tech",
  "Deer Processing",
  "Deer Tracking Dogs",
  "Drone Recovery",
  "Euro Mounts",
  "Food Plot Work",
  "Guided Hunts",
  "Influencer Collaboration",
  "Land Management",
  "Official Scorer",
  "Real Estate Agent",
  "Social Media Marketing",
  "Taxidermy",
  "Trail Cam Setup",
  "Videographer"
];

const CONTACT_METHODS = [
  "Email",
  "Phone",
  "Website",
  "Direct Message"
];

const ServicesDialog = ({ services = [], onSave }: ServicesDialogProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [userServices, setUserServices] = useState<ExtendedServiceItem[]>(services);
  const [currentService, setCurrentService] = useState<ExtendedServiceItem>({
    type: "",
    description: "",
    contactMethod: "",
    contactValue: "",
    location: {
      city: "",
      zipCode: "",
      coordinates: null
    }
  });
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const { user, profile, refreshProfile } = useAuth();
  
  // Check if user has promotion subscription
  const isUserPromoted = profile?.is_promoted && 
    profile?.promoted_until && 
    new Date(profile.promoted_until) > new Date();
  
  const resetCurrentService = () => {
    setCurrentService({
      type: "",
      description: "",
      contactMethod: "",
      contactValue: "",
      location: {
        city: "",
        zipCode: "",
        coordinates: null
      }
    });
    setEditingIndex(null);
  };

  const handleEditService = (index: number) => {
    const service = userServices[index];
    setCurrentService({ ...service });
    setEditingIndex(index);
  };

  // Services require promotion subscription
  const handleAddService = () => {
    if (!isUserPromoted) {
      toast.error("You need a promotion subscription to add services. Please subscribe to promote your services.");
      return;
    }
    // Allow adding service
    handleAddOrUpdateService();
  };

  const handleVerifySubscription = async () => {
    try {
      toast.info("Verifying your subscription...");
      
      const { data, error } = await supabase.functions.invoke('verify-subscription');
      
      if (error) {
        console.error('Subscription verification error:', error);
        toast.error("Failed to verify subscription. Please try again.");
        return;
      }

      if (data.is_promoted) {
        toast.success("Subscription verified! You can now add services.");
        // Refresh the profile to get updated promotion status
        await refreshProfile();
      } else {
        toast.error("No active subscription found. Please complete your payment first.");
      }
    } catch (error) {
      console.error('Error verifying subscription:', error);
      toast.error("Failed to verify subscription. Please try again.");
    }
  };

  const handleAddOrUpdateService = () => {
    // Validate service
    if (!currentService.type) {
      toast.error("Please select a service type");
      return;
    }

    if (!currentService.description) {
      toast.error("Please add a description for your service");
      return;
    }

    if (currentService.description.length > 150) {
      toast.error("Description must be 150 characters or less");
      return;
    }

    const updatedServices = [...userServices];
    
    if (editingIndex !== null) {
      // Update existing service
      updatedServices[editingIndex] = { ...currentService };
      toast.success("Service updated successfully");
    } else {
      // Add new service
      updatedServices.push({ ...currentService });
    }
    
    setUserServices(updatedServices);
    resetCurrentService();
  };

  const handleRemoveService = async (index: number) => {
    if (!user) {
      toast.error("You must be logged in to delete services");
      return;
    }

    try {
      const updatedServices = [...userServices];
      updatedServices.splice(index, 1);
      
      // Convert ServiceItems to a plain object array for storage in JSON field
      const servicesForStorage = updatedServices.map(service => ({
        type: service.type,
        description: service.description,
        contactMethod: service.contactMethod || "",
        contactValue: service.contactValue || "",
        location: service.location || null
      }));
      
      // Immediately update the database
      const { error } = await supabase
        .from('profiles')
        .update({
          services: servicesForStorage
        })
        .eq('id', user.id);
        
      if (error) {
        console.error("Supabase error deleting service:", error);
        throw error;
      }
      
      // Update local state
      setUserServices(updatedServices);
      
      // Update parent component
      onSave(updatedServices);
      
      toast.success("Service deleted successfully");
      
      // If we're editing the service being removed, reset the form
      if (editingIndex === index) {
        resetCurrentService();
      } else if (editingIndex !== null && editingIndex > index) {
        // Adjust editing index if a service before the one being edited was removed
        setEditingIndex(editingIndex - 1);
      }
    } catch (error: any) {
      console.error("Error deleting service:", error);
      toast.error("Failed to delete service: " + (error.message || "Unknown error"));
    }
  };

  const handleSubmit = () => {
    onSave(userServices);
    setIsOpen(false);
  };

  const handleOpenChange = (open: boolean) => {
    if (open) {
      // Reset to original services when opening
      setUserServices(services);
      resetCurrentService();
    }
    setIsOpen(open);
  };

  const geocodeZipCode = async (zipCode: string) => {
    if (!zipCode || zipCode.length < 5) return;
    
    try {
      // Get authentication token for the edge function
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error("Authentication error:", sessionError);
        toast.error("Authentication error. Please try again.");
        return;
      }
      
      const token = sessionData?.session?.access_token;
      
      // Call the edge function with proper authorization
      const EDGE_FUNCTION_URL = "https://fisvmdyzaeedwnfnrhpi.supabase.co/functions/v1/reverse-geocode";
      const response = await fetch(EDGE_FUNCTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token || ''}`
        },
        body: JSON.stringify({ zipCode }),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Error Status:", response.status, response.statusText);
        console.error("API Error Body:", errorText);
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log("Geocode response for zip code:", data);
      
      if (data.error) {
        console.error("Function returned error:", data.error);
        toast.error(`Error: ${data.error}`);
        return;
      }
      
      // Check if we have valid location data
      if (!data.latitude || !data.longitude) {
        toast.error("Unable to find coordinates for this zip code");
        return;
      }
      
      setCurrentService({
        ...currentService,
        location: {
          city: data.city || "",
          zipCode: zipCode, // Always store the original zip code the user entered
          coordinates: {
            lat: data.latitude,
            lng: data.longitude
          }
        }
      });
      
      if (data.city) {
        toast.success(`Location found: ${data.city}, ${data.state || data.county || ""}`);
      } else {
        toast.success("Location coordinates found");
      }
    } catch (error) {
      console.error("Error geocoding zip code:", error);
      toast.error("Error finding location");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button className="w-full bg-secondary hover:bg-secondary/90 text-secondary-foreground">
          <Briefcase size={16} className="mr-2" />
          Add/Manage Services
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Services I Offer</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">My Current Services</h3>
            {userServices.length === 0 ? (
              <p className="text-sm text-muted-foreground">You haven't added any services yet.</p>
            ) : (
              <div className="space-y-2">
                {userServices.map((service, index) => (
                  <div key={index} className={`flex items-start justify-between bg-muted/40 p-3 rounded-md ${editingIndex === index ? 'ring-2 ring-forest' : ''}`}>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <h4 className="font-medium text-sm">{service.type}</h4>
                        {service.contactMethod && (
                          <span className="text-xs text-muted-foreground">
                            {service.contactMethod}
                          </span>
                        )}
                      </div>
                      <p className="text-xs mt-1">{service.description}</p>
                      {service.location && (service.location.zipCode || service.location.city) && (
                        <p className="text-xs flex items-center gap-1 mt-1">
                          <MapPin size={12} />
                          {service.location.zipCode}
                          {service.location.city && ` (${service.location.city})`}
                        </p>
                      )}
                      {service.contactValue && (
                        <p className="text-xs text-forest mt-1">
                          {service.contactValue}
                        </p>
                      )}
                    </div>
                    <div className="flex gap-1 ml-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleEditService(index)}
                        className="text-forest h-auto p-1"
                        title="Edit service"
                      >
                        <Edit size={16} />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleRemoveService(index)}
                        className="text-destructive h-auto p-1"
                        title="Delete service"
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          <Separator className="my-4" />
          
          <div className="space-y-4">
            <h3 className="text-sm font-medium">
              {editingIndex !== null ? 'Edit Service' : 'Add New Service'}
            </h3>
            
            <div className="space-y-2">
              <Label htmlFor="serviceType">Service Type</Label>
              <Select 
                value={currentService.type} 
                onValueChange={(value) => setCurrentService({...currentService, type: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a service type" />
                </SelectTrigger>
                <SelectContent>
                  {SERVICE_TYPES.map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="description">Description</Label>
                <span className={`text-xs ${currentService.description.length > 150 ? 'text-destructive' : 'text-muted-foreground'}`}>
                  {currentService.description.length}/150
                </span>
              </div>
              <Textarea
                id="description"
                value={currentService.description}
                onChange={(e) => setCurrentService({...currentService, description: e.target.value})}
                placeholder="Briefly describe your service (max 150 characters)"
                className="resize-none"
                rows={3}
              />
            </div>

            {/* Promotion Subscription Required Notice */}
            {!isUserPromoted && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <Crown className="h-4 w-4 text-yellow-600" />
                <AlertDescription className="text-yellow-800">
                  You need a promotion subscription to add services. <strong>Subscribe to our "Promote Your Services" plan</strong> to list your services and reach more customers.
                  <div className="mt-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={handleVerifySubscription}
                      className="text-xs"
                    >
                      Already paid? Verify Subscription
                    </Button>
                  </div>
                </AlertDescription>
              </Alert>
            )}
            
            <div className="space-y-4 border p-3 rounded-md bg-muted/30">
              <h4 className="text-sm font-medium flex items-center gap-1">
                <MapPin size={14} />
                Location Information
              </h4>
              
              <div className="space-y-2">
                <Label htmlFor="zipCode">Zip Code</Label>
                <Input
                  id="zipCode"
                  value={currentService.location?.zipCode || ""}
                  onChange={(e) => {
                    const zipCode = e.target.value;
                    setCurrentService({
                      ...currentService, 
                      location: {
                        ...currentService.location,
                        zipCode
                      }
                    });
                    
                    // Auto-geocode when a valid zip is entered
                    if (zipCode.length === 5 && /^\d{5}$/.test(zipCode)) {
                      geocodeZipCode(zipCode);
                    }
                  }}
                  placeholder="Enter zip code"
                />
              </div>
              
              <p className="text-xs text-muted-foreground">
                Adding your zip code helps potential clients find your services in their area.
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="contactMethod">Contact Method (Optional)</Label>
              <Select 
                value={currentService.contactMethod || ""} 
                onValueChange={(value) => setCurrentService({...currentService, contactMethod: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="How can people contact you?" />
                </SelectTrigger>
                <SelectContent>
                  {CONTACT_METHODS.map((method) => (
                    <SelectItem key={method} value={method}>{method}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {currentService.contactMethod && (
              <div className="space-y-2">
                <Label htmlFor="contactValue">
                  {currentService.contactMethod === "Email" ? "Email Address" : 
                   currentService.contactMethod === "Phone" ? "Phone Number" :
                   currentService.contactMethod === "Website" ? "Website URL" : "Contact Info"}
                </Label>
                <Input
                  id="contactValue"
                  value={currentService.contactValue || ""}
                  onChange={(e) => setCurrentService({...currentService, contactValue: e.target.value})}
                  placeholder={
                    currentService.contactMethod === "Email" ? "<EMAIL>" : 
                    currentService.contactMethod === "Phone" ? "+****************" :
                    currentService.contactMethod === "Website" ? "https://yourwebsite.com" : 
                    "Contact information"
                  }
                />
              </div>
            )}
            
            <div className="flex gap-2">
              <Button 
                type="button" 
                onClick={editingIndex !== null ? handleAddOrUpdateService : handleAddService}
                className="flex-1"
                disabled={!currentService.type || !currentService.description || (!isUserPromoted && editingIndex === null)}
              >
                {editingIndex !== null ? (
                  <>
                    <Edit size={16} className="mr-2" />
                    Update Service
                  </>
                ) : (
                  <>
                    <Plus size={16} className="mr-2" />
                    Add Service
                  </>
                )}
              </Button>
              
              {editingIndex !== null && (
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={resetCurrentService}
                >
                  Cancel
                </Button>
              )}
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSubmit} className="bg-forest hover:bg-forest-dark">
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ServicesDialog;
