
import { useState, useEffect, useRef } from "react";
import VideoPlayer from "./VideoPlayer";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { RefreshCcw, Play } from "lucide-react";
import { getCachedOrGenerateThumbnail, isMobileDevice } from "@/utils/videoThumbnail";
import { supabase } from "@/integrations/supabase/client";

interface VideoThumbnailProps {
  videoId: string;
  videoUrl: string;
  soundtrackUrl?: string | null;
  count?: number;
  countLabel?: string;
  autoPlay?: boolean;
  onClick?: (e: React.MouseEvent) => void;
  controls?: boolean;
  children?: React.ReactNode;
  size?: "default" | "small";
  navigateOnClick?: boolean;
}

const VideoThumbnail = ({
  videoId,
  videoUrl,
  soundtrackUrl,
  count,
  countLabel = "",
  autoPlay = false,
  onClick,
  controls = false,
  children,
  size = "default",
  navigateOnClick = true
}: VideoThumbnailProps) => {
  const [isHovering, setIsHovering] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [loadError, setLoadError] = useState(false);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [isGeneratingThumbnail, setIsGeneratingThumbnail] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const navigate = useNavigate();

  // Generate thumbnail on mount for mobile devices
  useEffect(() => {
    const shouldGenerateThumbnail = isMobileDevice() && videoUrl && !autoPlay;

    console.log('VideoThumbnail useEffect:', {
      isMobile: isMobileDevice(),
      hasVideoUrl: !!videoUrl,
      autoPlay,
      shouldGenerate: shouldGenerateThumbnail
    });

    if (shouldGenerateThumbnail) {
      setIsGeneratingThumbnail(true);

      getCachedOrGenerateThumbnail(videoId, videoUrl, {
        quality: 0.8,
        timeOffset: 0.1, // Get thumbnail from 10% into the video
        maxWidth: 400,
        maxHeight: 600
      }).then((thumbnail) => {
        console.log('Thumbnail generated successfully:', !!thumbnail);
        setThumbnailUrl(thumbnail);
        setIsGeneratingThumbnail(false);
      }).catch((error) => {
        console.error('Error generating thumbnail for video:', videoId, error);
        setIsGeneratingThumbnail(false);
      });
    }
  }, [videoId, videoUrl, autoPlay]);
  
  const handleClick = async () => {
    // If there's a custom onClick handler, call it first
    if (onClick) {
      const syntheticEvent = {
        preventDefault: () => {},
        stopPropagation: () => {}
      } as React.MouseEvent;
      onClick(syntheticEvent);
      return;
    }

    // Track the view when video is clicked (for profile pages)
    if (!navigateOnClick) {
      try {
        const { error } = await supabase.rpc('increment_video_views', {
          video_id_param: videoId
        });
        
        if (error) {
          console.error('Error incrementing video views:', error);
        }
      } catch (error) {
        console.error('Error tracking video view:', error);
      }
    }
    
    if (navigateOnClick) {
      navigate(`/video/${videoId}`);
    } else {
      setIsFullscreen(!isFullscreen);
    }
  };
  
  const closeFullscreen = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsFullscreen(false);
  };
  
  const handleRetry = (e: React.MouseEvent) => {
    e.stopPropagation();
    setLoadError(false);
  };

  return (
    <>
      <div 
        className={`relative rounded-lg overflow-hidden shadow-md h-full w-full ${size === "small" ? "max-w-xs mx-auto" : ""}`}
        onClick={loadError ? handleRetry : handleClick}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        <AspectRatio ratio={9/16} className="bg-black">
          {loadError ? (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-black text-white text-center p-4">
              <p className="mb-4">Error loading video</p>
              <Button
                onClick={handleRetry}
                variant="outline"
                className="bg-forest hover:bg-forest/80 text-white"
                size="sm"
              >
                <RefreshCcw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            </div>
          ) : (
            <>
              {/* Show thumbnail with play button for mobile when not auto-playing */}
              {thumbnailUrl && !autoPlay && (
                <div className="absolute inset-0 z-10" onClick={handleClick}>
                  <img
                    src={thumbnailUrl}
                    alt="Video thumbnail"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                    <div className="bg-black/60 rounded-full p-3">
                      <Play className="w-8 h-8 text-white fill-white" />
                    </div>
                  </div>
                </div>
              )}

              {/* Show loading state while generating thumbnail */}
              {isGeneratingThumbnail && !autoPlay && (
                <div className="absolute inset-0 flex items-center justify-center bg-black z-10">
                  <div className="text-white text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white mx-auto mb-2"></div>
                    <p className="text-sm">Loading preview...</p>
                  </div>
                </div>
              )}

              {/* Fallback for mobile when no thumbnail and not generating */}
              {!thumbnailUrl && !isGeneratingThumbnail && !autoPlay && (
                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 z-10" onClick={handleClick}>
                  <div className="text-white text-center">
                    <div className="bg-black/60 rounded-full p-4 mb-2">
                      <Play className="w-12 h-12 text-white fill-white" />
                    </div>
                    <p className="text-sm opacity-80">Tap to play</p>
                  </div>
                </div>
              )}

              <VideoPlayer
                url={videoUrl}
                audioUrl={soundtrackUrl}
                muted={!autoPlay}
                autoPlay={autoPlay}
                onClick={handleClick}
                enableAudioControl={true}
                onError={() => setLoadError(true)}
                poster={thumbnailUrl || undefined}
                preload={autoPlay ? "auto" : "metadata"}
                lazyLoad={!autoPlay}
              />
            </>
          )}
        </AspectRatio>
        
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 hover:opacity-100 transition-opacity pointer-events-none" />
        
        {count !== undefined && (
          <div className="absolute bottom-3 right-3 bg-black/70 px-2 py-1 rounded text-sm text-white font-medium">
            {count > 1000 ? `${(count / 1000).toFixed(1)}K${countLabel}` : `${count}${countLabel}`}
          </div>
        )}
        
        {children}
      </div>
      
      {isFullscreen && (
        <div 
          className="fixed inset-0 bg-black z-50 flex items-center justify-center pt-safe"
          onClick={closeFullscreen}
        >
          <div className="relative w-full max-w-3xl max-h-screen">
            <button 
              className="absolute top-4 right-4 z-10 bg-black/50 text-white p-2 rounded-full"
              onClick={closeFullscreen}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </button>
            <VideoPlayer 
              url={videoUrl}
              audioUrl={soundtrackUrl}
              muted={false}
              autoPlay={true}
              controls={true}
              enableAudioControl={true}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default VideoThumbnail;
