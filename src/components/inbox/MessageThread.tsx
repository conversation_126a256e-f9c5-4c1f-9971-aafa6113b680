import React, { useState, useEffect, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar } from "@/components/ui/avatar";
import { ArrowLeft, Send, Phone, Video, MoreHorizontal, Smile, Paperclip } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import MessageItem from "./MessageItem";
import TypingIndicator from "./TypingIndicator";
import { useIsMobile } from "@/hooks/use-mobile";
import { Message } from "./types";

interface MessageThreadProps {
  conversationId: string;
  onBack: () => void;
  onRefreshList: () => void;
}

// Helper function to transform raw database message to Message type
const transformDatabaseMessage = (dbMessage: any): Message => {
  console.log('🔄 Transforming message:', dbMessage);
  
  let attachments = [];
  if (dbMessage.attachments) {
    if (Array.isArray(dbMessage.attachments)) {
      attachments = dbMessage.attachments;
    } else if (typeof dbMessage.attachments === 'string') {
      try {
        attachments = JSON.parse(dbMessage.attachments);
      } catch (e) {
        console.warn('Failed to parse attachments:', e);
        attachments = [];
      }
    }
  }

  return {
    id: dbMessage.id,
    sender_id: dbMessage.sender_id,
    receiver_id: dbMessage.receiver_id,
    content: dbMessage.content,
    created_at: dbMessage.created_at,
    read: dbMessage.read,
    edited: dbMessage.edited || false,
    edited_at: dbMessage.edited_at || null,
    attachments: attachments,
    reply_to_message_id: dbMessage.reply_to_message_id || undefined
  };
};

const MessageThread: React.FC<MessageThreadProps> = ({ 
  conversationId, 
  onBack, 
  onRefreshList 
}) => {
  const { user, session } = useAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [sending, setSending] = useState(false);
  const [conversationUser, setConversationUser] = useState<{
    id: string;
    username: string;
    avatar_url: string | null;
  } | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current && initialLoadComplete) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [initialLoadComplete]);

  const fetchMessages = useCallback(async () => {
    if (!user || !conversationId || !session) {
      console.log('⚠️ Missing user, conversationId, or session:', { 
        user: !!user, 
        conversationId, 
        session: !!session 
      });
      setLoading(false);
      return;
    }

    try {
      console.log('📨 Fetching messages for conversation:', conversationId);
      if (!initialLoadComplete) {
        setLoading(true);
      }

      console.log('🔑 Calling edge function with params:', { user1_id: user.id, user2_id: conversationId });

      // Use edge function to get messages with proper authentication
      const { data: edgeFunctionData, error: edgeFunctionError } = await supabase.functions.invoke('get_conversation_messages', {
        body: { 
          user1_id: user.id, 
          user2_id: conversationId 
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        }
      });

      let finalData = edgeFunctionData;

      if (edgeFunctionError) {
        console.error('❌ Edge function error:', edgeFunctionError);
        
        // Try to refresh session and retry once
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
        if (refreshError) {
          console.error('❌ Session refresh failed:', refreshError);
          toast({
            title: "Authentication error",
            description: "Please sign in again to continue.",
            variant: "destructive",
          });
          setLoading(false);
          return;
        }
        
        // Retry with refreshed session
        const { data: retryData, error: retryError } = await supabase.functions.invoke('get_conversation_messages', {
          body: { 
            user1_id: user.id, 
            user2_id: conversationId 
          },
          headers: {
            Authorization: `Bearer ${refreshData.session?.access_token}`,
          }
        });
        
        if (retryError) {
          console.error('❌ Retry failed:', retryError);
          toast({
            title: "Error loading messages",
            description: "Unable to load conversation. Please try again.",
            variant: "destructive",
          });
          setLoading(false);
          return;
        }
        
        console.log('✅ Retry successful, using refreshed data');
        finalData = retryData;
      }

      console.log('✅ Raw edge function response:', finalData);
      
      // Transform edge function data
      if (!finalData || !Array.isArray(finalData)) {
        console.log('📭 No messages data returned or invalid format');
        setMessages([]);
        if (!initialLoadComplete) {
          setInitialLoadComplete(true);
        }
        setLoading(false);
        return;
      }

      const transformedMessages = finalData.map(transformDatabaseMessage);
      
      console.log('✅ Transformed messages:', transformedMessages.length, transformedMessages);
      setMessages(transformedMessages);

      // Mark messages as read
      if (transformedMessages.length > 0) {
        const unreadMessages = transformedMessages.filter(
          (msg: Message) => msg.receiver_id === user.id && !msg.read
        );

        if (unreadMessages.length > 0) {
          console.log('📖 Marking', unreadMessages.length, 'messages as read');
          const { error: updateError } = await supabase
            .from('messages')
            .update({ read: true })
            .eq('sender_id', conversationId)
            .eq('receiver_id', user.id)
            .eq('read', false);
            
          if (updateError) {
            console.error('❌ Error marking messages as read:', updateError);
          }
        }
      }

      if (!initialLoadComplete) {
        setInitialLoadComplete(true);
      }

    } catch (error) {
      console.error("❌ Error fetching messages:", error);
      toast({
        title: "Error loading messages",
        description: "Please try refreshing the page.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [user, conversationId, session, toast, initialLoadComplete]);

  const fetchConversationUser = useCallback(async () => {
    if (!conversationId) return;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, avatar_url')
        .eq('id', conversationId)
        .single();

      if (error) {
        console.error("Error fetching conversation user:", error);
        // Set a fallback user if profile fetch fails
        setConversationUser({
          id: conversationId,
          username: 'User',
          avatar_url: null
        });
        return;
      }

      setConversationUser(data);
    } catch (error) {
      console.error("Error in fetchConversationUser:", error);
      // Set a fallback user
      setConversationUser({
        id: conversationId,
        username: 'User',
        avatar_url: null
      });
    }
  }, [conversationId]);

  useEffect(() => {
    fetchConversationUser();
    fetchMessages();
  }, [fetchConversationUser, fetchMessages]);

  useEffect(() => {
    if (!user || !session) return;

    console.log('🔄 Setting up real-time subscription for conversation:', conversationId);

    const channel = supabase
      .channel(`conversation_${conversationId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'messages',
        filter: `or(and(sender_id.eq.${user.id},receiver_id.eq.${conversationId}),and(sender_id.eq.${conversationId},receiver_id.eq.${user.id}))`
      }, (payload) => {
        console.log("📨 Real-time message event:", payload.eventType, payload.new);
        
        if (payload.eventType === 'INSERT') {
          const newMessage = transformDatabaseMessage(payload.new);
          setMessages(prev => {
            // Check if message already exists
            const exists = prev.some(msg => msg.id === newMessage.id);
            if (exists) return prev;
            
            // Add new message and sort by created_at
            const updated = [...prev, newMessage].sort((a, b) => 
              new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
            );
            return updated;
          });
          
          // Refresh the conversation list to update last message
          onRefreshList();
        } else if (payload.eventType === 'UPDATE') {
          const updatedMessage = transformDatabaseMessage(payload.new);
          setMessages(prev => 
            prev.map(msg => msg.id === updatedMessage.id ? updatedMessage : msg)
          );
        } else if (payload.eventType === 'DELETE') {
          const deletedMessage = payload.old as Message;
          setMessages(prev => prev.filter(msg => msg.id !== deletedMessage.id));
          onRefreshList();
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, session, conversationId, onRefreshList]);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const sendMessage = async () => {
    if (!newMessage.trim() || !user || !session || sending) return;

    try {
      setSending(true);
      console.log('📤 Sending message:', newMessage);

      const messageData = {
        sender_id: user.id,
        receiver_id: conversationId,
        content: newMessage.trim(),
        read: false,
        message_type: 'direct',
        group_chat_id: null,
        attachments: []
      };

      const { data, error } = await supabase.functions.invoke('send_direct_message', {
        body: messageData,
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        }
      });

      if (error) {
        throw error;
      }

      console.log('✅ Message sent successfully:', data);
      setNewMessage("");
      
      // Refresh the conversation list to update last message
      onRefreshList();

    } catch (error) {
      console.error("❌ Error sending message:", error);
      toast({
        title: "Failed to send message",
        description: "Please try again.",
        variant: "destructive",
      });
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  if (loading && !initialLoadComplete) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-forest mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading conversation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center gap-3 p-4 border-b bg-white">
        {isMobile && (
          <Button variant="ghost" size="sm" onClick={onBack} className="h-8 w-8 p-0">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        )}
        
        <Avatar className="h-10 w-10">
          <img 
            src={conversationUser?.avatar_url || `https://ui-avatars.com/api/?name=${conversationUser?.username || 'User'}`} 
            alt={conversationUser?.username || 'User'}
            className="object-cover"
          />
        </Avatar>
        
        <div className="flex-1">
          <h3 className="font-semibold">{conversationUser?.username || 'Loading...'}</h3>
          <p className="text-sm text-muted-foreground">Active now</p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Phone className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Video className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4">
          {messages.length === 0 && !loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No messages yet. Start the conversation!</p>
            </div>
          ) : (
            messages.map((message) => (
              <MessageItem
                key={message.id}
                message={message}
                senderUsername={
                  message.sender_id === user?.id 
                    ? user?.user_metadata?.username || 'You'
                    : conversationUser?.username || 'User'
                }
                senderAvatar={
                  message.sender_id === user?.id 
                    ? user?.user_metadata?.avatar_url 
                    : conversationUser?.avatar_url
                }
                onReply={(message) => {
                  // Handle reply functionality if needed
                }}
                onMessageUpdated={fetchMessages}
              />
            ))
          )}
          
          <TypingIndicator 
            conversationId={conversationId}
            isGroupChat={false}
          />
          
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="p-4 border-t bg-white">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Paperclip className="h-4 w-4" />
          </Button>
          
          <div className="flex-1 relative">
            <Input
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="pr-10"
              disabled={sending}
            />
            <Button 
              variant="ghost" 
              size="sm" 
              className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>
          
          <Button 
            onClick={sendMessage} 
            disabled={!newMessage.trim() || sending}
            size="sm"
            className="h-8 w-8 p-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MessageThread;
