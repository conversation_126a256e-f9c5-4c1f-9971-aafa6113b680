
import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ArrowLeft, Send, MoreVertical, Users, Settings } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/components/ui/use-toast";
import { format } from "date-fns";
import FileAttachment from "./FileAttachment";

interface GroupMessageThreadProps {
  groupChatId: string;
  onBack: () => void;
  onRefreshList: () => void;
}

interface Message {
  id: string;
  content: string;
  created_at: string;
  sender_id: string;
  attachments: any[];
  sender?: {
    username: string;
    avatar_url: string | null;
  };
}

interface GroupChat {
  id: string;
  name: string;
  description: string | null;
  memberCount: number;
}

const GroupMessageThread: React.FC<GroupMessageThreadProps> = ({
  groupChatId,
  onBack,
  onRefreshList,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [messages, setMessages] = useState<Message[]>([]);
  const [groupChat, setGroupChat] = useState<GroupChat | null>(null);
  const [newMessage, setNewMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchGroupChat();
    fetchMessages();

    // Set up real-time listener for new group messages
    const messagesChannel = supabase
      .channel(`group_messages_${groupChatId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'messages',
        filter: `group_chat_id=eq.${groupChatId}`
      }, (payload) => {
        console.log('📨 Real-time group message received:', payload);
        if (payload.new && payload.new.sender_id !== user?.id) {
          fetchMessages();
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(messagesChannel);
    };
  }, [groupChatId, user]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const fetchGroupChat = async () => {
    try {
      console.log('🏗️ Fetching group chat info:', groupChatId);
      
      const { data, error } = await supabase
        .from('group_chats')
        .select(`
          *,
          group_chat_members(count)
        `)
        .eq('id', groupChatId)
        .single();

      if (error) {
        console.error('❌ Error fetching group chat:', error);
        throw error;
      }

      console.log('✅ Group chat fetched:', data);

      setGroupChat({
        id: data.id,
        name: data.name,
        description: data.description,
        memberCount: data.group_chat_members?.[0]?.count || 0,
      });
    } catch (error) {
      console.error("Error fetching group chat:", error);
      toast({
        title: "Error loading group chat",
        description: "Please try again later.",
        variant: "destructive",
      });
    }
  };

  const fetchMessages = async () => {
    try {
      console.log('📨 Fetching group messages for:', groupChatId);
      
      // First get messages
      const { data: messagesData, error: messagesError } = await supabase
        .from('messages')
        .select('*')
        .eq('group_chat_id', groupChatId)
        .eq('message_type', 'group')
        .order('created_at', { ascending: true });

      if (messagesError) {
        console.error('❌ Error fetching messages:', messagesError);
        throw messagesError;
      }

      console.log('✅ Messages fetched:', messagesData?.length || 0);

      // Then get unique sender IDs
      const senderIds = [...new Set(messagesData?.map(msg => msg.sender_id) || [])];
      
      // Fetch profiles for all senders
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, username, avatar_url')
        .in('id', senderIds);

      if (profilesError) {
        console.error('❌ Error fetching profiles:', profilesError);
        throw profilesError;
      }

      console.log('✅ Profiles fetched:', profilesData?.length || 0);

      // Create a map of profiles by ID
      const profilesMap = new Map(profilesData?.map(p => [p.id, p]) || []);

      const formattedMessages = messagesData?.map(msg => ({
        id: msg.id,
        content: msg.content,
        created_at: msg.created_at,
        sender_id: msg.sender_id,
        attachments: Array.isArray(msg.attachments) ? msg.attachments : [],
        sender: profilesMap.get(msg.sender_id) ? {
          username: profilesMap.get(msg.sender_id)?.username || 'Unknown',
          avatar_url: profilesMap.get(msg.sender_id)?.avatar_url || null
        } : undefined
      })) || [];

      setMessages(formattedMessages);
    } catch (error) {
      console.error("Error fetching messages:", error);
      toast({
        title: "Error loading messages",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user) return;

    setSending(true);
    try {
      console.log('📤 Sending group message:', {
        content: newMessage.trim(),
        sender_id: user.id,
        group_chat_id: groupChatId,
        message_type: 'group'
      });

      const { error } = await supabase
        .from('messages')
        .insert({
          content: newMessage.trim(),
          sender_id: user.id,
          receiver_id: '', // Empty string for group messages
          group_chat_id: groupChatId,
          message_type: 'group',
          read: false,
          attachments: []
        });

      if (error) {
        console.error('❌ Error sending group message:', error);
        throw error;
      }

      console.log('✅ Group message sent successfully');

      setNewMessage("");
      fetchMessages();
      onRefreshList();
    } catch (error) {
      console.error("Error sending message:", error);
      toast({
        title: "Error sending message",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatMessageTime = (timestamp: string) => {
    return format(new Date(timestamp), "h:mm a");
  };

  const isMobileView = () => {
    return window.innerWidth < 768;
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-muted-foreground">Loading group chat...</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b p-4 flex items-center justify-between bg-white">
        <div className="flex items-center gap-3">
          {isMobileView() && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
          )}
          <Avatar className="h-10 w-10">
            <div className="bg-forest text-white flex items-center justify-center h-full w-full text-lg font-semibold">
              {groupChat?.name.charAt(0).toUpperCase()}
            </div>
          </Avatar>
          <div>
            <h3 className="font-semibold">{groupChat?.name}</h3>
            <p className="text-sm text-muted-foreground">
              {groupChat?.memberCount} members
            </p>
          </div>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreVertical className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Users className="h-4 w-4 mr-2" />
              View Members
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="h-4 w-4 mr-2" />
              Group Settings
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <div key={message.id} className="flex items-start gap-3">
              <Avatar className="h-8 w-8">
                <img
                  src={message.sender?.avatar_url || `https://ui-avatars.com/api/?name=${message.sender?.username}`}
                  alt={message.sender?.username}
                  className="object-cover"
                />
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-sm font-medium">
                    {message.sender?.username}
                  </span>
                  {message.sender_id === user?.id && (
                    <Badge variant="secondary" className="text-xs">You</Badge>
                  )}
                  <span className="text-xs text-muted-foreground">
                    {formatMessageTime(message.created_at)}
                  </span>
                </div>
                <div className="bg-gray-100 rounded-lg p-3 text-sm">
                  <p>{message.content}</p>
                  {message.attachments?.length > 0 && (
                    <div className="mt-2 space-y-2">
                      {message.attachments.map((attachment: any, index: number) => (
                        <FileAttachment key={index} attachment={attachment} />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Message Input */}
      <div className="border-t p-4 bg-white">
        <div className="flex gap-2">
          <Input
            placeholder="Type a message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || sending}
            size="icon"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default GroupMessageThread;
