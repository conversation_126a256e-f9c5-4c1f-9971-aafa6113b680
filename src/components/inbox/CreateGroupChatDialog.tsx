
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Avatar } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, Users, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/components/ui/use-toast";

interface CreateGroupChatDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGroupCreated: () => void;
}

interface User {
  id: string;
  username: string;
  avatar_url: string | null;
}

const CreateGroupChatDialog: React.FC<CreateGroupChatDialogProps> = ({
  open,
  onOpenChange,
  onGroupCreated,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [groupName, setGroupName] = useState("");
  const [groupDescription, setGroupDescription] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);

  React.useEffect(() => {
    if (open && searchQuery.trim()) {
      searchUsers();
    } else {
      setUsers([]);
    }
  }, [searchQuery, open]);

  const searchUsers = async () => {
    if (!searchQuery.trim()) return;
    
    setSearchLoading(true);
    try {
      console.log('🔍 Searching for users with query:', searchQuery);
      
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, avatar_url')
        .ilike('username', `%${searchQuery}%`)
        .neq('id', user?.id)
        .limit(20);

      if (error) {
        console.error('❌ Error searching users:', error);
        throw error;
      }
      
      console.log('✅ Found users:', data?.length || 0);
      setUsers(data || []);
    } catch (error) {
      console.error("Error searching users:", error);
      toast({
        title: "Error searching users",
        description: "Please try again later.",
        variant: "destructive",
      });
      setUsers([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleUserSelect = (userId: string) => {
    const newSelected = new Set(selectedUsers);
    if (newSelected.has(userId)) {
      newSelected.delete(userId);
    } else {
      newSelected.add(userId);
    }
    setSelectedUsers(newSelected);
  };

  const handleCreateGroup = async () => {
    if (!user || !groupName.trim() || selectedUsers.size === 0) {
      toast({
        title: "Missing information",
        description: "Please enter a group name and select at least one member.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      console.log('🏗️ Creating group chat:', {
        name: groupName,
        description: groupDescription,
        created_by: user.id,
        memberCount: selectedUsers.size
      });

      // Create the group chat - the RLS policy should now work correctly
      const { data: groupChat, error: groupError } = await supabase
        .from('group_chats')
        .insert({
          name: groupName,
          description: groupDescription || null,
          created_by: user.id, // Use the user ID from auth context
        })
        .select()
        .single();

      if (groupError) {
        console.error('❌ Error creating group chat:', groupError);
        throw groupError;
      }

      console.log('✅ Group chat created:', groupChat.id);

      // Add the creator as an admin
      const { error: creatorError } = await supabase
        .from('group_chat_members')
        .insert({
          group_chat_id: groupChat.id,
          user_id: user.id,
          role: 'admin',
        });

      if (creatorError) {
        console.error('❌ Error adding creator as admin:', creatorError);
        throw creatorError;
      }

      console.log('✅ Creator added as admin');

      // Add selected members
      const memberInserts = Array.from(selectedUsers).map(userId => ({
        group_chat_id: groupChat.id,
        user_id: userId,
        role: 'member',
      }));

      if (memberInserts.length > 0) {
        console.log('👥 Adding members:', memberInserts.length);
        
        const { error: membersError } = await supabase
          .from('group_chat_members')
          .insert(memberInserts);

        if (membersError) {
          console.error('❌ Error adding members:', membersError);
          throw membersError;
        }

        console.log('✅ Members added successfully');
      }

      toast({
        title: "Group chat created",
        description: `"${groupName}" has been created successfully.`,
      });

      // Reset form
      setGroupName("");
      setGroupDescription("");
      setSelectedUsers(new Set());
      setSearchQuery("");
      setUsers([]);
      onOpenChange(false);
      onGroupCreated();
    } catch (error) {
      console.error("Error creating group chat:", error);
      toast({
        title: "Error creating group",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const selectedUsersList = users.filter(u => selectedUsers.has(u.id));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Create Group Chat
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="groupName">Group Name</Label>
            <Input
              id="groupName"
              placeholder="Enter group name"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="groupDescription">Description (Optional)</Label>
            <Textarea
              id="groupDescription"
              placeholder="Enter group description"
              value={groupDescription}
              onChange={(e) => setGroupDescription(e.target.value)}
              rows={2}
            />
          </div>

          <div className="space-y-2">
            <Label>Add Members</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users to add..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {selectedUsersList.length > 0 && (
            <div className="space-y-2">
              <Label>Selected Members ({selectedUsersList.length})</Label>
              <div className="flex flex-wrap gap-2">
                {selectedUsersList.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center gap-2 bg-forest/10 rounded-full px-3 py-1 text-sm"
                  >
                    <Avatar className="h-6 w-6">
                      <img
                        src={user.avatar_url || `https://ui-avatars.com/api/?name=${user.username}`}
                        alt={user.username}
                        className="object-cover"
                      />
                    </Avatar>
                    <span>{user.username}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4"
                      onClick={() => handleUserSelect(user.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {searchQuery && (
            <ScrollArea className="h-40 border rounded-md p-2">
              {searchLoading ? (
                <div className="text-center py-4 text-muted-foreground">
                  Searching...
                </div>
              ) : users.length > 0 ? (
                <div className="space-y-2">
                  {users.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-md cursor-pointer"
                      onClick={() => handleUserSelect(user.id)}
                    >
                      <Checkbox
                        checked={selectedUsers.has(user.id)}
                        onChange={() => handleUserSelect(user.id)}
                      />
                      <Avatar className="h-8 w-8">
                        <img
                          src={user.avatar_url || `https://ui-avatars.com/api/?name=${user.username}`}
                          alt={user.username}
                          className="object-cover"
                        />
                      </Avatar>
                      <span className="text-sm font-medium">{user.username}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No users found
                </div>
              )}
            </ScrollArea>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateGroup}
            disabled={loading || !groupName.trim() || selectedUsers.size === 0}
          >
            {loading ? "Creating..." : "Create Group"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateGroupChatDialog;
