import React, { useState, useEffect, memo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Plus } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/components/ui/use-toast";

interface Reaction {
  id: string;
  emoji: string;
  user_id: string;
  created_at: string;
}

interface MessageReactionsProps {
  messageId: string;
  reactions: Reaction[];
  onReactionChange: () => void;
}

const COMMON_EMOJIS = ["👍", "❤️", "😂", "😮", "😢", "😡", "👏", "🔥"];

const MessageReactions: React.FC<MessageReactionsProps> = ({
  messageId,
  reactions = [], // Provide default empty array to prevent undefined errors
  onReactionChange,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [localReactions, setLocalReactions] = useState<Reaction[]>(reactions);

  // Update local reactions when props change
  useEffect(() => {
    setLocalReactions(reactions || []);
  }, [reactions]);

  // Fetch reactions using edge function to bypass RLS issues
  const fetchReactions = async () => {
    try {
      console.log('🔍 Fetching reactions for message:', messageId);
      
      const { data, error } = await supabase.functions.invoke('get_reactions', {
        body: { messageId }
      });

      if (error) {
        console.error('❌ Error fetching reactions:', error);
        throw error;
      }

      console.log('✅ Fetched reactions successfully:', data?.data?.length || 0);
      setLocalReactions(data?.data || []);
      
      // Also trigger the parent callback
      onReactionChange();
    } catch (error) {
      console.error("Error fetching reactions:", error);
    }
  };

  // Group reactions by emoji - make sure localReactions is never undefined
  const groupedReactions = (localReactions || []).reduce((acc, reaction) => {
    if (!acc[reaction.emoji]) {
      acc[reaction.emoji] = [];
    }
    acc[reaction.emoji].push(reaction);
    return acc;
  }, {} as Record<string, Reaction[]>);

  const addReaction = async (emoji: string) => {
    if (!user || isProcessing) {
      console.log('🚫 Cannot add reaction - user not logged in or processing');
      return;
    }

    setIsProcessing(true);
    console.log('🎭 Starting reaction process for emoji:', emoji, 'messageId:', messageId, 'userId:', user.id);
    
    try {
      // Check if user already reacted with this emoji
      const existingReaction = (localReactions || []).find(
        (r) => r.user_id === user.id && r.emoji === emoji
      );

      if (existingReaction) {
        console.log('🗑️ Removing existing reaction:', existingReaction.id);
        
        // Use the service role to bypass RLS for deletion
        const { error: deleteError } = await supabase.functions.invoke('delete_reaction', {
          body: { 
            reactionId: existingReaction.id,
            userId: user.id 
          }
        });

        if (deleteError) {
          console.error('❌ Delete error:', deleteError);
          throw deleteError;
        }
        
        console.log('✅ Reaction removed successfully');
        toast({
          title: "Reaction removed",
          description: `Removed ${emoji} reaction`,
        });
      } else {
        console.log('➕ Adding new reaction');
        
        // Use edge function to bypass RLS for insertion
        const { error: insertError } = await supabase.functions.invoke('add_reaction', {
          body: {
            messageId: messageId,
            userId: user.id,
            emoji: emoji,
          }
        });

        if (insertError) {
          console.error('❌ Insert error details:', insertError);
          throw insertError;
        }
        
        console.log('✅ Reaction added successfully');
        toast({
          title: "Reaction added",
          description: `Added ${emoji} reaction`,
        });
      }

      // Refresh reactions to show updated state
      console.log('🔄 Triggering reaction refresh');
      await fetchReactions();
      setIsOpen(false);
      
    } catch (error: any) {
      console.error("💥 Detailed error in reaction handling:", {
        error,
        errorCode: error?.code,
        errorMessage: error?.message,
        errorDetails: error?.details,
        errorHint: error?.hint,
        messageId,
        userId: user.id,
        emoji
      });
      
      const actionType = (localReactions || []).find(r => r.user_id === user.id && r.emoji === emoji) ? 'remove' : 'add';
      toast({
        title: "Error",
        description: `Failed to ${actionType} reaction: ${error?.message || 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const hasUserReacted = (emoji: string) => {
    return (localReactions || []).some(
      (r) => r.user_id === user?.id && r.emoji === emoji
    );
  };

  // Log current state for debugging
  useEffect(() => {
    console.log('🔍 MessageReactions state:', {
      messageId,
      userId: user?.id,
      reactionsCount: localReactions?.length || 0,
      reactions: localReactions?.map(r => ({ emoji: r.emoji, user_id: r.user_id })) || [],
      isProcessing
    });
  }, [messageId, user?.id, localReactions, isProcessing]);

  // Fetch reactions on mount with cleanup
  useEffect(() => {
    let mounted = true;
    
    if (messageId) {
      fetchReactions();
    }
    
    return () => {
      mounted = false;
    };
  }, [messageId]);

  if (!user) {
    console.log('👤 No user found, not rendering reactions');
    return null;
  }

  return (
    <div className="flex items-center gap-1 mt-1">
      {/* Display existing reactions */}
      {Object.entries(groupedReactions).map(([emoji, emojiReactions]) => (
        <Button
          key={emoji}
          variant="ghost"
          size="sm"
          className={`h-6 px-2 text-xs rounded-full transition-colors ${
            hasUserReacted(emoji)
              ? "bg-forest/20 text-forest border border-forest/30"
              : "bg-gray-100 hover:bg-gray-200 text-gray-600"
          }`}
          onClick={() => !isProcessing && addReaction(emoji)}
          disabled={isProcessing}
        >
          {emoji} {emojiReactions.length}
        </Button>
      ))}

      {/* Add reaction button */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 rounded-full hover:bg-gray-200 text-gray-500"
            disabled={isProcessing}
          >
            <Plus className="h-3 w-3" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-2" side="top">
          <div className="grid grid-cols-4 gap-1">
            {COMMON_EMOJIS.map((emoji) => (
              <Button
                key={emoji}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-lg hover:bg-gray-100 transition-colors"
                onClick={() => addReaction(emoji)}
                disabled={isProcessing}
              >
                {emoji}
              </Button>
            ))}
          </div>
          {isProcessing && (
            <div className="text-xs text-center mt-2 text-gray-500">
              Processing...
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
};

// Use React.memo to prevent unnecessary re-renders
export default memo(MessageReactions);
