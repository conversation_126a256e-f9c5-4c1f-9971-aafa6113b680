
import React, { useState, useEffect, use<PERSON>allback, useMemo, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar } from "@/components/ui/avatar";
import { ArrowLeft, Send, X, Search, Paperclip, Image } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/components/ui/use-toast";
import FileAttachment from "./FileAttachment";

interface NewMessageDialogProps {
  onBack: () => void;
  onMessageSent: () => void;
  initialTargetUser?: {
    id: string;
    username: string;
    avatar_url: string | null;
  } | null;
}

interface User {
  id: string;
  username: string;
  avatar_url: string | null;
}

interface Attachment {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
}

const NewMessageDialog: React.FC<NewMessageDialogProps> = ({
  onBack,
  onMessageSent,
  initialTargetUser = null
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [message, setMessage] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(initialTargetUser);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Helper function to convert attachments to JSON for database storage
  const convertAttachmentsToJson = (attachments: Attachment[]) => {
    return attachments as any; // Cast to Json type for database
  };

  // Memoized filtered users to prevent unnecessary re-renders
  const filteredUsers = useMemo(() => 
    users.filter(u => 
      u.username.toLowerCase().includes(searchQuery.toLowerCase()) && 
      u.id !== user?.id
    ), [users, searchQuery, user?.id]
  );

  // Optimized users search with debouncing
  const searchUsers = useCallback(async (query: string) => {
    if (!query.trim()) {
      setUsers([]);
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, avatar_url')
        .ilike('username', `%${query}%`)
        .neq('id', user?.id || '')
        .limit(20)
        .order('username');

      if (error) throw error;
      
      setUsers(data || []);
    } catch (error) {
      console.error("Error searching users:", error);
      toast({
        title: "Error searching users",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id, toast]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery && !selectedUser) {
        searchUsers(searchQuery);
      } else if (!searchQuery) {
        setUsers([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, selectedUser, searchUsers]);

  // Optimized file upload
  const uploadFiles = useCallback(async (files: File[]): Promise<Attachment[]> => {
    const uploadedFiles = [];
    
    for (const file of files) {
      try {
        const fileExt = file.name.split('.').pop();
        const fileName = `${user!.id}/${selectedUser!.id}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
        
        console.log('📎 Uploading file:', fileName, 'Size:', file.size, 'Type:', file.type);
        
        const { data, error } = await supabase.storage
          .from('message-attachments')
          .upload(fileName, file);
        
        if (error) {
          console.error('❌ Upload error:', error);
          throw error;
        }
        
        const { data: urlData } = supabase.storage
          .from('message-attachments')
          .getPublicUrl(fileName);
        
        uploadedFiles.push({
          id: data.path,
          name: file.name,
          size: file.size,
          type: file.type,
          url: urlData.publicUrl
        });
      } catch (error) {
        console.error(`Failed to upload file ${file.name}:`, error);
        toast({
          title: "Upload failed",
          description: `Failed to upload ${file.name}`,
          variant: "destructive",
        });
      }
    }
    
    return uploadedFiles;
  }, [user, selectedUser, toast]);

  // Optimized send message function
  const handleSendMessage = useCallback(async () => {
    if (!user || !selectedUser || (!message.trim() && selectedFiles.length === 0)) {
      return;
    }

    const content = message.trim();
    setSending(true);
    setUploadingFiles(selectedFiles.length > 0);

    try {
      let uploadedFiles: Attachment[] = [];
      if (selectedFiles.length > 0) {
        console.log('📎 Starting file upload for', selectedFiles.length, 'files');
        uploadedFiles = await uploadFiles(selectedFiles);
        console.log('✅ Upload completed:', uploadedFiles);
      }

      console.log('📤 Sending new message to:', selectedUser.username);
      
      const messageData = {
        sender_id: user.id,
        receiver_id: selectedUser.id,
        content: content,
        read: false,
        message_type: 'direct' as const,
        group_chat_id: null,
        attachments: convertAttachmentsToJson(uploadedFiles),
      };

      // Use the edge function for sending messages
      const { error } = await supabase.functions.invoke('send_direct_message', {
        body: messageData
      });

      if (error) throw error;

      console.log('✅ Message sent successfully');
      
      toast({
        title: "Message sent",
        description: `Your message was sent to ${selectedUser.username}`,
      });

      onMessageSent();
      onBack();
    } catch (error) {
      console.error("Error sending message:", error);
      toast({
        title: "Error sending message",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setSending(false);
      setUploadingFiles(false);
    }
  }, [user, selectedUser, message, selectedFiles, uploadFiles, toast, onMessageSent, onBack]);

  const handleUserSelect = useCallback((selectedUser: User) => {
    setSelectedUser(selectedUser);
    setSearchQuery(selectedUser.username);
    setUsers([]);
  }, []);

  const handleClearSelection = useCallback(() => {
    setSelectedUser(null);
    setSearchQuery("");
    setUsers([]);
  }, []);

  // File handling
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setSelectedFiles(prev => [...prev, ...files]);
  }, []);

  const handleImageSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    setSelectedFiles(prev => [...prev, ...imageFiles]);
  }, []);

  const removeSelectedFile = useCallback((index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  }, []);

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header */}
      <div className="border-b px-4 py-3 flex items-center justify-between bg-white shadow-sm">
        <div className="flex items-center gap-3">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={onBack}
            className="h-9 w-9"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h2 className="text-lg font-semibold">New Message</h2>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <div className="p-4 space-y-4 h-full flex flex-col">
          {/* User Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">To:</label>
            <div className="relative">
              <div className="flex items-center gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search for a user..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-10"
                    disabled={!!selectedUser}
                  />
                  {selectedUser && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleClearSelection}
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Selected User Display */}
              {selectedUser && (
                <div className="mt-2 flex items-center gap-2 p-2 bg-forest/10 rounded-lg">
                  <Avatar className="h-8 w-8">
                    <img
                      src={selectedUser.avatar_url || `https://ui-avatars.com/api/?name=${selectedUser.username}`}
                      alt={selectedUser.username}
                      className="object-cover"
                    />
                  </Avatar>
                  <span className="text-sm font-medium">{selectedUser.username}</span>
                </div>
              )}

              {/* User Search Results */}
              {!selectedUser && searchQuery && (
                <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-white border rounded-lg shadow-lg max-h-48 overflow-hidden">
                  <ScrollArea className="max-h-48">
                    {loading ? (
                      <div className="p-4 text-center text-sm text-muted-foreground">
                        Searching users...
                      </div>
                    ) : filteredUsers.length > 0 ? (
                      <div className="py-1">
                        {filteredUsers.map((user) => (
                          <div
                            key={user.id}
                            className="flex items-center gap-3 px-4 py-2 hover:bg-gray-50 cursor-pointer"
                            onClick={() => handleUserSelect(user)}
                          >
                            <Avatar className="h-8 w-8">
                              <img
                                src={user.avatar_url || `https://ui-avatars.com/api/?name=${user.username}`}
                                alt={user.username}
                                className="object-cover"
                              />
                            </Avatar>
                            <span className="text-sm font-medium">{user.username}</span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-4 text-center text-sm text-muted-foreground">
                        No users found
                      </div>
                    )}
                  </ScrollArea>
                </div>
              )}
            </div>
          </div>

          {/* File Preview */}
          {selectedFiles.length > 0 && (
            <div className="border rounded-lg p-3 bg-gray-50 flex-shrink-0">
              <p className="text-xs text-muted-foreground mb-2">Attachments ({selectedFiles.length})</p>
              <div className="flex flex-wrap gap-2 max-h-24 overflow-y-auto">
                {selectedFiles.map((file, index) => {
                  const attachment = {
                    id: `preview-${index}`,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    url: URL.createObjectURL(file)
                  };
                  
                  return (
                    <div key={index} className="relative w-16 h-16 flex-shrink-0">
                      <FileAttachment
                        attachment={attachment}
                        onRemove={() => removeSelectedFile(index)}
                        showRemove={true}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Message Input */}
          <div className="space-y-2 flex-1 flex flex-col">
            <label className="text-sm font-medium">Message:</label>
            <div className="flex-1 bg-gray-50 rounded-2xl border border-gray-200 focus-within:border-forest focus-within:ring-1 focus-within:ring-forest/20 transition-all p-4">
              <textarea
                placeholder="Type your message..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="w-full h-full bg-transparent border-none outline-none text-sm placeholder:text-gray-400 resize-none min-h-[120px]"
                disabled={!selectedUser || uploadingFiles}
              />
            </div>
          </div>

          {/* Input Area with Attachments */}
          <div className="flex-shrink-0">
            <input
              ref={fileInputRef}
              type="file"
              multiple
              onChange={handleFileSelect}
              className="hidden"
              accept=".pdf,.doc,.docx,.txt"
            />
            <input
              ref={imageInputRef}
              type="file"
              multiple
              onChange={handleImageSelect}
              className="hidden"
              accept="image/*"
            />
            
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => fileInputRef.current?.click()}
                disabled={uploadingFiles || !selectedUser}
                className="flex-shrink-0 h-10 w-10 rounded-full hover:bg-gray-200 text-gray-500"
              >
                <Paperclip className="h-5 w-5" />
              </Button>
              
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => imageInputRef.current?.click()}
                disabled={uploadingFiles || !selectedUser}
                className="flex-shrink-0 h-10 w-10 rounded-full hover:bg-gray-200 text-gray-500"
              >
                <Image className="h-5 w-5" />
              </Button>

              <Button
                onClick={handleSendMessage}
                disabled={!selectedUser || (!message.trim() && selectedFiles.length === 0) || sending || uploadingFiles}
                className="bg-forest hover:bg-forest/90 rounded-full px-6 py-2 flex items-center gap-2 ml-auto"
              >
                <Send className="h-4 w-4" />
                {sending ? "Sending..." : uploadingFiles ? "Uploading..." : "Send Message"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewMessageDialog;
