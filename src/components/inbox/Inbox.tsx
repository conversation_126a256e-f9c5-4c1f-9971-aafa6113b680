import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Mail, Search, ArrowLeft, Users, Plus } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import ConversationList from "./ConversationList";
import MessageThread from "./MessageThread";
import NewMessageDialog from "./NewMessageDialog";
import CreateGroupChatDialog from "./CreateGroupChatDialog";
import GroupMessageThread from "./GroupMessageThread";
import { useToast } from "@/components/ui/use-toast";
import { useNavigate } from "react-router-dom";

interface InboxProps {
  initialMessageUserId?: string | null;
}

interface Conversation {
  id: string;
  username: string;
  avatar_url: string | null;
  lastMessage: string;
  timestamp: string;
  unread: number;
}

interface GroupChat {
  id: string;
  name: string;
  lastMessage: string;
  timestamp: string;
  unread: number;
  memberCount: number;
}

const Inbox: React.FC<InboxProps> = ({ initialMessageUserId = null }) => {
  const { user, session } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [activeConversation, setActiveConversation] = useState<string | null>(null);
  const [activeGroupChat, setActiveGroupChat] = useState<string | null>(null);
  const [isComposing, setIsComposing] = useState(false);
  const [isCreatingGroup, setIsCreatingGroup] = useState(false);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [groupChats, setGroupChats] = useState<GroupChat[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [newMessageTargetUser, setNewMessageTargetUser] = useState<{id: string, username: string, avatar_url: string | null} | null>(null);
  const [activeTab, setActiveTab] = useState("direct");

  // Memoize filtered results to prevent unnecessary re-renders
  const filteredConversations = useMemo(() => 
    conversations.filter(conversation => 
      conversation.username.toLowerCase().includes(searchQuery.toLowerCase())
    ), [conversations, searchQuery]
  );

  const filteredGroupChats = useMemo(() => 
    groupChats.filter(group => 
      group.name.toLowerCase().includes(searchQuery.toLowerCase())
    ), [groupChats, searchQuery]
  );

  // Effect to handle initialMessageUserId if provided
  useEffect(() => {
    if (initialMessageUserId && user) {
      fetchUserInfo(initialMessageUserId);
    }
  }, [initialMessageUserId, user]);

  // Fetch user info for the target user
  const fetchUserInfo = useCallback(async (targetUserId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, avatar_url')
        .eq('id', targetUserId)
        .single();

      if (error) {
        console.error("Error fetching user info:", error);
        return;
      }

      if (data) {
        setNewMessageTargetUser({
          id: data.id,
          username: data.username || "User",
          avatar_url: data.avatar_url
        });
        
        setIsComposing(true);
        setActiveConversation(null);
        setActiveGroupChat(null);
      }
    } catch (error) {
      console.error("Error in fetchUserInfo:", error);
    }
  }, []);

  const fetchConversations = useCallback(async () => {
    if (!user || !session) {
      console.log('⚠️ No user or session available');
      setLoading(false);
      return;
    }
    
    try {
      console.log('🔄 Fetching conversations for user:', user.id);
      
      // First, get recent conversation users with proper session handling
      const { data: conversationUsers, error: usersError } = await supabase.functions.invoke('get_recent_conversation_users', {
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        }
      });
      
      if (usersError) {
        console.error('❌ Error fetching conversation users:', usersError);
        // Try to refresh session and retry once
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
        if (refreshError) {
          console.error('❌ Session refresh failed:', refreshError);
          setConversations([]);
          setLoading(false);
          return;
        }
        
        // Retry with refreshed session
        const { data: retryUsers, error: retryError } = await supabase.functions.invoke('get_recent_conversation_users', {
          headers: {
            Authorization: `Bearer ${refreshData.session?.access_token}`,
          }
        });
        
        if (retryError) {
          console.error('❌ Retry failed:', retryError);
          setConversations([]);
          setLoading(false);
          return;
        }
        
        console.log('✅ Retry successful, using refreshed data');
        // Use the retry data
      }
      
      const finalUsers = conversationUsers;
      console.log('👥 Found conversation users:', finalUsers);
      
      if (!finalUsers || finalUsers.length === 0) {
        console.log('📭 No conversation users found');
        setConversations([]);
        setLoading(false);
        return;
      }
      
      // Fetch profiles for all conversation partners
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, username, avatar_url')
        .in('id', finalUsers);
        
      if (profilesError) {
        console.error('❌ Error fetching profiles:', profilesError);
        setConversations([]);
        setLoading(false);
        return;
      }
      
      console.log('👤 Fetched profiles:', profiles?.length || 0, profiles);
      
      // Get the most recent message for each conversation
      const conversationsArray = [];
      
      for (const partnerId of finalUsers) {
        const profile = profiles?.find(p => p.id === partnerId);
        if (!profile) continue;
        
        try {
          // Get the most recent message for this conversation with proper authentication
          const { data: recentMessages, error: messageError } = await supabase.functions.invoke('get_conversation_messages', {
            body: { 
              user1_id: user.id, 
              user2_id: partnerId 
            },
            headers: {
              Authorization: `Bearer ${session.access_token}`,
            }
          });
          
          if (messageError) {
            console.error('❌ Error fetching messages for', partnerId, ':', messageError);
            continue;
          }
          
          if (recentMessages && recentMessages.length > 0) {
            const lastMessage = recentMessages[recentMessages.length - 1];
            const unreadCount = recentMessages.filter(
              (msg: any) => msg.receiver_id === user.id && !msg.read
            ).length;
            
            conversationsArray.push({
              id: partnerId,
              username: profile.username || 'Unknown User',
              avatar_url: profile.avatar_url,
              lastMessage: lastMessage.content || '',
              timestamp: lastMessage.created_at,
              unread: unreadCount
            });
          }
        } catch (error) {
          console.error('❌ Error processing conversation for', partnerId, ':', error);
        }
      }
      
      // Sort by most recent message
      conversationsArray.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      console.log('✅ Conversations processed:', conversationsArray.length, conversationsArray);
      setConversations(conversationsArray);
      
    } catch (error) {
      console.error("❌ Error in fetchConversations:", error);
      setConversations([]);
      toast({
        title: "Error loading conversations",
        description: "Please try refreshing the page.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [user, session, toast]);

  const fetchGroupChats = useCallback(async () => {
    if (!user) return;
    
    try {
      console.log('🔄 Fetching group chats for user:', user.id);
      
      // Fetch group chats the user is a member of
      const { data: groupChatsData, error: groupChatsError } = await supabase
        .from('group_chats')
        .select(`
          id,
          name,
          description,
          created_at,
          group_chat_members!inner(user_id)
        `)
        .eq('group_chat_members.user_id', user.id);

      if (groupChatsError) {
        console.error('❌ Error fetching group chats:', groupChatsError);
        setGroupChats([]);
        return;
      }

      console.log('✅ Group chats fetched:', groupChatsData?.length || 0);

      if (!groupChatsData || groupChatsData.length === 0) {
        setGroupChats([]);
        return;
      }

      // Get the most recent message and member count for each group chat
      const groupChatsArray = [];
      
      for (const group of groupChatsData) {
        try {
          // Get the most recent message for this group
          const { data: recentMessages, error: messageError } = await supabase
            .from('messages')
            .select('content, created_at')
            .eq('group_chat_id', group.id)
            .eq('message_type', 'group')
            .order('created_at', { ascending: false })
            .limit(1);

          if (messageError) {
            console.error('❌ Error fetching group messages for', group.id, ':', messageError);
            continue;
          }

          // Get member count
          const { count: memberCount, error: countError } = await supabase
            .from('group_chat_members')
            .select('*', { count: 'exact', head: true })
            .eq('group_chat_id', group.id);

          if (countError) {
            console.error('❌ Error fetching member count for', group.id, ':', countError);
            continue;
          }

          const lastMessage = recentMessages?.[0];
          
          groupChatsArray.push({
            id: group.id,
            name: group.name,
            lastMessage: lastMessage?.content || 'No messages yet',
            timestamp: lastMessage?.created_at || group.created_at,
            unread: 0, // TODO: Implement unread count for group messages
            memberCount: memberCount || 0
          });
        } catch (error) {
          console.error('❌ Error processing group chat for', group.id, ':', error);
        }
      }
      
      // Sort by most recent activity
      groupChatsArray.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      console.log('✅ Group chats processed:', groupChatsArray.length, groupChatsArray);
      setGroupChats(groupChatsArray);
      
    } catch (error) {
      console.error("❌ Error fetching group chats:", error);
      setGroupChats([]);
    }
  }, [user]);

  useEffect(() => {
    if (!user || !session) {
      setLoading(false);
      return;
    }
    
    console.log('🚀 Initial data fetch for user:', user.id);
    
    // Fetch conversations first
    fetchConversations();
    
    // Skip group chats for now to avoid RLS recursion issues
    // fetchGroupChats();

    const messagesChannel = supabase
      .channel('inbox_messages')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'messages',
        filter: `or(sender_id.eq.${user.id},receiver_id.eq.${user.id})`
      }, (payload) => {
        console.log("📨 Real-time message event in inbox:", payload.eventType, payload);
        
        // Immediately refresh conversations when a new message is sent or received
        fetchConversations();
        // Skip group chats refresh for now
      })
      .subscribe();
      
    return () => {
      supabase.removeChannel(messagesChannel);
    };
  }, [user, session, fetchConversations, fetchGroupChats]);

  const handleStartConversation = useCallback((userId: string) => {
    setActiveConversation(userId);
    setActiveGroupChat(null);
    setIsComposing(false);
  }, []);

  const handleStartGroupChat = useCallback((groupId: string) => {
    setActiveGroupChat(groupId);
    setActiveConversation(null);
    setIsComposing(false);
  }, []);

  const handleComposeMessage = useCallback(() => {
    setActiveConversation(null);
    setActiveGroupChat(null);
    setIsComposing(true);
  }, []);

  const handleCreateGroup = useCallback(() => {
    setActiveConversation(null);
    setActiveGroupChat(null);
    setIsComposing(false);
    setIsCreatingGroup(true);
  }, []);

  const handleCloseCompose = useCallback(() => {
    setIsComposing(false);
    setNewMessageTargetUser(null);
  }, []);

  const handleBackToList = useCallback(() => {
    setActiveConversation(null);
    setActiveGroupChat(null);
    setIsComposing(false);
    setNewMessageTargetUser(null);
  }, []);

  const refreshData = useCallback(() => {
    fetchConversations();
    fetchGroupChats();
  }, [fetchConversations, fetchGroupChats]);

  const isMobileView = () => window.innerWidth < 768;

  useEffect(() => {
    if (user && session) {
      setLoading(false);
    }
  }, [user, session]);

  return (
    <div className="h-full bg-background">
      <div className="px-6 pt-0 pb-1"> 
        <h1 className="text-3xl font-bold mb-1">Messages</h1>
        
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="grid md:grid-cols-3 h-[calc(100vh-96px)]">
            {/* Conversations List */}
            <div className={`border-r ${(activeConversation || activeGroupChat || isComposing) && isMobileView() ? 'hidden' : 'block'} md:block`}>
              <div className="p-4 border-b flex items-center justify-between bg-gray-50">
                <div className="flex items-center gap-2 md:hidden">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-9 w-9 p-0" 
                    onClick={() => navigate('/profile')}
                  >
                    <ArrowLeft className="h-5 w-5" />
                  </Button>
                </div>
                
                <div className="flex items-center gap-2 flex-1">
                  <Search className="text-muted-foreground w-5 h-5" />
                  <Input 
                    placeholder="Search messages..." 
                    className="border-none shadow-none focus-visible:ring-0 text-sm h-9 bg-transparent"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex gap-1">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-9 w-9 p-0" 
                    onClick={handleComposeMessage}
                    title="New Message"
                  >
                    <Mail className="h-5 w-5" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-9 w-9 p-0" 
                    onClick={handleCreateGroup}
                    title="Create Group"
                  >
                    <Users className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              <Tabs value={activeTab} onValueChange={setActiveTab} className="h-[calc(100%-65px)]">
                <TabsList className="grid w-full grid-cols-2 px-4 pt-2">
                  <TabsTrigger value="direct" className="text-sm">Direct</TabsTrigger>
                  <TabsTrigger value="groups" className="text-sm">Groups</TabsTrigger>
                </TabsList>
                
                <TabsContent value="direct" className="h-full mt-0">
                  <ScrollArea className="h-full">
                    {loading ? (
                      <div className="p-6 text-center">
                        <p className="text-muted-foreground">Loading conversations...</p>
                      </div>
                    ) : filteredConversations.length > 0 ? (
                      <ConversationList 
                        conversations={filteredConversations}
                        activeConversationId={activeConversation}
                        onSelectConversation={handleStartConversation}
                        onConversationDeleted={refreshData}
                      />
                    ) : (
                      <div className="p-8 text-center">
                        <Mail className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-xl font-semibold mb-2">No direct messages</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          {searchQuery ? 'No conversations found matching your search' : 'Start a conversation with a fellow hunter'}
                        </p>
                        {!searchQuery && (
                          <Button onClick={handleComposeMessage}>
                            New Message
                          </Button>
                        )}
                      </div>
                    )}
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="groups" className="h-full mt-0">
                  <ScrollArea className="h-full">
                    <div className="p-8 text-center">
                      <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-xl font-semibold mb-2">Group chats temporarily unavailable</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        We're working on fixing group chat functionality. Please check back later.
                      </p>
                    </div>
                  </ScrollArea>
                </TabsContent>
              </Tabs>
            </div>
            
            {/* Message Thread */}
            <div className={`col-span-2 ${(activeConversation || activeGroupChat || isComposing) || !isMobileView() ? 'block' : 'hidden'} md:block h-full overflow-hidden`}>
              {activeConversation ? (
                <MessageThread 
                  conversationId={activeConversation}
                  onBack={handleBackToList}
                  onRefreshList={refreshData}
                />
              ) : activeGroupChat ? (
                <GroupMessageThread 
                  groupChatId={activeGroupChat}
                  onBack={handleBackToList}
                  onRefreshList={refreshData}
                />
              ) : isComposing ? (
                <NewMessageDialog 
                  onBack={handleBackToList}
                  onMessageSent={refreshData}
                  initialTargetUser={newMessageTargetUser}
                />
              ) : (
                <div className="h-full flex items-center justify-center">
                  <div className="text-center p-8">
                    <Mail className="h-20 w-20 text-muted-foreground mx-auto mb-6" />
                    <h3 className="text-2xl font-semibold mb-3">Select a conversation</h3>
                    <p className="text-muted-foreground mb-6 text-lg">Choose a conversation from the list or start a new one</p>
                    <div className="flex gap-2 justify-center">
                      <Button onClick={handleComposeMessage} size="lg">
                        Compose Message
                      </Button>
                      <Button onClick={handleCreateGroup} variant="outline" size="lg">
                        <Users className="h-4 w-4 mr-2" />
                        Create Group
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <CreateGroupChatDialog
        open={isCreatingGroup}
        onOpenChange={setIsCreatingGroup}
        onGroupCreated={() => {
          fetchGroupChats();
          setIsCreatingGroup(false);
        }}
      />
    </div>
  );
};

export default Inbox;
