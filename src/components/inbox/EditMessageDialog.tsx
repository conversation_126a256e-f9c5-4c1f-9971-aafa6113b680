
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";

interface EditMessageDialogProps {
  messageId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onMessageUpdated: () => void;
}

const EditMessageDialog: React.FC<EditMessageDialogProps> = ({ 
  messageId,
  open,
  onOpenChange,
  onMessageUpdated 
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [content, setContent] = useState("");
  const [originalContent, setO<PERSON>inalContent] = useState("");
  const [loading, setLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);

  // Fetch message on open
  useEffect(() => {
    const fetchMessage = async () => {
      if (!open || !messageId || !user) return;
      
      setIsFetching(true);
      
      try {
        const { data, error } = await supabase
          .from('messages')
          .select('*')
          .eq('id', messageId)
          .eq('sender_id', user.id)
          .single();
          
        if (error) throw error;
        
        if (data) {
          setContent(data.content || "");
          setOriginalContent(data.content || "");
        }
      } catch (error) {
        console.error("Error fetching message:", error);
        toast({
          title: "Error",
          description: "Could not load message content",
          variant: "destructive",
        });
        onOpenChange(false);
      } finally {
        setIsFetching(false);
      }
    };
    
    fetchMessage();
  }, [messageId, open, user, toast, onOpenChange]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !messageId || !content.trim()) return;
    
    // Don't update if content hasn't changed
    if (content.trim() === originalContent.trim()) {
      onOpenChange(false);
      return;
    }
    
    setLoading(true);
    
    try {
      const { error } = await supabase
        .from('messages')
        .update({
          content: content.trim(),
          edited: true,
          edited_at: new Date().toISOString()
        })
        .eq('id', messageId)
        .eq('sender_id', user.id);
        
      if (error) throw error;
      
      toast({
        description: "Message updated successfully",
      });
      
      onMessageUpdated();
      onOpenChange(false);
    } catch (error) {
      console.error("Error updating message:", error);
      toast({
        title: "Error",
        description: "Could not update message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Message</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <Textarea
            placeholder="Message content"
            className="min-h-[100px]"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            disabled={loading || isFetching}
          />
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="ghost" 
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!content.trim() || content.trim() === originalContent.trim() || loading || isFetching}
            >
              {loading ? "Saving..." : "Save changes"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditMessageDialog;
