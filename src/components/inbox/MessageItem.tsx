
import React, { useState, memo, useCallback } from "react";
import { Avatar } from "@/components/ui/avatar";
import { format } from "date-fns";
import { useAuth } from "@/contexts/AuthContext";
import EditMessageDialog from "./EditMessageDialog";
import FileAttachment from "./FileAttachment";
import MessageStatus from "./MessageStatus";
import MessageReactions from "./MessageReactions";
import { Message } from "./types";

interface MessageItemProps {
  message: Message;
  senderUsername?: string;
  senderAvatar?: string | null;
  onReply: (message: Message) => void;
  onMessageUpdated: () => void;
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  senderUsername = "User",
  senderAvatar = null,
  onReply,
  onMessageUpdated
}) => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [attachments, setAttachments] = useState(message.attachments || []);

  // Check if the current user is the sender of this message
  const isSender = user && user.id === message.sender_id;

  const formatMessageDate = (timestamp: string) => {
    const messageDate = new Date(timestamp);
    const now = new Date();
    
    const isToday = messageDate.toDateString() === now.toDateString();
    
    if (isToday) {
      return format(messageDate, "h:mm a");
    } else {
      return format(messageDate, "MMM d, h:mm a");
    }
  };

  return (
    <div className={`flex flex-col mb-4 ${isSender ? 'items-end' : 'items-start'} group`}>
      <div className={`flex ${isSender ? 'flex-row-reverse' : 'flex-row'} gap-2 max-w-[85%]`}>
        {/* Avatar - only show for received messages */}
        {!isSender && (
          <Avatar className="h-8 w-8 flex-shrink-0 ring-2 ring-amber-200/50">
            <img 
              src={senderAvatar || `https://ui-avatars.com/api/?name=${senderUsername}`}
              alt={senderUsername}
              className="object-cover"
            />
          </Avatar>
        )}
        
        <div className={`flex flex-col ${isSender ? 'items-end' : 'items-start'}`}>
          {/* Message container */}
          <div 
            className={`
              relative rounded-2xl px-4 py-3 text-sm shadow-sm
              ${isSender 
                ? 'bg-gradient-to-r from-amber-600 to-amber-700 text-white shadow-amber-600/20' 
                : 'bg-gradient-to-r from-stone-100 to-amber-50 text-stone-800 border border-amber-200/50'}
              ${message.reply_to_message_id ? 'mt-2' : ''}
            `}
          >
            {/* Sender name for non-sender messages */}
            {!isSender && (
              <div className="text-xs font-medium text-amber-700 mb-1">
                {senderUsername}
              </div>
            )}
            
            {/* Message content */}
            <div className="whitespace-pre-wrap break-words">
              {message.content}
            </div>
            
            {/* Attachments */}
            {attachments && attachments.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {attachments.map((attachment) => (
                  <FileAttachment
                    key={attachment.id}
                    attachment={attachment}
                  />
                ))}
              </div>
            )}
            
            {/* Edited indicator */}
            {message.edited && (
              <span className={`text-xs opacity-70 ml-1 ${isSender ? 'text-amber-100' : 'text-amber-600'}`}>(edited)</span>
            )}
          </div>
          
          {/* Message metadata */}
          <div className={`flex items-center gap-1 mt-1 text-xs text-amber-700`}>
            <div>{formatMessageDate(message.created_at)}</div>
            {isSender && <MessageStatus read={message.read} />}
          </div>
          
          {/* Reactions */}
          <MessageReactions 
            messageId={message.id}
            reactions={[]}
            onReactionChange={onMessageUpdated}
          />
        </div>
      </div>
      
      {/* Edit message dialog */}
      <EditMessageDialog
        open={isEditing}
        onOpenChange={setIsEditing}
        messageId={message.id}
        onMessageUpdated={onMessageUpdated}
      />
    </div>
  );
};

// Use React.memo with custom comparison to prevent unnecessary re-renders
export default memo(MessageItem, (prevProps, nextProps) => {
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.message.content === nextProps.message.content &&
    prevProps.message.read === nextProps.message.read &&
    prevProps.message.edited === nextProps.message.edited &&
    prevProps.senderUsername === nextProps.senderUsername &&
    prevProps.senderAvatar === nextProps.senderAvatar
  );
});
