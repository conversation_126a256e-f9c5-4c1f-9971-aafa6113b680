import React from "react";
import { format, formatDistanceToNow } from "date-fns";
import { Avatar } from "@/components/ui/avatar";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Trash2, MoreHorizontal } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/components/ui/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";

interface Conversation {
  id: string;
  username: string;
  avatar_url: string | null;
  lastMessage: string;
  timestamp: string;
  unread: number;
}

interface ConversationListProps {
  conversations: Conversation[];
  activeConversationId: string | null;
  onSelectConversation: (id: string) => void;
  onConversationDeleted: () => void;
}

const ConversationList: React.FC<ConversationListProps> = ({ 
  conversations, 
  activeConversationId,
  onSelectConversation,
  onConversationDeleted
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [conversationToDelete, setConversationToDelete] = React.useState<Conversation | null>(null);
  const [localConversations, setLocalConversations] = React.useState<Conversation[]>(conversations);

  // Update local conversations when prop changes
  React.useEffect(() => {
    setLocalConversations(conversations);
  }, [conversations]);

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    
    // If message is from today, show time
    if (date.toDateString() === now.toDateString()) {
      return format(date, "h:mm a");
    }
    
    // If message is from this week, show day
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (diffDays < 7) {
      return format(date, "EEE");
    }
    
    // Otherwise show date
    return format(date, "MMM d");
  };

  const handleDeleteConversation = async () => {
    if (!conversationToDelete || !user) return;

    try {
      console.log('Deleting conversation with user:', conversationToDelete.id);
      
      const { data: session } = await supabase.auth.getSession();
      
      if (!session.session?.access_token) {
        throw new Error('No valid session found');
      }

      // Use the Supabase function to handle message deletion
      const { data, error } = await supabase.functions.invoke('delete-conversation', {
        body: {
          user1_id: user.id,
          user2_id: conversationToDelete.id,
        },
      });

      if (error) {
        console.error('Function error:', error);
        throw error;
      }

      toast({
        title: "Conversation deleted",
        description: `Conversation with ${conversationToDelete.username} has been deleted.`,
      });

      // Close dialog and refresh conversations
      setDeleteDialogOpen(false);
      setConversationToDelete(null);
      onConversationDeleted();
    } catch (error) {
      console.error("Error deleting conversation:", error);
      toast({
        title: "Error deleting conversation",
        description: "Please try again later.",
        variant: "destructive",
      });
    }
  };

  const openDeleteDialog = (conversation: Conversation) => {
    setConversationToDelete(conversation);
    setDeleteDialogOpen(true);
  };

  const handleConversationClick = async (conversationId: string) => {
    console.log('🔄 Conversation clicked:', conversationId);
    
    // Mark messages as read when conversation is clicked
    if (user) {
      try {
        console.log('📖 Marking messages as read for conversation:', conversationId);
        await supabase
          .from('messages')
          .update({ read: true })
          .eq('sender_id', conversationId)
          .eq('receiver_id', user.id)
          .eq('message_type', 'direct')
          .is('group_chat_id', null)
          .eq('read', false);
        
        console.log('✅ Messages marked as read');
        
        // Refresh the conversation list to update unread counts
        onConversationDeleted();
      } catch (error) {
        console.warn("Non-critical error marking messages as read:", error);
      }
    }
    
    onSelectConversation(conversationId);
  };

  const ConversationItem = ({ conversation }: { conversation: Conversation }) => {
    console.log('🔍 Rendering conversation:', conversation.id, 'unread:', conversation.unread);
    
    return (
      <div 
        className={`p-3 flex items-center gap-3 cursor-pointer hover:bg-gray-50 transition-colors ${
          activeConversationId === conversation.id ? "bg-forest/5" : ""
        }`}
        onClick={() => handleConversationClick(conversation.id)}
      >
        <Avatar className="h-10 w-10 flex-shrink-0">
          <img 
            src={conversation.avatar_url || `https://ui-avatars.com/api/?name=${conversation.username}`} 
            alt={conversation.username}
            className="object-cover"
          />
        </Avatar>
        
        <div className="flex-1 min-w-0 overflow-hidden">
          <div className="flex items-center justify-between gap-2 mb-1">
            <h4 className="font-semibold text-sm truncate">
              {conversation.username}
            </h4>
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="text-xs text-muted-foreground whitespace-nowrap">
                {formatMessageTime(conversation.timestamp)}
              </span>
              {isMobile && (
                <DropdownMenu>
                  <DropdownMenuTrigger
                    onClick={(e) => e.stopPropagation()}
                    className="p-1 hover:bg-gray-200 rounded flex-shrink-0"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem
                      className="text-red-600 focus:text-red-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        openDeleteDialog(conversation);
                      }}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete conversation
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <p className="text-sm flex-1 break-words text-muted-foreground">
              {conversation.lastMessage}
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="divide-y">
        {localConversations.map((conversation) => (
          isMobile ? (
            <ConversationItem key={conversation.id} conversation={conversation} />
          ) : (
            <ContextMenu key={conversation.id}>
              <ContextMenuTrigger asChild>
                <ConversationItem conversation={conversation} />
              </ContextMenuTrigger>
              <ContextMenuContent>
                <ContextMenuItem
                  className="text-red-600 focus:text-red-600"
                  onClick={() => openDeleteDialog(conversation)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete conversation
                </ContextMenuItem>
              </ContextMenuContent>
            </ContextMenu>
          )
        ))}
      </div>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete conversation</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete your conversation with {conversationToDelete?.username}? 
              This action cannot be undone and will permanently delete all messages between you two.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConversation}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ConversationList;
