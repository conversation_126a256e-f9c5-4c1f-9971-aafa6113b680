
import React, { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface TypingIndicatorProps {
  conversationId: string;
  isGroupChat?: boolean;
}

interface TypingUser {
  user_id: string;
  username: string;
  last_typed: string;
}

interface TypingIndicatorRef {
  sendTypingEvent: (username: string) => Promise<void>;
}

const TypingIndicator = forwardRef<TypingIndicatorRef, TypingIndicatorProps>(({
  conversationId,
  isGroupChat = false,
}, ref) => {
  const { user } = useAuth();
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);

  const sendTypingEvent = async (username: string) => {
    if (!user) return;

    const channelName = isGroupChat 
      ? `typing_group_${conversationId}`
      : `typing_direct_${[user.id, conversationId].sort().join('_')}`;

    const channel = supabase.channel(channelName);
    
    await channel.track({
      user_id: user.id,
      username: username,
      last_typed: new Date().toISOString(),
    });
  };

  useImperativeHandle(ref, () => ({
    sendTypingEvent,
  }), [sendTypingEvent]);

  useEffect(() => {
    if (!user) return;

    const channelName = isGroupChat 
      ? `typing_group_${conversationId}`
      : `typing_direct_${[user.id, conversationId].sort().join('_')}`;

    const channel = supabase.channel(channelName);

    // Listen for typing events
    channel.on('presence', { event: 'sync' }, () => {
      const presenceState = channel.presenceState();
      const typing = Object.values(presenceState)
        .flat()
        .filter((presence: any) => presence.user_id !== user.id)
        .map((presence: any) => ({
          user_id: presence.user_id,
          username: presence.username,
          last_typed: presence.last_typed,
        }));

      // Filter out users who stopped typing (last typed > 3 seconds ago)
      const recentTyping = typing.filter((t) => {
        const lastTyped = new Date(t.last_typed);
        const now = new Date();
        return now.getTime() - lastTyped.getTime() < 3000;
      });

      setTypingUsers(recentTyping);
    });

    channel.subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, conversationId, isGroupChat]);

  if (typingUsers.length === 0) return null;

  const getTypingText = () => {
    if (typingUsers.length === 1) {
      return `${typingUsers[0].username} is typing...`;
    } else if (typingUsers.length === 2) {
      return `${typingUsers[0].username} and ${typingUsers[1].username} are typing...`;
    } else {
      return `${typingUsers.length} people are typing...`;
    }
  };

  return (
    <div className="px-4 py-2 text-sm text-muted-foreground">
      <div className="flex items-center gap-2">
        <div className="flex gap-1">
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
        </div>
        <span>{getTypingText()}</span>
      </div>
    </div>
  );
});

TypingIndicator.displayName = "TypingIndicator";

export default TypingIndicator;
