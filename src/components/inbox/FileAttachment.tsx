
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Download, FileText, Image, X } from "lucide-react";

interface FileAttachmentProps {
  attachment: {
    id: string;
    name: string;
    size: number;
    type: string;
    url: string;
  };
  onRemove?: () => void;
  showRemove?: boolean;
}

const FileAttachment: React.FC<FileAttachmentProps> = ({ 
  attachment, 
  onRemove, 
  showRemove = false 
}) => {
  const [imageError, setImageError] = useState(false);
  
  // More comprehensive image detection
  const isImage = attachment.type.startsWith('image/') || 
                  attachment.name.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i) ||
                  attachment.url.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i);
  
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleImageClick = () => {
    window.open(attachment.url, '_blank');
  };

  const handleImageError = () => {
    console.error('❌ Failed to load image:', attachment.url);
    setImageError(true);
  };

  if (isImage && !imageError) {
    return (
      <div className="relative inline-block w-full max-w-full">
        <img 
          src={attachment.url}
          alt={attachment.name}
          className="w-full max-w-full h-auto max-h-64 rounded-lg object-cover cursor-pointer border"
          onClick={handleImageClick}
          onError={handleImageError}
          crossOrigin="anonymous"
          style={{ maxWidth: '100%', height: 'auto' }}
        />
        {showRemove && (
          <Button
            variant="destructive"
            size="icon"
            className="absolute -top-2 -right-2 h-6 w-6"
            onClick={onRemove}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  }

  // Fallback for failed images or non-images
  if (isImage && imageError) {
    return (
      <div className="flex items-center gap-2 p-3 bg-gray-100 rounded-lg w-full border-2 border-dashed border-gray-300">
        <div className="flex flex-col items-center gap-2 flex-1">
          <Image className="h-8 w-8 text-gray-400" />
          <div className="text-center">
            <p className="text-sm font-medium text-gray-600">Image failed to load</p>
            <p className="text-xs text-gray-500 truncate max-w-32">{attachment.name}</p>
            <p className="text-xs text-gray-400">{formatFileSize(attachment.size)}</p>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <Button variant="ghost" size="icon" className="h-6 w-6" onClick={handleDownload}>
            <Download className="h-3 w-3" />
          </Button>
          {showRemove && (
            <Button variant="ghost" size="icon" className="h-6 w-6" onClick={onRemove}>
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2 p-2 bg-gray-100 rounded-lg w-full">
      <FileText className="h-4 w-4 text-muted-foreground" />
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">{attachment.name}</p>
        <p className="text-xs text-muted-foreground">{formatFileSize(attachment.size)}</p>
      </div>
      <Button variant="ghost" size="icon" className="h-6 w-6" onClick={handleDownload}>
        <Download className="h-3 w-3" />
      </Button>
      {showRemove && (
        <Button variant="ghost" size="icon" className="h-6 w-6" onClick={onRemove}>
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};

export default FileAttachment;
