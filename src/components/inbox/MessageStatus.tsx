
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";

interface MessageStatusProps {
  read: boolean;
}

const MessageStatus: React.FC<MessageStatusProps> = ({ read }) => {
  return (
    <div className="flex items-center ml-1">
      {read ? (
        <CheckCheck className="h-3 w-3 text-blue-500" />
      ) : (
        <Check className="h-3 w-3 text-gray-400" />
      )}
    </div>
  );
};

export default MessageStatus;
