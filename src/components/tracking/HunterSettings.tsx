
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Settings, Target, Eye, MapPin, Bell, RotateCcw } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface SettingsConfig {
  bloodSensitivity: number;
  terrainType: 'forest' | 'snow' | 'field' | 'desert';
  autoSave: boolean;
  gpsInterval: number;
  notifications: boolean;
  nightMode: boolean;
  filterIntensity: number;
}

const HunterSettings = () => {
  const [settings, setSettings] = useState<SettingsConfig>({
    bloodSensitivity: 75,
    terrainType: 'forest',
    autoSave: true,
    gpsInterval: 30,
    notifications: true,
    nightMode: false,
    filterIntensity: 50
  });

  const terrainTypes = [
    { 
      id: 'forest' as const, 
      name: 'Forest', 
      color: 'bg-green-600', 
      description: 'Dense woods, mixed lighting conditions'
    },
    { 
      id: 'snow' as const, 
      name: 'Snow', 
      color: 'bg-blue-200', 
      description: 'High contrast, bright snowy terrain'
    },
    { 
      id: 'field' as const, 
      name: 'Field', 
      color: 'bg-yellow-600', 
      description: 'Open terrain, grass and dirt surfaces'
    },
    { 
      id: 'desert' as const, 
      name: 'Desert', 
      color: 'bg-orange-600', 
      description: 'Sandy terrain, minimal vegetation'
    }
  ];

  const updateSetting = <K extends keyof SettingsConfig>(
    key: K, 
    value: SettingsConfig[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    toast({
      title: "Setting Updated",
      description: `${key} has been changed`
    });
  };

  const resetToDefaults = () => {
    setSettings({
      bloodSensitivity: 75,
      terrainType: 'forest',
      autoSave: true,
      gpsInterval: 30,
      notifications: true,
      nightMode: false,
      filterIntensity: 50
    });
    toast({
      title: "Settings Reset",
      description: "All settings restored to defaults"
    });
  };

  return (
    <div className="h-full bg-slate-900 text-white flex flex-col">
      {/* Header */}
      <div className="p-4 bg-slate-800 border-b border-slate-700">
        <div className="flex items-center gap-2">
          <Settings className="w-6 h-6 text-cyan-400" />
          <h1 className="text-xl font-bold">Hunter Settings</h1>
        </div>
      </div>

      {/* Settings Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        
        {/* Detection Settings */}
        <div className="bg-slate-800 p-4 rounded-lg border border-slate-700">
          <div className="flex items-center gap-2 mb-4">
            <Target className="w-5 h-5 text-cyan-400" />
            <h2 className="text-lg font-semibold">Detection Settings</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium">Blood Detection Sensitivity</label>
                <Badge variant="outline" className="text-cyan-400 border-cyan-400">
                  {settings.bloodSensitivity}%
                </Badge>
              </div>
              <Slider
                value={[settings.bloodSensitivity]}
                onValueChange={(value) => updateSetting('bloodSensitivity', value[0])}
                max={100}
                min={10}
                step={5}
                className="w-full"
              />
              <p className="text-xs text-slate-400 mt-1">
                Higher values detect more potential blood spots but may increase false positives
              </p>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium">Filter Intensity</label>
                <Badge variant="outline" className="text-cyan-400 border-cyan-400">
                  {settings.filterIntensity}%
                </Badge>
              </div>
              <Slider
                value={[settings.filterIntensity]}
                onValueChange={(value) => updateSetting('filterIntensity', value[0])}
                max={100}
                min={0}
                step={10}
                className="w-full"
              />
              <p className="text-xs text-slate-400 mt-1">
                Adjusts the intensity of visual filters for better detection
              </p>
            </div>
          </div>
        </div>

        {/* Terrain Optimization */}
        <div className="bg-slate-800 p-4 rounded-lg border border-slate-700">
          <div className="flex items-center gap-2 mb-4">
            <Eye className="w-5 h-5 text-cyan-400" />
            <h2 className="text-lg font-semibold">Terrain Optimization</h2>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {terrainTypes.map((terrain) => (
              <Button
                key={terrain.id}
                onClick={() => updateSetting('terrainType', terrain.id)}
                className={`h-auto p-4 flex flex-col items-center justify-center min-h-[100px] ${
                  settings.terrainType === terrain.id
                    ? 'bg-cyan-600 border-cyan-500 text-white'
                    : 'bg-slate-700 border-slate-600 text-slate-300 hover:bg-slate-600'
                } border`}
              >
                <div className={`w-6 h-6 rounded-full ${terrain.color} mb-2`} />
                <span className="font-medium text-sm mb-1">{terrain.name}</span>
                <span className="text-xs text-center opacity-75 leading-tight">
                  {terrain.description}
                </span>
              </Button>
            ))}
          </div>
        </div>

        {/* GPS & Tracking Settings */}
        <div className="bg-slate-800 p-4 rounded-lg border border-slate-700">
          <div className="flex items-center gap-2 mb-4">
            <MapPin className="w-5 h-5 text-cyan-400" />
            <h2 className="text-lg font-semibold">GPS & Tracking</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium">GPS Update Interval</label>
                <Badge variant="outline" className="text-cyan-400 border-cyan-400">
                  {settings.gpsInterval}s
                </Badge>
              </div>
              <Slider
                value={[settings.gpsInterval]}
                onValueChange={(value) => updateSetting('gpsInterval', value[0])}
                max={300}
                min={5}
                step={5}
                className="w-full"
              />
              <p className="text-xs text-slate-400 mt-1">
                Shorter intervals use more battery but provide more precise tracking
              </p>
            </div>

            <div className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
              <div>
                <label className="text-sm font-medium">Auto-save Trail Data</label>
                <p className="text-xs text-slate-400">Automatically save detection points</p>
              </div>
              <Switch
                checked={settings.autoSave}
                onCheckedChange={(checked) => updateSetting('autoSave', checked)}
              />
            </div>
          </div>
        </div>

        {/* App Settings */}
        <div className="bg-slate-800 p-4 rounded-lg border border-slate-700">
          <div className="flex items-center gap-2 mb-4">
            <Bell className="w-5 h-5 text-cyan-400" />
            <h2 className="text-lg font-semibold">App Settings</h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
              <div>
                <label className="text-sm font-medium">Push Notifications</label>
                <p className="text-xs text-slate-400">Get alerts for detections and updates</p>
              </div>
              <Switch
                checked={settings.notifications}
                onCheckedChange={(checked) => updateSetting('notifications', checked)}
              />
            </div>

            <div className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
              <div>
                <label className="text-sm font-medium">Night Mode</label>
                <p className="text-xs text-slate-400">Enhanced low-light visibility</p>
              </div>
              <Switch
                checked={settings.nightMode}
                onCheckedChange={(checked) => updateSetting('nightMode', checked)}
              />
            </div>
          </div>
        </div>

        {/* Current Settings Summary */}
        <div className="bg-slate-800 p-4 rounded-lg border border-slate-700">
          <h3 className="text-sm font-semibold text-cyan-400 mb-3">Current Configuration</h3>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>Sensitivity: {settings.bloodSensitivity}%</div>
            <div>Terrain: {settings.terrainType}</div>
            <div>GPS Interval: {settings.gpsInterval}s</div>
            <div>Filter: {settings.filterIntensity}%</div>
            <div>Auto-save: {settings.autoSave ? 'On' : 'Off'}</div>
            <div>Notifications: {settings.notifications ? 'On' : 'Off'}</div>
          </div>
        </div>
      </div>

      {/* Reset Button */}
      <div className="p-4 bg-slate-800 border-t border-slate-700">
        <Button 
          onClick={resetToDefaults} 
          variant="outline" 
          className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          Reset to Defaults
        </Button>
      </div>
    </div>
  );
};

export default HunterSettings;
