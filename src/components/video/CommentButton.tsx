
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MessageCircle } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

interface CommentButtonProps {
  videoId: string;
  commentCount: number;
  videoUrl: string;
  username: string;
  caption: string;
  userAvatar: string;
}

const CommentButton = ({ 
  videoId, 
  commentCount: initialCommentCount,
  videoUrl,
  username,
  caption,
  userAvatar
}: CommentButtonProps) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [commentCount, setCommentCount] = useState(initialCommentCount);

  // Update comment count when initialCommentCount prop changes
  useEffect(() => {
    setCommentCount(initialCommentCount);
  }, [initialCommentCount]);

  // Fetch the latest comment count when component mounts
  useEffect(() => {
    const fetchCommentCount = async () => {
      try {
        const { count, error } = await supabase
          .from('comments')
          .select('id', { count: 'exact', head: true })
          .eq('video_id', videoId);
          
        if (error) {
          console.error("Error fetching comment count:", error);
          return;
        }
        
        if (count !== null) {
          setCommentCount(count);
        }
      } catch (error) {
        console.error("Error fetching comment count:", error);
      }
    };

    fetchCommentCount();

    // Set up realtime subscription to listen for comment changes
    const commentsChannel = supabase
      .channel(`video-comments-${videoId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'comments',
        filter: `video_id=eq.${videoId}`
      }, () => {
        // When comments change, fetch the updated count
        fetchCommentCount();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(commentsChannel);
    };
  }, [videoId]);

  const handleComment = () => {
    if (!user) {
      toast.error("Please sign in to comment");
      navigate("/auth", { state: { returnTo: `/comments/${videoId}` } });
      return;
    }
    
    sessionStorage.setItem('currentVideoId', videoId);
    navigate(`/comments/${videoId}`, { 
      state: { 
        videoId,
        videoUrl,
        username,
        caption,
        userAvatar
      } 
    });
  };

  return (
    <div className="action-button">
      <Button 
        variant="ghost" 
        size="icon" 
        className="action-icon text-white hover:scale-110 transition-transform"
        onClick={handleComment}
      >
        <MessageCircle 
          size={28} 
          className="drop-shadow-[0_0_8px_rgba(255,255,255,0.5)]" 
        />
      </Button>
      <span className="text-white text-xs font-medium drop-shadow-md">{commentCount}</span>
    </div>
  );
};

export default CommentButton;
