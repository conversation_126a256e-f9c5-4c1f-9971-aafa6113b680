
import { useState, useEffect } from "react";
import { Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Share } from "@capacitor/share";
import { Capacitor } from "@capacitor/core";

interface ShareButtonProps {
  videoId: string;
  initialShares: number;
  username: string;
  caption: string;
}

const ShareButton = ({ videoId, initialShares, username, caption }: ShareButtonProps) => {
  const [shareCount, setShareCount] = useState(initialShares);
  const { user } = useAuth();

  // Update share count when initialShares prop changes
  useEffect(() => {
    setShareCount(initialShares);
  }, [initialShares]);

  // Fetch the latest share count when component mounts
  useEffect(() => {
    const fetchShareCount = async () => {
      try {
        const { data, error } = await supabase
          .from('videos')
          .select('shares')
          .eq('id', videoId)
          .single();
          
        if (!error && data) {
          setShareCount(data.shares || 0);
        }
      } catch (error) {
        console.error("Error fetching share count:", error);
      }
    };

    fetchShareCount();

    // Set up realtime subscription to listen for shares changes
    const sharesChannel = supabase
      .channel(`video-shares-${videoId}`)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'videos',
        filter: `id=eq.${videoId}`
      }, (payload) => {
        if (payload.new && 'shares' in payload.new) {
          setShareCount(payload.new.shares || 0);
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(sharesChannel);
    };
  }, [videoId]);

  const handleShare = async () => {
    try {
      // Create the video-specific URL
      let videoUrl = `${window.location.origin}/video/${videoId}`;
      
      if (!user) {
        if (Capacitor.isNativePlatform()) {
          videoUrl = `https://whitetaillivn.app/video/${videoId}`
          const canShare = await Share.canShare();
          if (canShare.value) {
            await Share.share({
              title: `${username}'s video`,
              text: caption,
              url: videoUrl
            });
            toast.success("Shared successfully!");
          } else {
            toast.error("Unable to share at this time");
          }
        }
        else if (navigator.share) {
          await navigator.share({
            title: `${username}'s video`,
            text: caption,
            url: videoUrl
          });
          toast.success("Shared successfully!");
        } else {
          await navigator.clipboard.writeText(videoUrl);
          toast.success("Link copied to clipboard!");
        }
        return;
      }
      
      if (Capacitor.isNativePlatform()) {
          videoUrl = `https://whitetaillivn.app/video/${videoId}`
          const canShare = await Share.canShare();
          if (canShare.value) {
            await Share.share({
              title: `${username}'s video`,
              text: caption,
              url: videoUrl
            });
            toast.success("Shared successfully!");
          } else {
            toast.error("Unable to share at this time");
          }
        } else if (navigator.share) {
        await navigator.share({
          title: `${username}'s video`,
          text: caption,
          url: videoUrl
        });
        
        const newShareCount = shareCount + 1;
        await supabase
          .from('videos')
          .update({ shares: newShareCount })
          .eq('id', videoId);
          
        setShareCount(newShareCount);
        toast.success("Shared successfully!");
      } else {
        await navigator.clipboard.writeText(videoUrl);
        
        const newShareCount = shareCount + 1;
        await supabase
          .from('videos')
          .update({ shares: newShareCount })
          .eq('id', videoId);
          
        setShareCount(newShareCount);
        toast.success("Link copied to clipboard!");
      }
    } catch (error) {
      console.error("Error sharing:", error);
      try {
        const videoUrl = `${window.location.origin}/video/${videoId}`;
        await navigator.clipboard.writeText(videoUrl);
        
        if (user) {
          const newShareCount = shareCount + 1;
          await supabase
            .from('videos')
            .update({ shares: newShareCount })
            .eq('id', videoId);
            
          setShareCount(newShareCount);
        }
        
        toast.success("Link copied to clipboard!");
      } catch (clipboardError) {
        console.error("Clipboard error:", clipboardError);
        toast.error("Couldn't share the video. Try copying the URL manually.");
      }
    }
  };

  return (
    <div className="action-button">
      <Button 
        variant="ghost" 
        size="icon" 
        className="action-icon text-white hover:scale-110 transition-transform"
        onClick={handleShare}
      >
        <Share2 
          size={28} 
          className="drop-shadow-[0_0_8px_rgba(255,255,255,0.5)]" 
        />
      </Button>
      <span className="text-white text-xs font-medium drop-shadow-md">{shareCount}</span>
    </div>
  );
};

export default ShareButton;
