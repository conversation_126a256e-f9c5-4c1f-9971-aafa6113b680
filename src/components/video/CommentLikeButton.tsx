
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Heart } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

interface CommentLikeButtonProps {
  commentId: string;
  initialLikes: number;
}

// Define proper type for the payload from Supabase
interface CommentPayload {
  new: {
    id: string;
    like_count?: number;
    [key: string]: any;
  };
  old?: {
    id: string;
    like_count?: number;
    [key: string]: any;
  };
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
}

const CommentLikeButton = ({ commentId, initialLikes = 0 }: CommentLikeButtonProps) => {
  const [likeCount, setLikeCount] = useState(initialLikes);
  const [isLiked, setIsLiked] = useState(false);
  const { user } = useAuth();

  // Check if the current user has already liked this comment
  useEffect(() => {
    const checkIfLiked = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .rpc('check_comment_like', {
            comment_id_param: commentId,
            user_id_param: user.id
          });

        if (error) {
          console.error("Error checking comment like:", error);
          return;
        }

        setIsLiked(!!data);
      } catch (error) {
        console.error("Error in checkIfLiked:", error);
      }
    };

    checkIfLiked();
  }, [commentId, user]);

  // Subscribe to real-time changes for this comment
  useEffect(() => {
    // Fixed: Corrected the channel subscription syntax for Supabase
    const channel = supabase
      .channel(`comment-likes-${commentId}`)
      .on(
        'postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'comments',
          filter: `id=eq.${commentId}`
        },
        (payload) => {
          // Type assertion to ensure TypeScript knows the shape of the payload
          const typedPayload = payload as unknown as CommentPayload;
          if (typedPayload.new && typeof typedPayload.new.like_count !== 'undefined') {
            setLikeCount(typedPayload.new.like_count || 0);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [commentId]);

  const handleLike = async () => {
    if (!user) {
      toast.error("Please sign in to like comments");
      return;
    }

    try {
      if (!isLiked) {
        // Add like
        const { error } = await supabase
          .rpc('add_comment_like', {
            comment_id_param: commentId,
            user_id_param: user.id
          });

        if (error) throw error;

        setIsLiked(true);
        setLikeCount(prev => prev + 1);
      } else {
        // Remove like
        const { error } = await supabase
          .rpc('remove_comment_like', {
            comment_id_param: commentId,
            user_id_param: user.id
          });

        if (error) throw error;

        setIsLiked(false);
        setLikeCount(prev => Math.max(0, prev - 1));
      }
    } catch (error: any) {
      console.error("Error toggling like:", error);
      toast.error(`Failed to ${isLiked ? 'unlike' : 'like'} comment`);
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      className={`flex items-center gap-1 p-1 ${isLiked ? 'text-red-500' : 'text-gray-400'}`}
      onClick={handleLike}
    >
      <Heart 
        size={16} 
        className={isLiked ? "fill-current" : ""} 
      />
      <span className="text-xs">{likeCount > 0 ? likeCount : ''}</span>
    </Button>
  );
};

export default CommentLikeButton;
