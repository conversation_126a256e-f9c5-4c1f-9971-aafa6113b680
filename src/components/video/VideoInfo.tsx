
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Music } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { DEFAULT_AVATAR } from "@/components/ui/avatar";

interface VideoInfoProps {
  username: string;
  userAvatar: string;
  caption: string;
  tags: string[];
  gameType?: string;
  userId: string;
  soundtrack: string;
}

const VideoInfo = ({ 
  username, 
  userAvatar, 
  caption, 
  tags, 
  gameType, 
  userId,
  soundtrack 
}: VideoInfoProps) => {
  const navigate = useNavigate();

  const navigateToUserProfile = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/user/${userId}`);
  };

  const handleTagClick = (e: React.MouseEvent, tag: string) => {
    e.stopPropagation();
    navigate(`/discover?tag=${encodeURIComponent(tag)}`);
  };

  const handleGameTypeClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (gameType) {
      navigate(`/discover?gameType=${encodeURIComponent(gameType.toLowerCase())}`);
    }
  };

  return (
    <div className="relative z-10 pb-safe">
      <div className="flex items-center">
        <div 
          className="profile-circle mr-3 relative cursor-pointer group"
          onClick={navigateToUserProfile}
        >
          <Avatar className="w-12 h-12 ring-1 ring-white/50 ring-offset-1 ring-offset-black/50 transition-all transform group-hover:scale-105 animate-fade-in">
            <AvatarImage src={userAvatar} alt={username} className="object-cover" />
            <AvatarFallback>
              <img src={DEFAULT_AVATAR} alt={username} className="object-cover w-full h-full" />
            </AvatarFallback>
          </Avatar>
          <div className="plus-button bg-gradient-to-r from-blue-500 to-purple-500 shadow-md opacity-0 group-hover:opacity-100 transition-opacity">+</div>
        </div>
        <div className="flex flex-col">
          <span 
            className="font-bold text-white text-base cursor-pointer hover:underline transition-all duration-300 hover:text-blue-400 bg-black/30 px-2 py-1 rounded-lg backdrop-blur-sm"
            onClick={navigateToUserProfile}
          >
            @{username}
          </span>
          <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500 w-0 group-hover:w-full transition-all duration-300" />
        </div>
      </div>
      
      <p className="text-white mt-2 text-sm line-clamp-2 bg-black/20 backdrop-blur-sm p-2 rounded-lg">{caption}</p>
      
      <div className="flex flex-wrap mt-2 max-w-[90vw]">
        {tags.map((tag, index) => (
          <span 
            key={index} 
            className="text-xs text-white bg-black/20 rounded-full px-2 py-0.5 mr-1 mb-1 backdrop-blur-sm cursor-pointer hover:bg-black/40 transition-colors"
            onClick={(e) => handleTagClick(e, tag)}
          >
            #{tag}
          </span>
        ))}
        {gameType && (
          <span 
            className={`text-xs text-white bg-game-${gameType.toLowerCase()} rounded-full px-2 py-0.5 mr-1 mb-1 cursor-pointer hover:opacity-80 transition-opacity`}
            onClick={handleGameTypeClick}
          >
            {gameType}
          </span>
        )}
      </div>
      
      {soundtrack && (
        <div className="flex items-center mt-2 text-white bg-black/30 rounded-lg px-2 py-1 backdrop-blur-sm inline-block">
          <Music size={12} className="mr-1 text-blue-300 animate-pulse" />
          <p className="text-xs truncate max-w-[60vw]">{soundtrack}</p>
        </div>
      )}
    </div>
  );
};

export default VideoInfo;
