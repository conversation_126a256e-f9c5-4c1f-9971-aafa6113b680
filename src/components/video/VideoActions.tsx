
import { useAuth } from "@/contexts/AuthContext";
import LikeButton from "./LikeButton";
import CommentButton from "./CommentButton";
import ShareButton from "./ShareButton";
import DeleteVideoButton from "@/components/DeleteVideoButton";

interface VideoActionsProps {
  id: string;
  likes: number;
  comments: number;
  shares: number;
  userId: string;
  username: string;
  videoUrl: string;
  caption: string;
  userAvatar: string;
  onDelete?: (videoId: string) => void;
  isAdmin?: boolean;
}

const VideoActions = ({
  id,
  likes,
  comments,
  shares,
  userId,
  username,
  videoUrl,
  caption,
  userAvatar,
  onDelete,
  isAdmin = false
}: VideoActionsProps) => {
  const { user } = useAuth();
  
  const handleDeleteSuccess = () => {
    console.log("Delete success in VideoActions for video:", id);
    if (onDelete) {
      onDelete(id);
    }
  };
  
  // Show delete button if user owns the video OR is an admin
  const canDelete = (user && user.id === userId) || isAdmin;
  
  return (
    <div className="absolute right-4 bottom-24 flex flex-col items-center z-20">
      {/* Increase z-index to ensure buttons are always clickable */}
      <LikeButton videoId={id} initialLikes={likes} />
      
      <CommentButton 
        videoId={id} 
        commentCount={comments} 
        videoUrl={videoUrl}
        username={username}
        caption={caption}
        userAvatar={userAvatar}
      />
      
      <ShareButton 
        videoId={id} 
        initialShares={shares} 
        username={username}
        caption={caption}
      />
      
      {/* Show delete button if user owns the video OR is an admin and onDelete is provided */}
      {canDelete && onDelete && (
        <DeleteVideoButton 
          videoId={id} 
          userId={userId} 
          onDeleteSuccess={handleDeleteSuccess}
          variant="feed"
        />
      )}
    </div>
  );
};

export default VideoActions;
