
import { useState, useEffect } from "react";
import { Heart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";

interface LikeButtonProps {
  videoId: string;
  initialLikes: number;
}

const LikeButton = ({ videoId, initialLikes }: LikeButtonProps) => {
  const [liked, setLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(initialLikes);
  const { user } = useAuth();
  const navigate = useNavigate();

  // Update like count when initialLikes prop changes
  useEffect(() => {
    setLikeCount(initialLikes);
  }, [initialLikes]);

  // Check if the current user has liked the video and get the updated like count
  useEffect(() => {
    // Always fetch the latest like count from the database when component mounts
    refreshLikeCount();
    
    if (user) {
      const checkUserLike = async () => {
        try {
          const { data, error } = await supabase.rpc('check_user_like', { 
            video_id_param: videoId,
            user_id_param: user.id
          });
          
          if (!error && data !== null) {
            setLiked(data);
          }
        } catch (error) {
          console.error("Error checking if user liked video:", error);
        }
      };
      
      checkUserLike();

      // Set up realtime subscription to listen for like changes
      const channel = supabase
        .channel(`public:likes:video_id=eq.${videoId}`)
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'likes',
          filter: `video_id=eq.${videoId}`
        }, () => {
          // When likes change, fetch the updated count from the videos table
          refreshLikeCount();
        })
        .subscribe();

      return () => {
        supabase.removeChannel(channel);
      };
    }
  }, [videoId, user]);

  const refreshLikeCount = async () => {
    try {
      const { data, error } = await supabase
        .from('videos')
        .select('likes')
        .eq('id', videoId)
        .single();
        
      if (!error && data) {
        setLikeCount(data.likes || 0);
      }
    } catch (error) {
      console.error("Error fetching updated like count:", error);
    }
  };

  const handleLike = async () => {
    if (!user) {
      toast.error("Please sign in to like videos");
      navigate("/auth", { state: { returnTo: window.location.pathname } });
      return;
    }
    
    try {
      // Use RPC functions to make the backend handle all security and data integrity
      if (liked) {
        // Optimistically update UI first
        setLiked(false);
        const newLikeCount = Math.max(0, likeCount - 1);
        setLikeCount(newLikeCount);
        
        const { error } = await supabase.rpc('remove_like', {
          video_id_param: videoId,
          user_id_param: user.id
        });
          
        if (error) {
          console.error("Error removing like:", error);
          // Revert optimistic update on error
          setLiked(true);
          setLikeCount(likeCount);
          throw error;
        }
        
        toast.success("Removed like");
        
        // Call our edge function to update the video like count
        await supabase.functions.invoke('update_video_like_count', {
          body: { videoId }
        });
        
      } else {
        // Optimistically update UI first
        setLiked(true);
        const newLikeCount = likeCount + 1;
        setLikeCount(newLikeCount);
        
        const { error } = await supabase.rpc('add_like', {
          video_id_param: videoId,
          user_id_param: user.id
        });
          
        if (error) {
          console.error("Error adding like:", error);
          // Revert optimistic update on error
          setLiked(false);
          setLikeCount(likeCount);
          throw error;
        }
        
        toast.success("Added like");
        
        // Call our edge function to update the video like count
        await supabase.functions.invoke('update_video_like_count', {
          body: { videoId }
        });
      }
      
      // Refresh the like count after a short delay to ensure database has updated
      setTimeout(refreshLikeCount, 300);
      
    } catch (error: any) {
      console.error("Error updating like:", error);
      toast.error("Failed to update like");
      // Refresh the like count to ensure UI is in sync
      refreshLikeCount();
    }
  };

  return (
    <div className="action-button">
      <Button 
        variant="ghost" 
        size="icon" 
        className={`action-icon ${liked ? 'text-red-500' : 'text-white'} hover:scale-110 transition-transform`}
        onClick={handleLike}
      >
        <Heart 
          size={28} 
          fill={liked ? "#ef4444" : "none"} 
          className="drop-shadow-[0_0_8px_rgba(255,255,255,0.5)]" 
        />
      </Button>
      <span className="text-white text-xs font-medium drop-shadow-md">{likeCount}</span>
    </div>
  );
};

export default LikeButton;
