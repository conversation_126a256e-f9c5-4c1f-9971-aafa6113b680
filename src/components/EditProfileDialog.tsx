
import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, Mail, Link } from "lucide-react";
import { 
  <PERSON><PERSON>, 
  <PERSON>alogContent, 
  Di<PERSON>Header, 
  <PERSON><PERSON>T<PERSON><PERSON>, 
  Di<PERSON>Footer,
  DialogTrigger 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { DEFAULT_AVATAR } from "@/components/ui/avatar";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import ProfilePhotoUpload from "@/components/profile/ProfilePhotoUpload";
import BadgeSelector from "@/components/profile/BadgeSelector";

interface EditProfileDialogProps {
  username: string;
  avatarUrl: string;
  bio: string;
  region?: string;
  badges?: string[];
  website?: string;
  onSave: (profileData: {
    username: string;
    avatarUrl: string;
    bio: string;
    region?: string;
    badges?: string[];
    website?: string;
  }) => void;
}

const EditProfileDialog = ({
  username,
  avatarUrl,
  bio,
  region = "",
  badges = [],
  website = "",
  onSave
}: EditProfileDialogProps) => {
  const { user, updateEmail } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [form, setForm] = useState({
    username,
    avatarUrl,
    bio,
    region,
    email: user?.email || "",
    website
  });
  const [selectedBadges, setSelectedBadges] = useState<string[]>([]);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [isUpdatingEmail, setIsUpdatingEmail] = useState(false);

  // Update form state when props change and when dialog opens
  useEffect(() => {
    if (isOpen) {
      setForm({
        username,
        avatarUrl: avatarUrl || "",
        bio,
        region: region || "",
        email: user?.email || "",
        website: website || ""
      });
      
      const presetFromExisting = badges?.filter(badge => 
        ["Buck Fever Survivor", "Backstrap Dreamer", "Hang & Bang-ish", "Wrong Stand Specialist", "Tag Donation Club"]
        .includes(badge)
      ) || [];
      setSelectedBadges(presetFromExisting);
      setPreviewImage(null);
    }
  }, [isOpen, username, avatarUrl, bio, region, badges, user?.email, website]);

  const currentImageUrl = previewImage || form.avatarUrl || avatarUrl || DEFAULT_AVATAR;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  const handleImageChange = (imageUrl: string, isPreview: boolean) => {
    if (isPreview) {
      setPreviewImage(imageUrl);
    } else {
      setForm(prev => ({ ...prev, avatarUrl: imageUrl }));
    }
  };

  const handleUrlChange = (url: string) => {
    setForm(prev => ({ ...prev, avatarUrl: url }));
    if (previewImage) {
      setPreviewImage(null);
    }
  };

  const handleBadgeToggle = (badge: string) => {
    setSelectedBadges(prev => {
      if (prev.includes(badge)) {
        return prev.filter(b => b !== badge);
      } else {
        return [...prev, badge];
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (form.email !== user?.email && updateEmail) {
      try {
        setIsUpdatingEmail(true);
        await updateEmail(form.email);
        toast.success("Email updated successfully");
      } catch (error: any) {
        toast.error(`Failed to update email: ${error.message}`);
        setForm(prev => ({ ...prev, email: user?.email || "" }));
        setIsUpdatingEmail(false);
        return;
      }
      setIsUpdatingEmail(false);
    }
    
    const finalAvatarUrl = previewImage || form.avatarUrl;
    
    onSave({
      username: form.username,
      avatarUrl: finalAvatarUrl,
      bio: form.bio,
      region: form.region,
      badges: selectedBadges,
      website: form.website
    });
    
    toast.success("Profile updated successfully");
    setIsOpen(false);
    setPreviewImage(null);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="w-full bg-forest hover:bg-forest-dark">
          <Pencil size={16} className="mr-2" />
          Edit Profile
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Profile</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4 pt-4">
          
          <ProfilePhotoUpload
            currentAvatarUrl={currentImageUrl}
            username={form.username}
            onImageChange={handleImageChange}
            onUrlChange={handleUrlChange}
            avatarUrlValue={form.avatarUrl}
          />
          
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center gap-1">
              <Mail size={16} />
              Email Address
            </Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={form.email}
              onChange={handleChange}
              placeholder="Your email address"
              disabled={isUpdatingEmail}
              required
            />
            <p className="text-xs text-muted-foreground">
              Changing your email will require you to verify the new address.
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              name="username"
              value={form.username}
              onChange={handleChange}
              placeholder="Username"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="bio">Bio</Label>
            <Input
              id="bio"
              name="bio"
              value={form.bio}
              onChange={handleChange}
              placeholder="Tell us about yourself"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="region">Region</Label>
            <Input
              id="region"
              name="region"
              value={form.region}
              onChange={handleChange}
              placeholder="Your hunting region"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="website" className="flex items-center gap-1">
              <Link size={16} />
              Website
            </Label>
            <Input
              id="website"
              name="website"
              type="url"
              value={form.website}
              onChange={handleChange}
              placeholder="https://yourwebsite.com"
            />
          </div>
          
          <BadgeSelector
            selectedBadges={selectedBadges}
            onBadgeToggle={handleBadgeToggle}
          />
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" className="bg-forest hover:bg-forest-dark" disabled={isUpdatingEmail}>
              {isUpdatingEmail ? "Updating..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditProfileDialog;
