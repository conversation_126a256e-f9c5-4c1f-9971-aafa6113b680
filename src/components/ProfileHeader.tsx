import { Log<PERSON>ut, Share2, <PERSON><PERSON><PERSON>, <PERSON>riefcase, Link, MessageSquare } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback, DEFAULT_AVATAR } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import EditProfileDialog from "@/components/EditProfileDialog";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useState } from "react";
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import ServicesDialog from "@/components/ServicesDialog";
import { Json } from "@/integrations/supabase/types";
import ShareProfileButton from "@/components/ShareProfileButton";
import WhitetailScore from "@/components/WhitetailScore";
import { useWhitetailScore } from "@/hooks/useWhitetailScore";
import DeleteAccountDialog from "@/components/DeleteAccountDialog";
import { Link as RouterLink } from "react-router-dom";

interface ProfileHeaderProps {
  username: string;
  avatarUrl: string;
  bio: string;
  isCurrentUser?: boolean;
  region?: string;
  badges?: string[];
  services?: ServiceItem[];
  website?: string;
  onProfileUpdate?: (profileData: {
    username: string;
    avatarUrl: string;
    bio: string;
    region?: string;
    badges?: string[];
    website?: string;
  }) => void;
  onServicesUpdate?: (services: ServiceItem[]) => void;
  userId?: string; // Add userId prop for external profiles
}

export interface ServiceItem {
  type: string;
  description: string;
  contactMethod?: string;
  contactValue?: string;
  video_url?: string;
  location?: {
    city?: string;
    zipCode?: string;
    coordinates?: {
      lat?: number;
      lng?: number;
    } | null;
  };
}

const ProfileHeader = ({
  username,
  avatarUrl,
  bio,
  isCurrentUser = false,
  region,
  badges = [],
  services = [],
  website,
  onProfileUpdate,
  onServicesUpdate,
  userId
}: ProfileHeaderProps) => {
  const { signOut, user } = useAuth();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const whitetailScore = useWhitetailScore(userId);
  
  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Signed out successfully");
    } catch (error: any) {
      toast.error(`Error signing out: ${error.message}`);
    }
  };
  
  const handleProfileSave = async (profileData: {
    username: string;
    avatarUrl: string;
    bio: string;
    region?: string;
    badges?: string[];
    website?: string;
  }) => {
    console.log("ProfileHeader received profile update:", profileData);
    
    if (!user) {
      toast.error("You must be logged in to update your profile");
      return;
    }
    
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          username: profileData.username,
          avatar_url: profileData.avatarUrl,
          bio: profileData.bio,
          region: profileData.region,
          badges: profileData.badges,
          website: profileData.website
        })
        .eq('id', user.id);
        
      if (error) {
        console.error("Supabase error in ProfileHeader:", error);
        throw error;
      }
      
      console.log("Profile updated successfully in ProfileHeader");
      toast.success("Profile updated successfully");
      
      if (onProfileUpdate) {
        console.log("Calling parent onProfileUpdate with:", profileData);
        onProfileUpdate(profileData);
      }
    } catch (error: any) {
      console.error("Error updating profile in ProfileHeader:", error);
      toast.error("Failed to update profile: " + (error.message || "Unknown error"));
    }
  };

  const handleServicesSave = async (updatedServices: ServiceItem[]) => {
    if (!user) {
      toast.error("You must be logged in to update your services");
      return;
    }

    try {
      // Convert ServiceItems to a plain object array for storage in JSON field
      const servicesForStorage = updatedServices.map(service => ({
        type: service.type,
        description: service.description,
        contactMethod: service.contactMethod || "",
        contactValue: service.contactValue || "",
        video_url: service.video_url || "", // Include video_url in storage
        location: service.location || null
      }));
      
      const { error } = await supabase
        .from('profiles')
        .update({
          services: servicesForStorage as Json
        })
        .eq('id', user.id);
        
      if (error) {
        console.error("Supabase error updating services:", error);
        throw error;
      }
      
      toast.success("Services updated successfully");
      
      if (onServicesUpdate) {
        onServicesUpdate(updatedServices);
      }
    } catch (error: any) {
      console.error("Error updating services:", error);
      toast.error("Failed to update services: " + (error.message || "Unknown error"));
    }
  };

  return (
    <div className="pt-12 px-3">
      {/* Mobile-Optimized Header Section with Camouflage Background */}
      <div 
        className="relative overflow-hidden rounded-2xl p-4 mb-4 shadow-xl border border-earth-dark/60 backdrop-blur-md"
        style={{
          backgroundImage: `url('/lovable-uploads/c047076f-15c2-478e-aeb7-abe8a5e69f0d.png')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-black/40 rounded-2xl"></div>
        
        {/* Simplified Background Elements for Mobile */}
        <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-earth-light/20 to-forest/15 rounded-full blur-2xl animate-pulse-subtle"></div>
        <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-forest-light/30 to-earth-dark/20 rounded-full blur-xl animate-float"></div>
        
        <div className="relative z-10">
          {/* Mobile Header Layout */}
          <div className="flex justify-between items-start mb-4">
            <div className="flex-1">
              <div className="mb-2">
                <h1 className="text-2xl font-bold bg-gradient-to-r from-earth-light via-forest-light to-earth-light bg-clip-text text-transparent">
                  @{username}
                </h1>
                <div className="w-full h-1 bg-gradient-to-r from-earth-light/60 via-forest-light/50 to-earth-light/60 rounded-full mt-1"></div>
              </div>
              <div className="transform hover:scale-105 transition-all duration-300">
                <WhitetailScore 
                  videoCount={whitetailScore.videoCount} 
                  size="small" 
                />
              </div>
            </div>
            
            {/* Compact Action Buttons */}
            <div className="flex gap-2 ml-3">
              {isCurrentUser ? (
                <Sheet open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
                  <SheetTrigger asChild>
                    <Button variant="ghost" size="sm" className="hover:bg-earth-light/20 transition-all duration-300 rounded-lg border border-earth-light/30 text-earth-light p-2">
                      <Settings size={16} />
                    </Button>
                  </SheetTrigger>
                  <SheetContent>
                    <SheetHeader>
                      <SheetTitle>Settings</SheetTitle>
                    </SheetHeader>
                    <div className="py-6">
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-sm font-medium">Account</h3>
                          <Separator className="my-2" />
                          <div className="space-y-2">
                            <DeleteAccountDialog />
                            <Button 
                              variant="outline" 
                              className="w-full flex justify-between items-center hover:bg-destructive/5 hover:border-destructive/20 transition-colors"
                              onClick={handleSignOut}
                            >
                              <span>Sign Out</span>
                              <LogOut size={16} />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </SheetContent>
                </Sheet>
              ) : (
                <div className="hover:scale-105 transition-transform duration-300">
                  <ShareProfileButton />
                </div>
              )}
            </div>
          </div>
          
          {/* Mobile Avatar and Actions Layout */}
          <div className="flex items-center gap-4">
            <div className="relative group/avatar flex-shrink-0">
              {/* Simplified glows for mobile */}
              <div className="absolute inset-0 bg-gradient-to-r from-earth-dark/40 via-forest-dark/30 to-earth/30 rounded-full blur-xl group-hover/avatar:blur-2xl transition-all duration-500 scale-110"></div>
              <Avatar className="relative w-32 h-32 border-2 border-earth-light shadow-lg transition-all duration-300 hover:scale-105 ring-2 ring-earth-dark/40">
                <AvatarImage 
                  src={avatarUrl} 
                  alt={username}
                  className="w-full h-full object-cover"
                  onLoad={() => setImageLoaded(true)}
                />
                <AvatarFallback className="w-full h-full">
                  <img 
                    src={DEFAULT_AVATAR} 
                    alt={username} 
                    className="w-full h-full object-cover" 
                  />
                </AvatarFallback>
              </Avatar>
            </div>
            
            {isCurrentUser && (
              <div className="flex-1">
                <RouterLink to="/inbox">
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="w-full flex items-center justify-center gap-0.5 text-earth-light border border-earth-light/50 hover:bg-earth-dark/20 transition-all duration-300 rounded-lg px-1 py-0.5 text-xs"
                  >
                    <MessageSquare size={10} />
                    <span>Messages</span>
                  </Button>
                </RouterLink>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Mobile-Optimized Information Cards */}
      <div className="space-y-3 mb-6">
        {/* Compact Badges and Region Card */}
        {(region || (badges && badges.length > 0)) && (
          <div className="bg-gradient-to-br from-earth-light/15 via-forest-light/10 to-earth-dark/8 rounded-xl p-4 border border-earth-dark/30 shadow-sm">
            {region && (
              <div className="mb-3">
                <span className="inline-flex items-center text-xs font-semibold bg-gradient-to-r from-earth-dark to-forest-dark text-earth-light rounded-full px-3 py-1.5 shadow-md">
                  <span className="mr-1">📍</span>
                  {region}
                </span>
              </div>
            )}
            
            {badges && badges.length > 0 && (
              <div>
                <h4 className="text-xs font-semibold text-forest-dark mb-2 opacity-70">Badges</h4>
                <div className="flex flex-wrap gap-1.5">
                  {badges.map((badge, index) => (
                    <span 
                      key={index} 
                      className="inline-flex items-center text-xs bg-gradient-to-r from-earth/30 to-forest/25 text-forest-dark font-medium rounded-md px-2 py-1 border border-earth-dark/20 shadow-sm"
                    >
                      {badge}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Compact Bio Card */}
        {bio && (
          <div className="bg-gradient-to-br from-earth-light/12 via-forest-light/8 to-earth-dark/6 rounded-xl p-4 border border-earth-dark/30 shadow-sm">
            <p className="text-sm leading-relaxed text-forest-dark font-bold">{bio}</p>
          </div>
        )}
        
        {/* Compact Website Card */}
        {website && (
          <div className="bg-gradient-to-r from-earth-dark/12 to-forest-dark/15 rounded-xl p-3 border border-earth-dark/30">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-earth/25 to-forest/30 rounded-lg shadow-sm">
                <Link size={14} className="text-earth-dark" />
              </div>
              <a 
                href={website} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-earth-dark hover:text-forest-dark text-sm font-semibold transition-colors duration-200 hover:underline"
              >
                {website}
              </a>
            </div>
          </div>
        )}
      </div>
      
      {/* Mobile-Optimized Services Section */}
      {services && services.length > 0 && (
        <div className="mb-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-gradient-to-br from-forest/15 to-earth/25 rounded-lg shadow-sm">
              <Briefcase size={16} className="text-forest-dark" />
            </div>
            <h3 className="text-lg font-bold bg-gradient-to-r from-forest-dark to-forest bg-clip-text text-transparent">Services</h3>
          </div>
          <div className="grid gap-3">
            {services.map((service, index) => (
              <div key={index} className="bg-gradient-to-br from-white via-earth-light/15 to-forest/6 p-4 rounded-xl border border-earth/30 shadow-sm">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-bold text-sm text-forest-dark">{service.type}</h4>
                  {service.contactMethod && (
                    <span className="text-xs bg-gradient-to-r from-forest/15 to-earth-light/30 text-forest-dark px-2 py-1 rounded-full font-medium">
                      {service.contactMethod}
                    </span>
                  )}
                </div>
                <p className="text-sm text-slate-600 leading-relaxed mb-2">{service.description}</p>
                
                {/* Display service video if available */}
                {service.video_url && (
                  <div className="mb-3">
                    <video 
                      src={service.video_url} 
                      className="w-full max-w-xs h-32 object-cover rounded-lg border shadow-sm"
                      controls
                      muted
                      poster="" // You could add a poster image here
                    />
                  </div>
                )}
                
                {service.location && (service.location.zipCode || service.location.city) && (
                  <p className="text-sm flex items-center gap-1 text-forest/80 mb-2">
                    <span>📍</span>
                    {service.location.zipCode}
                    {service.location.city && ` (${service.location.city})`}
                  </p>
                )}
                {service.contactValue && (
                  <p className="text-sm text-forest-dark font-semibold bg-forest/10 rounded-md px-2 py-1 inline-block">
                    {service.contactValue}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Mobile-Optimized Profile Actions */}
      {isCurrentUser && (
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gradient-to-r from-earth-light/15 to-forest/8 rounded-xl border border-earth/20">
            <h3 className="text-sm font-semibold text-forest-dark">Share Profile</h3>
            <ShareProfileButton showPopover={false} />
          </div>
          
          <div className="grid gap-3">
            <EditProfileDialog
              username={username}
              avatarUrl={avatarUrl}
              bio={bio}
              region={region}
              badges={badges}
              website={website}
              onSave={handleProfileSave}
            />
            
            <ServicesDialog
              services={services}
              onSave={handleServicesSave}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileHeader;
