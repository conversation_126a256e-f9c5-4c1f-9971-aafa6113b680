
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const ProtectedRoute = () => {
  const { user, loading, profile } = useAuth();
  const location = useLocation();
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminChecked, setAdminChecked] = useState(false);

  // Check if user is an admin for the admin route
  useEffect(() => {
    const checkAdminStatus = () => {
      if (location.pathname.startsWith('/admin')) {
        // Check if the user has an admin badge
        if (profile?.badges && Array.isArray(profile.badges)) {
          setIsAdmin(profile.badges.includes("admin"));
        } else {
          setIsAdmin(false);
        }
      }
      setAdminChecked(true);
    };

    if (profile) {
      checkAdminStatus();
    }
  }, [profile, location.pathname]);

  // Show loading state
  if (loading || (location.pathname.startsWith('/admin') && !adminChecked)) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-black">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  // If not authenticated, redirect to login and save current location
  if (!user) {
    // Save the current location to redirect back after login
    sessionStorage.setItem('redirectAfterLogin', location.pathname);
    return <Navigate to="/auth" />;
  }

  // If trying to access admin route but not an admin, redirect to home and show toast
  if (location.pathname.startsWith('/admin') && !isAdmin) {
    toast.error("You don't have permission to access the admin panel");
    return <Navigate to="/" />;
  }

  // If authenticated, render children
  return <Outlet />;
};

export default ProtectedRoute;
