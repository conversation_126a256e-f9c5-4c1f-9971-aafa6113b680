import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useRevenueCatContext, shouldUseRevenueCat } from '@/contexts/RevenueCatContext';
import { Smartphone, Globe, Crown, RefreshCw, CheckCircle, XCircle, Loader2 } from 'lucide-react';

/**
 * Test component for RevenueCat functionality
 * This component helps test and debug RevenueCat integration
 */
const RevenueCatTest: React.FC = () => {
  const isNative = shouldUseRevenueCat();
  const revenueCat = isNative ? useRevenueCatContext() : null;

  const handlePurchase = async () => {
    if (!revenueCat) return;
    await revenueCat.purchaseAnnualSubscription();
  };

  const handleRestore = async () => {
    if (!revenueCat) return;
    await revenueCat.restorePurchases();
  };

  const handleReinitialize = async () => {
    if (!revenueCat) return;
    await revenueCat.reinitialize();
  };

  const handleSync = async () => {
    if (!revenueCat) return;
    await revenueCat.syncSubscriptionStatus();
  };

  const productInfo = revenueCat?.getProductInfo();

  return (
    <div className="p-4 max-w-2xl mx-auto space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            RevenueCat Integration Test
          </CardTitle>
          <CardDescription>
            Test RevenueCat functionality for mobile subscription management
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Platform Info */}
          <div className="flex items-center gap-2">
            <span className="font-medium">Platform:</span>
            {isNative ? (
              <Badge variant="default" className="bg-blue-100 text-blue-800">
                <Smartphone className="h-3 w-3 mr-1" />
                Native (RevenueCat Active)
              </Badge>
            ) : (
              <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                <Globe className="h-3 w-3 mr-1" />
                Web (RevenueCat Disabled)
              </Badge>
            )}
          </div>

          {/* RevenueCat Status */}
          {isNative && revenueCat && (
            <>
              <div className="space-y-2">
                <h4 className="font-medium">RevenueCat Status:</h4>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm">Initialized:</span>
                    {revenueCat.isInitialized ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">Loading:</span>
                    {revenueCat.isLoading ? (
                      <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">Subscribed:</span>
                    {revenueCat.isSubscribed ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">Has Offering:</span>
                    {revenueCat.currentOffering ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                  </div>
                </div>
              </div>

              {/* Error Display */}
              {revenueCat.error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <h4 className="font-medium text-red-800 mb-1">Error:</h4>
                  <p className="text-sm text-red-700">{revenueCat.error}</p>
                </div>
              )}

              {/* Product Information */}
              {productInfo && (
                <div className="space-y-2">
                  <h4 className="font-medium">Product Information:</h4>
                  <div className="bg-muted p-3 rounded-md text-sm space-y-1">
                    <div><strong>ID:</strong> {productInfo.identifier}</div>
                    <div><strong>Title:</strong> {productInfo.title}</div>
                    <div><strong>Price:</strong> {productInfo.priceString}</div>
                    <div><strong>Currency:</strong> {productInfo.currencyCode}</div>
                    {productInfo.originalPrice && (
                      <div><strong>Original Price:</strong> {productInfo.originalPrice}</div>
                    )}
                    {productInfo.discountPercentage && (
                      <div><strong>Discount:</strong> {productInfo.discountPercentage}%</div>
                    )}
                    <div><strong>Description:</strong> {productInfo.description}</div>
                  </div>
                </div>
              )}

              {/* Customer Info */}
              {revenueCat.customerInfo && (
                <div className="space-y-2">
                  <h4 className="font-medium">Customer Information:</h4>
                  <div className="bg-muted p-3 rounded-md text-sm space-y-1">
                    <div><strong>User ID:</strong> {revenueCat.customerInfo.originalAppUserId}</div>
                    <div><strong>Active Entitlements:</strong> {Object.keys(revenueCat.customerInfo.entitlements.active).length}</div>
                    <div><strong>All Entitlements:</strong> {Object.keys(revenueCat.customerInfo.entitlements.all).length}</div>
                    <div><strong>Latest Expiration:</strong> {revenueCat.customerInfo.latestExpirationDate || 'N/A'}</div>
                  </div>
                </div>
              )}

              {/* Test Actions */}
              <div className="space-y-2">
                <h4 className="font-medium">Test Actions:</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  <Button
                    onClick={handlePurchase}
                    disabled={revenueCat.isLoading || !revenueCat.annualPackage}
                    className="flex items-center gap-2"
                  >
                    <Crown className="h-4 w-4" />
                    Test Purchase
                  </Button>

                  <Button
                    onClick={handleRestore}
                    disabled={revenueCat.isLoading}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Restore Purchases
                  </Button>

                  <Button
                    onClick={handleReinitialize}
                    disabled={revenueCat.isLoading}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Reinitialize
                  </Button>

                  <Button
                    onClick={handleSync}
                    disabled={revenueCat.isLoading}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Sync Status
                  </Button>
                </div>
              </div>

              {/* Subscription Status */}
              {revenueCat.isSubscribed && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <div className="flex items-center gap-2 text-green-800">
                    <CheckCircle className="h-5 w-5" />
                    <span className="font-medium">Active Subscription</span>
                  </div>
                  <p className="text-sm text-green-700 mt-1">
                    User has an active subscription and should have access to premium features.
                  </p>
                </div>
              )}
            </>
          )}

          {/* Web Platform Message */}
          {!isNative && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-blue-800">
                <Globe className="h-5 w-5" />
                <span className="font-medium">Web Platform</span>
              </div>
              <p className="text-sm text-blue-700 mt-1">
                RevenueCat is only available on native mobile platforms. Web users will use Stripe checkout.
              </p>
            </div>
          )}

          {/* Instructions */}
          <div className="space-y-2">
            <h4 className="font-medium">Instructions:</h4>
            <div className="bg-muted p-3 rounded-md text-sm space-y-1">
              <div><strong>Mobile:</strong> RevenueCat handles subscriptions through app stores</div>
              <div><strong>Web:</strong> Stripe handles subscriptions through web checkout</div>
              <div><strong>Testing:</strong> Use sandbox/test environment for development</div>
              <div><strong>Product ID:</strong> com.whitetaillivn.whitetaillivn.Annual</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RevenueCatTest;
