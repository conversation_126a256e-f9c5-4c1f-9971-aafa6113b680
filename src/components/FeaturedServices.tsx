import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Star } from "lucide-react";
import { Button } from "@/components/ui/button";
interface Service {
  type: string;
  description: string;
  location?: {
    city?: string;
    zipCode?: string;
    coordinates?: {
      lat?: number;
      lng?: number;
    } | null;
  };
  price?: string;
  contact?: string;
}
const FeaturedServices = () => {
  const [featuredServices, setFeaturedServices] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const fetchFeaturedServices = async () => {
      try {
        const {
          data,
          error
        } = await supabase.from('profiles').select('*').eq('is_promoted', true).gte('promoted_until', new Date().toISOString()).limit(3);
        if (error) {
          console.error('Error fetching featured services:', error);
          return;
        }
        if (data) {
          // Parse services data and filter for featured ones
          const servicesWithParsedData = data.map(profile => ({
            ...profile,
            parsedServices: Array.isArray(profile.services) ? profile.services as unknown as Service[] : []
          })).filter(profile => profile.parsedServices.length > 0);
          setFeaturedServices(servicesWithParsedData);
        }
      } catch (error) {
        console.error('Error in fetchFeaturedServices:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchFeaturedServices();
  }, []);
  if (loading) {
    return <div className="space-y-4">
        {[1, 2, 3].map(i => <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </CardContent>
          </Card>)}
      </div>;
  }
  if (featuredServices.length === 0) {
    return;
  }
  return <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <Star className="h-5 w-5 text-yellow-500" />
        <h3 className="text-lg font-semibold">Featured Services</h3>
      </div>
      
      {featuredServices.map(profile => (
        <Card 
          key={profile.id} 
          className="group relative overflow-hidden transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 hover:rotate-1 shadow-lg bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50 border-2 border-yellow-400 hover:border-yellow-500 shadow-yellow-200/50 hover:shadow-yellow-300/60 ring-2 ring-yellow-200 hover:ring-yellow-300"
        >
          {/* Enhanced animated background overlay */}
          <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-yellow-100/30 to-amber-100/40"></div>
          
          {/* Enhanced decorative corner accent */}
          <div className="absolute top-0 left-0 w-24 h-24 rounded-br-full bg-gradient-to-br from-yellow-300/20 to-amber-300/30"></div>

          {/* Featured star decoration */}
          <div className="absolute top-3 right-3 z-20">
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-yellow-400 to-amber-500 flex items-center justify-center shadow-lg animate-pulse">
              <Star className="h-4 w-4 text-white fill-current" />
            </div>
          </div>

          <CardHeader className="pb-3 relative z-10">
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-bold text-amber-800 group-hover:text-amber-700 transition-colors duration-300">
                {profile.username}
              </CardTitle>
              <Badge 
                variant="secondary" 
                className="bg-gradient-to-r from-yellow-200 to-amber-200 text-amber-800 border-2 border-yellow-400 shadow-md font-bold px-3 py-1"
              >
                <Star className="h-3 w-3 mr-1 fill-current" />
                Featured
              </Badge>
            </div>
            {profile.region && (
              <div className="flex items-center gap-2 text-sm text-amber-700 font-medium">
                <div className="p-2 rounded-full bg-yellow-100 group-hover:bg-yellow-200 transition-colors duration-300">
                  <MapPin className="h-4 w-4" />
                </div>
                <span>{profile.region}</span>
              </div>
            )}
          </CardHeader>
          <CardContent className="pt-0 pb-6 relative z-10">
            <div className="space-y-4">
              {profile.parsedServices.slice(0, 2).map((service: Service, idx: number) => (
                <div key={idx} className="relative">
                  <div className="backdrop-blur-sm rounded-xl p-4 border shadow-md hover:shadow-lg transition-all duration-200 group-hover:scale-105 bg-white/90 border-yellow-200 hover:border-yellow-300 shadow-yellow-100">
                    <div className="flex items-center gap-3 mb-3">
                      <Badge 
                        variant="outline" 
                        className="font-bold px-3 py-1 text-sm transition-all duration-200 shadow-sm hover:shadow-md bg-gradient-to-r from-yellow-100 to-amber-100 text-amber-700 border-yellow-400"
                      >
                        {service.type}
                      </Badge>
                    </div>
                    
                    {service.description && (
                      <p className="text-sm text-gray-700 leading-relaxed mb-3 line-clamp-2 font-medium">
                        {service.description}
                      </p>
                    )}
                    
                    {service.location && (service.location.zipCode || service.location.city) && (
                      <div className="flex items-center gap-3 text-xs font-medium rounded-lg px-3 py-2 border text-amber-700 bg-yellow-50 border-yellow-200">
                        <MapPin className="h-3 w-3 text-amber-600" />
                        <span>
                          {service.location.zipCode}
                          {service.location.city && ` • ${service.location.city}`}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-4 w-full group-hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl font-bold text-base py-3 bg-gradient-to-r from-yellow-500 via-amber-500 to-orange-500 hover:from-yellow-600 hover:via-amber-600 hover:to-orange-600 text-white shadow-yellow-400/40 hover:shadow-yellow-500/50 border-0"
            >
              View Profile
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>;
};
export default FeaturedServices;