import React, { useState, useRef, useEffect } from "react";
import { Tag } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// Updated tag suggestions with new tags
const TAG_SUGGESTIONS = [
  "Velvet Bucks",
  "Bow Hunting",
  "Gun Hunting",
  "Shed Hunting",
  "Food Plots",
  "Trail Cam Videos",
  "The Rut",
  "Big Bucks",
  "Harvest Shot",
];

interface TagInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const TagInput: React.FC<TagInputProps> = ({ 
  value, 
  onChange,
  placeholder = "Add tags (separate with commas)"
}) => {
  const [open, setOpen] = useState(false);
  const [currentTag, setCurrentTag] = useState("");
  const [displayValue, setDisplayValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const [cursorPosition, setCursorPosition] = useState(0);
  
  // Parse the comma-separated tags string into an array
  const tagsArray = value.split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);
  
  // Format the display value with highlighted tags
  useEffect(() => {
    // Keep the original input value but format it for display purposes
    setDisplayValue(value);
    
    // Update cursor position on value change if input is focused
    if (document.activeElement === inputRef.current && inputRef.current) {
      const position = inputRef.current.selectionStart || 0;
      setCursorPosition(position);
    }
  }, [value]);

  // Restore cursor position after render
  useEffect(() => {
    if (inputRef.current && document.activeElement === inputRef.current) {
      inputRef.current.setSelectionRange(cursorPosition, cursorPosition);
    }
  }, [displayValue, cursorPosition]);
  
  // Filter suggestions based on current input
  const getSuggestions = () => {
    if (!currentTag.trim()) return [];
    
    const searchTerm = currentTag.toLowerCase();
    return TAG_SUGGESTIONS.filter(
      tag => tag.toLowerCase().includes(searchTerm) && 
      !tagsArray.some(t => t.toLowerCase() === tag.toLowerCase())
    );
  };
  
  const suggestions = getSuggestions();
  
  // Handle selecting a suggestion
  const handleSelectTag = (tag: string) => {
    const newTagsArray = [...tagsArray];
    
    // Replace current tag or add to the end
    if (currentTag.trim() !== "") {
      // Find where in the comma-separated string we are currently editing
      const fullInput = inputRef.current?.value || "";
      const cursorPos = inputRef.current?.selectionStart || 0;
      
      // Find the start and end positions of the current tag being typed
      let startPos = cursorPos;
      while (startPos > 0 && fullInput[startPos - 1] !== ',') {
        startPos--;
      }
      
      let endPos = cursorPos;
      while (endPos < fullInput.length && fullInput[endPos] !== ',') {
        endPos++;
      }
      
      // Determine which tag position we're editing
      const tagIndex = fullInput.substring(0, startPos).split(',').filter(t => t.trim()).length;
      
      if (tagIndex < newTagsArray.length) {
        // Replace existing tag
        newTagsArray[tagIndex] = tag;
      } else {
        // Add new tag
        newTagsArray.push(tag);
      }
    } else {
      // Just add to the end if we're not currently editing a tag
      newTagsArray.push(tag);
    }
    
    // Join tags with a comma and a space
    const newValue = newTagsArray.join(', ');
    onChange(newValue);
    setCurrentTag("");
    setOpen(false);
    
    // Move cursor to the beginning of the input field
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        // Set cursor position to 0 to position at the beginning
        inputRef.current.setSelectionRange(0, 0);
        setCursorPosition(0);
      }
    }, 0);
  };
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    onChange(inputValue);
    
    // Save cursor position
    const cursorPos = e.target.selectionStart || 0;
    setCursorPosition(cursorPos);
    
    // Determine the current tag being typed
    let startPos = cursorPos;
    while (startPos > 0 && inputValue[startPos - 1] !== ',') {
      startPos--;
    }
    
    const currentTagText = inputValue.substring(startPos, cursorPos).trim();
    setCurrentTag(currentTagText);
    
    // Open dropdown if we have a partial tag and suggestions
    if (currentTagText && getSuggestions().length > 0) {
      setOpen(true);
    } else {
      setOpen(false);
    }
  };

  // Handle key navigation
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle Tab or Enter to select first suggestion
    if ((e.key === 'Tab' || e.key === 'Enter') && suggestions.length > 0 && open) {
      e.preventDefault();
      handleSelectTag(suggestions[0]);
    }
  };
  
  // Handle tag button click
  const handleTagButtonClick = (tag: string) => {
    // Check if tag already exists
    if (tagsArray.some(t => t.toLowerCase() === tag.toLowerCase())) {
      return; // Skip if tag already exists
    }
    
    // Add the tag to the list
    const newTagsArray = [...tagsArray, tag];
    const newValue = newTagsArray.join(', ');
    onChange(newValue);
    
    // Move cursor to the beginning
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.setSelectionRange(0, 0);
        setCursorPosition(0);
      }
    }, 0);
  };
  
  // Get unused tags that can be shown as buttons
  const getUnusedTags = () => {
    return TAG_SUGGESTIONS.filter(
      tag => !tagsArray.some(t => t.toLowerCase() === tag.toLowerCase())
    );
  };
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setOpen(false);
    };
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  
  return (
    <div className="space-y-2">
      <div className="relative">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <div className="flex items-center space-x-2">
              <Tag size={20} className="text-muted-foreground" />
              <div className="relative flex-1">
                <div className="absolute inset-0 pointer-events-none px-3 py-2 flex flex-wrap gap-1 items-center overflow-hidden">
                  {tagsArray.length > 0 ? (
                    tagsArray.map((tag, index) => (
                      <React.Fragment key={index}>
                        <span className="bg-forest text-white px-2 py-0.5 rounded text-sm">
                          {tag}
                        </span>
                        {index < tagsArray.length - 1 && ", "}
                      </React.Fragment>
                    ))
                  ) : null}
                </div>
                <Input
                  ref={inputRef}
                  placeholder={tagsArray.length ? "" : placeholder}
                  value={value}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  className="flex-1 text-transparent bg-transparent selection:bg-sky-200 caret-black"
                />
              </div>
            </div>
          </PopoverTrigger>
          <PopoverContent className="p-0" align="start">
            {suggestions.length > 0 && (
              <Command>
                <CommandList>
                  <CommandGroup>
                    {suggestions.map((tag) => (
                      <CommandItem
                        key={tag}
                        onSelect={() => handleSelectTag(tag)}
                        className="cursor-pointer"
                      >
                        {tag}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            )}
          </PopoverContent>
        </Popover>
      </div>
      
      {/* Tag buttons row */}
      <div className="flex flex-wrap gap-2 mt-2">
        {getUnusedTags().map((tag) => (
          <Badge 
            key={tag}
            variant="outline" 
            className="cursor-pointer bg-background hover:bg-accent hover:text-accent-foreground"
            onClick={() => handleTagButtonClick(tag)}
          >
            {tag}
          </Badge>
        ))}
      </div>
    </div>
  );
};

export default TagInput;
export { TagInput };
