import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/integrations/supabase/types";
import ServiceFilters from "./services/ServiceFilters";
import ServiceTable from "./services/ServiceTable";
import EditServiceDialog from "./services/EditServiceDialog";
import { ProfileWithServices, ServiceWithProfile, ServiceItem } from "./services/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Users, Eye, TrendingUp, Crown, Star, DollarSign, Calendar } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const AdminServicesControl = () => {
  const { user } = useAuth();
  
  // State management
  const [profiles, setProfiles] = useState<ProfileWithServices[]>([]);
  const [promotedProfiles, setPromotedProfiles] = useState<ProfileWithServices[]>([]);
  const [services, setServices] = useState<ServiceWithProfile[]>([]);
  const [filteredServices, setFilteredServices] = useState<ServiceWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Filter states
  const [searchQuery, setSearchQuery] = useState("");
  const [serviceTypeFilter, setServiceTypeFilter] = useState("all");
  const [regionFilter, setRegionFilter] = useState("all");
  
  // Edit dialog states
  const [editingService, setEditingService] = useState<ServiceWithProfile | null>(null);
  const [editedDescription, setEditedDescription] = useState("");
  const [editedContactMethod, setEditedContactMethod] = useState("");
  const [editedContactValue, setEditedContactValue] = useState("");
  const [saving, setSaving] = useState(false);

  // Delete dialog states
  const [deletingService, setDeletingService] = useState<ServiceWithProfile | null>(null);
  const [deleting, setDeleting] = useState(false);

  // Service types from the actual Services Directory
  const serviceTypes = [
    "Guided Hunts",
    "Deer Processing", 
    "Taxidermy",
    "Drone Recovery",
    "Deer Tracking Dogs",
    "Land Management",
    "Food Plot Work", 
    "Trail Cam Setup",
    "Videographer",
    "Bow Tech",
    "Official Scorer",
    "Social Media Marketing",
    "Euro Mounts",
    "Influencer Collaboration"
  ];

  // Fetch services data with improved error handling and correct column selection
  const fetchServices = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id, 
          username, 
          avatar_url, 
          region, 
          services, 
          video_count, 
          created_at, 
          is_premium, 
          premium_until,
          is_promoted,
          promoted_until,
          stripe_customer_id
        `)
        .not('services', 'is', null);

      if (error) {
        console.error("Database error:", error);
        throw error;
      }

      console.log("Raw fetched profiles data:", data);

      // Process profiles with consistent boolean handling
      const processedProfiles: ProfileWithServices[] = (data || []).map(profile => {
        // Ensure is_premium is always a boolean
        const isPremium = profile.is_premium === true;

        const typedServices: ServiceItem[] = Array.isArray(profile.services) 
          ? profile.services.map((service: Json) => {
              if (typeof service === 'object' && service !== null) {
                return {
                  type: (service as any).type || "",
                  description: (service as any).description || "",
                  contactMethod: (service as any).contactMethod || "",
                  contactValue: (service as any).contactValue || "",
                  video_url: (service as any).video_url || "",
                  location: (service as any).location || null
                };
              }
              return {
                type: "",
                description: "",
                contactMethod: "",
                contactValue: "",
                video_url: "",
                location: null
              };
            })
          : [];

        return {
          ...profile,
          services: typedServices,
          is_premium: isPremium
        };
      });

      setProfiles(processedProfiles);

      // Flatten services with profile data
      const allServices: ServiceWithProfile[] = [];
      processedProfiles.forEach(profile => {
        profile.services.forEach((service: ServiceItem, index: number) => {
          allServices.push({
            ...service,
            profileId: profile.id,
            username: profile.username,
            avatar_url: profile.avatar_url,
            region: profile.region,
            video_count: profile.video_count,
            is_premium: profile.is_premium,
            premium_until: profile.premium_until,
            serviceIndex: index
          });
        });
      });

      setServices(allServices);
      console.log(`Successfully loaded ${allServices.length} services from ${processedProfiles.length} profiles`);
      
      // Fetch promoted profiles separately
      await fetchPromotedProfiles();
      
    } catch (error: any) {
      console.error("Error fetching services:", error);
      toast.error(`Failed to load services: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Fetch promoted profiles
  const fetchPromotedProfiles = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id, 
          username, 
          avatar_url, 
          region, 
          services, 
          is_promoted,
          promoted_until,
          stripe_customer_id,
          created_at
        `)
        .eq('is_promoted', true)
        .gte('promoted_until', new Date().toISOString());

      if (error) {
        console.error("Error fetching promoted profiles:", error);
        throw error;
      }

      const processedPromotedProfiles: ProfileWithServices[] = (data || []).map(profile => {
        const typedServices: ServiceItem[] = Array.isArray(profile.services) 
          ? profile.services.map((service: Json) => {
              if (typeof service === 'object' && service !== null) {
                return {
                  type: (service as any).type || "",
                  description: (service as any).description || "",
                  contactMethod: (service as any).contactMethod || "",
                  contactValue: (service as any).contactValue || "",
                  video_url: (service as any).video_url || "",
                  location: (service as any).location || null
                };
              }
              return {
                type: "",
                description: "",
                contactMethod: "",
                contactValue: "",
                video_url: "",
                location: null
              };
            })
          : [];

        return {
          ...profile,
          services: typedServices,
          is_promoted: profile.is_promoted,
          promoted_until: profile.promoted_until,
          stripe_customer_id: profile.stripe_customer_id
        };
      });

      setPromotedProfiles(processedPromotedProfiles);
      
    } catch (error: any) {
      console.error("Error fetching promoted profiles:", error);
      toast.error(`Failed to load promoted profiles: ${error.message}`);
    }
  };

  // Apply filters
  useEffect(() => {
    let result = [...services];
    
    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      result = result.filter(service => 
        service.description?.toLowerCase().includes(query) || 
        service.type?.toLowerCase().includes(query) ||
        service.username?.toLowerCase().includes(query) ||
        service.region?.toLowerCase().includes(query) ||
        service.contactValue?.toLowerCase().includes(query)
      );
    }
    
    // Service type filter
    if (serviceTypeFilter !== "all") {
      result = result.filter(service => service.type === serviceTypeFilter);
    }
    
    // Region filter
    if (regionFilter !== "all") {
      result = result.filter(service => service.region === regionFilter);
    }
    
    setFilteredServices(result);
  }, [searchQuery, serviceTypeFilter, regionFilter, services]);

  // Get unique regions from services
  const availableRegions = Array.from(
    new Set(services.map(s => s.region).filter(Boolean))
  ).sort();

  // Statistics
  const stats = {
    totalServices: services.length,
    totalProviders: new Set(services.map(s => s.profileId)).size,
    activeRegions: availableRegions.length,
    premiumProviders: new Set(services.filter(s => s.is_premium).map(s => s.profileId)).size,
    promotedProviders: promotedProfiles.length
  };

  // Handle toggle premium status with edge function instead of RPC
  const handleTogglePremium = async (profileId: string, isPremium: boolean) => {
    if (!user?.id) {
      toast.error("Not authenticated");
      return;
    }

    try {
      console.log(`Toggling premium status for profile ${profileId} to ${isPremium}`);
      
      // Use edge function instead of RPC to avoid column ambiguity
      const { data, error } = await supabase.functions.invoke('admin-update-premium', {
        body: {
          target_user_id: profileId,
          is_premium: isPremium,
          admin_user_id: user.id
        }
      });
      
      if (error) {
        console.error("Edge function error:", error);
        throw error;
      }
      
      if (!data?.success) {
        throw new Error("Update operation failed");
      }
      
      // Update local state immediately for better UX
      setServices(prevServices => 
        prevServices.map(service => 
          service.profileId === profileId 
            ? { ...service, is_premium: isPremium }
            : service
        )
      );
      
      setProfiles(prevProfiles =>
        prevProfiles.map(profile =>
          profile.id === profileId
            ? { ...profile, is_premium: isPremium }
            : profile
        )
      );
      
      toast.success(`Premium status ${isPremium ? 'enabled' : 'disabled'} successfully`);
      
      // Refresh data to ensure consistency
      await fetchServices();
      
    } catch (error: any) {
      console.error("Error updating premium status:", error);
      toast.error(`Failed to update premium status: ${error.message}`);
      
      // Refresh data to revert any optimistic updates
      await fetchServices();
    }
  };

  // Handle edit service
  const handleEditService = (service: ServiceWithProfile) => {
    setEditingService(service);
    setEditedDescription(service.description || "");
    setEditedContactMethod(service.contactMethod || "");
    setEditedContactValue(service.contactValue || "");
  };

  // Save service changes with admin function
  const handleSaveService = async () => {
    if (!editingService || !user?.id) {
      toast.error("Missing required information");
      return;
    }
    
    setSaving(true);
    try {
      const profile = profiles.find(p => p.id === editingService.profileId);
      if (!profile) {
        throw new Error("Profile not found");
      }
      
      // Create updated services array
      const updatedServices = profile.services.map((service, index) => {
        if (index === editingService.serviceIndex) {
          return {
            ...service,
            description: editedDescription,
            contactMethod: editedContactMethod,
            contactValue: editedContactValue
          };
        }
        return service;
      });
      
      // Convert to Json type for Supabase
      const servicesAsJson: Json = updatedServices.map(service => ({
        type: service.type,
        description: service.description,
        contactMethod: service.contactMethod,
        contactValue: service.contactValue,
        video_url: service.video_url,
        location: service.location
      })) as Json;
      
      // Use admin function to update services
      const { data, error } = await supabase.rpc('admin_update_user_services', {
        target_user_id: editingService.profileId,
        new_services: servicesAsJson,
        admin_user_id: user.id
      });
      
      if (error) {
        console.error("Error updating service:", error);
        throw error;
      }
      
      // Update local state
      setProfiles(prevProfiles =>
        prevProfiles.map(profile =>
          profile.id === editingService.profileId
            ? { ...profile, services: updatedServices }
            : profile
        )
      );
      
      // Update services state
      setServices(prevServices =>
        prevServices.map(service =>
          service.profileId === editingService.profileId && service.serviceIndex === editingService.serviceIndex
            ? {
                ...service,
                description: editedDescription,
                contactMethod: editedContactMethod,
                contactValue: editedContactValue
              }
            : service
        )
      );
      
      toast.success("Service updated successfully");
      setEditingService(null);
      
    } catch (error: any) {
      console.error("Error updating service:", error);
      toast.error(`Failed to update service: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  // Handle delete service
  const handleDeleteService = async (service: ServiceWithProfile) => {
    if (!user?.id) {
      toast.error("Not authenticated");
      return;
    }
    
    setDeleting(true);
    try {
      const profile = profiles.find(p => p.id === service.profileId);
      if (!profile) {
        throw new Error("Profile not found");
      }
      
      // Create updated services array without the deleted service
      const updatedServices = profile.services.filter((_, index) => index !== service.serviceIndex);
      
      // Convert to Json type for Supabase
      const servicesAsJson: Json = updatedServices.map(serviceItem => ({
        type: serviceItem.type,
        description: serviceItem.description,
        contactMethod: serviceItem.contactMethod,
        contactValue: serviceItem.contactValue,
        video_url: serviceItem.video_url,
        location: serviceItem.location
      })) as Json;
      
      // Use admin function to update services
      const { data, error } = await supabase.rpc('admin_update_user_services', {
        target_user_id: service.profileId,
        new_services: servicesAsJson,
        admin_user_id: user.id
      });
      
      if (error) {
        console.error("Error deleting service:", error);
        throw error;
      }
      
      // Update local state
      setProfiles(prevProfiles =>
        prevProfiles.map(profile =>
          profile.id === service.profileId
            ? { ...profile, services: updatedServices }
            : profile
        )
      );
      
      // Remove the service from services state
      setServices(prevServices =>
        prevServices.filter(s => 
          !(s.profileId === service.profileId && s.serviceIndex === service.serviceIndex)
        )
      );
      
      toast.success("Service deleted successfully");
      setDeletingService(null);
      
      // Refresh data to ensure consistency
      await fetchServices();
      
    } catch (error: any) {
      console.error("Error deleting service:", error);
      toast.error(`Failed to delete service: ${error.message}`);
    } finally {
      setDeleting(false);
    }
  };

  // View profile (placeholder for future implementation)
  const handleViewProfile = (profileId: string) => {
    console.log("View profile:", profileId);
    toast.info("Profile viewing feature coming soon");
  };

  // Initialize data
  useEffect(() => {
    fetchServices();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Services Directory Control</h2>
        <p className="text-muted-foreground mt-2">
          Manage service providers and their offerings in the Services Directory
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Services</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalServices}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Service Providers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProviders}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Premium Providers</CardTitle>
            <Crown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.premiumProviders}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Promoted Services</CardTitle>
            <Star className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.promotedProviders}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Regions</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeRegions}</div>
          </CardContent>
        </Card>
      </div>
      
      {/* Filters */}
      <ServiceFilters
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        serviceTypeFilter={serviceTypeFilter}
        setServiceTypeFilter={setServiceTypeFilter}
        regionFilter={regionFilter}
        setRegionFilter={setRegionFilter}
        onRefresh={fetchServices}
        loading={loading}
        availableServiceTypes={serviceTypes}
        availableRegions={availableRegions}
        totalResults={filteredServices.length}
      />
      
      {/* Services Table */}
      <ServiceTable
        loading={loading}
        services={filteredServices}
        onEditService={handleEditService}
        onViewProfile={handleViewProfile}
        onTogglePremium={handleTogglePremium}
        onDeleteService={setDeletingService}
      />

      {/* Edit Dialog */}
      <EditServiceDialog
        service={editingService}
        editedDescription={editedDescription}
        setEditedDescription={setEditedDescription}
        editedContactMethod={editedContactMethod}
        setEditedContactMethod={setEditedContactMethod}
        editedContactValue={editedContactValue}
        setEditedContactValue={setEditedContactValue}
        onSave={handleSaveService}
        onClose={() => setEditingService(null)}
        loading={saving}
      />

      {/* Promoted Services Subscription Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-green-500" />
            Active Promotion Subscriptions
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Users with active "Promote Your Services" subscriptions
          </p>
        </CardHeader>
        <CardContent>
          {promotedProfiles.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No active promotion subscriptions</p>
            </div>
          ) : (
            <div className="space-y-4">
              {promotedProfiles.map((profile) => (
                <div
                  key={profile.id}
                  className="flex items-center justify-between p-4 border rounded-lg bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-200"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-full overflow-hidden bg-yellow-100 flex items-center justify-center">
                      {profile.avatar_url ? (
                        <img 
                          src={profile.avatar_url} 
                          alt={profile.username} 
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Users className="h-6 w-6 text-yellow-600" />
                      )}
                    </div>
                    <div>
                      <div className="font-semibold text-amber-800">{profile.username}</div>
                      <div className="text-sm text-amber-600">{profile.region}</div>
                      <div className="text-xs text-amber-600">
                        {profile.services.length} service{profile.services.length !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="secondary" className="bg-yellow-100 text-amber-700 border-yellow-300 mb-2">
                      <Star className="h-3 w-3 mr-1" />
                      Active Promotion
                    </Badge>
                    <div className="flex items-center gap-2 text-sm text-amber-600">
                      <Calendar className="h-4 w-4" />
                      Expires: {profile.promoted_until ? new Date(profile.promoted_until).toLocaleDateString() : 'Unknown'}
                    </div>
                    {profile.stripe_customer_id && (
                      <div className="text-xs text-amber-500 mt-1">
                        Customer ID: {profile.stripe_customer_id.slice(0, 12)}...
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingService} onOpenChange={() => setDeletingService(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Service</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this service? This action cannot be undone.
              <div className="mt-4 p-3 bg-muted rounded-lg">
                <div className="font-semibold">{deletingService?.type}</div>
                <div className="text-sm text-muted-foreground">
                  Provider: {deletingService?.username}
                </div>
                <div className="text-sm text-muted-foreground line-clamp-2">
                  {deletingService?.description}
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={() => deletingService && handleDeleteService(deletingService)}
              disabled={deleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleting ? "Deleting..." : "Delete Service"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminServicesControl;
