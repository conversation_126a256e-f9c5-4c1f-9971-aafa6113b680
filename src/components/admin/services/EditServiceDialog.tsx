
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { User, MapPin, VideoIcon } from "lucide-react";
import { ServiceWithProfile } from "./types";

interface EditServiceDialogProps {
  service: ServiceWithProfile | null;
  editedDescription: string;
  setEditedDescription: (description: string) => void;
  editedContactMethod: string;
  setEditedContactMethod: (method: string) => void;
  editedContactValue: string;
  setEditedContactValue: (value: string) => void;
  onSave: () => void;
  onClose: () => void;
  loading?: boolean;
}

const EditServiceDialog = ({
  service,
  editedDescription,
  setEditedDescription,
  editedContactMethod,
  setEditedContactMethod,
  editedContactValue,
  setEditedContactValue,
  onSave,
  onClose,
  loading = false
}: EditServiceDialogProps) => {
  return (
    <Dialog open={!!service} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Service Details</DialogTitle>
          <DialogDescription>
            Modify the service information below. Changes will be reflected in the Services Directory.
          </DialogDescription>
        </DialogHeader>
        
        {service && (
          <div className="space-y-6 py-4">
            {/* Provider Information */}
            <div className="bg-muted/50 p-4 rounded-lg">
              <h3 className="font-medium mb-3">Provider Information</h3>
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 rounded-full overflow-hidden bg-muted flex-shrink-0">
                  {service.avatar_url ? (
                    <img 
                      src={service.avatar_url} 
                      alt={service.username || 'Provider'} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="w-full h-full p-2 text-muted-foreground" />
                  )}
                </div>
                <div className="space-y-2 flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-lg">{service.username}</h4>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-muted-foreground">
                    {service.region && (
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {service.region}
                      </div>
                    )}
                    {service.video_count !== undefined && (
                      <div className="flex items-center gap-1">
                        <VideoIcon className="h-3 w-3" />
                        {service.video_count} videos
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Service Details */}
            <div className="space-y-4">
              <h3 className="font-medium">Service Details</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Service Type</Label>
                  <div className="mt-1">
                    <Badge variant="outline" className="font-medium">
                      {service.type}
                    </Badge>
                  </div>
                </div>
                
                {service.location && (service.location.city || service.location.zipCode) && (
                  <div>
                    <Label className="text-sm font-medium">Service Location</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {service.location.city && service.location.zipCode 
                        ? `${service.location.city}, ${service.location.zipCode}`
                        : service.location.city || service.location.zipCode
                      }
                    </p>
                  </div>
                )}
              </div>
              
              <div>
                <Label htmlFor="serviceDescription" className="text-sm font-medium">
                  Service Description
                </Label>
                <Textarea
                  id="serviceDescription"
                  value={editedDescription}
                  onChange={(e) => setEditedDescription(e.target.value)}
                  rows={4}
                  className="mt-1"
                  placeholder="Describe the service offered..."
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contactMethod" className="text-sm font-medium">
                    Contact Method
                  </Label>
                  <Input
                    id="contactMethod"
                    value={editedContactMethod}
                    onChange={(e) => setEditedContactMethod(e.target.value)}
                    className="mt-1"
                    placeholder="e.g., Phone, Email, Website"
                  />
                </div>
                
                <div>
                  <Label htmlFor="contactValue" className="text-sm font-medium">
                    Contact Information
                  </Label>
                  <Input
                    id="contactValue"
                    value={editedContactValue}
                    onChange={(e) => setEditedContactValue(e.target.value)}
                    className="mt-1"
                    placeholder="Contact details"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
        
        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={onSave}
            disabled={loading}
          >
            {loading ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditServiceDialog;
