
export interface ServiceItem {
  type: string;
  description: string;
  contactMethod: string;
  contactValue: string;
  video_url: string;
  location: {
    city?: string;
    zipCode?: string;
    coordinates?: {
      lat?: number;
      lng?: number;
    } | null;
  } | null;
}

export interface ProfileWithServices {
  id: string;
  username: string;
  avatar_url: string | null;
  region: string | null;
  services: ServiceItem[];
  video_count?: number;
  created_at?: string;
  is_premium?: boolean;
  premium_until?: string | null;
  is_promoted?: boolean;
  promoted_until?: string | null;
  stripe_customer_id?: string | null;
}

export interface ServiceWithProfile extends ServiceItem {
  profileId: string;
  username: string;
  avatar_url: string | null;
  region: string | null;
  video_count?: number;
  is_premium?: boolean;
  premium_until?: string | null;
  serviceIndex?: number; // Add index to distinguish services from same user
}
