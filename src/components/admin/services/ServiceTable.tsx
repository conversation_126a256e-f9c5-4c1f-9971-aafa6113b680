
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { 
  Edit, 
  User,
  MapPin,
  Eye,
  VideoIcon,
  Crown,
  Trash2
} from "lucide-react";
import { ServiceWithProfile } from "./types";

interface ServiceTableProps {
  loading: boolean;
  services: ServiceWithProfile[];
  onEditService: (service: ServiceWithProfile) => void;
  onViewProfile: (profileId: string) => void;
  onTogglePremium: (profileId: string, isPremium: boolean) => void;
  onDeleteService: (service: ServiceWithProfile) => void;
}

const ServiceTable = ({
  loading,
  services,
  onEditService,
  onViewProfile,
  onTogglePremium,
  onDeleteService
}: ServiceTableProps) => {
  if (loading) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Provider</TableHead>
              <TableHead className="w-[140px]">Service Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="w-[120px]">Region</TableHead>
              <TableHead className="w-[100px]">Premium</TableHead>
              <TableHead className="w-[180px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array(5).fill(0).map((_, index) => (
              <TableRow key={index}>
                <TableCell><Skeleton className="h-10 w-full" /></TableCell>
                <TableCell><Skeleton className="h-6 w-full" /></TableCell>
                <TableCell><Skeleton className="h-8 w-full" /></TableCell>
                <TableCell><Skeleton className="h-6 w-full" /></TableCell>
                <TableCell><Skeleton className="h-6 w-full" /></TableCell>
                <TableCell><Skeleton className="h-8 w-full" /></TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (services.length === 0) {
    return (
      <div className="rounded-md border">
        <div className="p-8 text-center">
          <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4">
            <User className="h-6 w-6 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">No services found</h3>
          <p className="text-muted-foreground">Try adjusting your filters to see more results.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Provider</TableHead>
            <TableHead className="w-[140px]">Service Type</TableHead>
            <TableHead>Description</TableHead>
            <TableHead className="w-[120px]">Region</TableHead>
            <TableHead className="w-[100px]">Premium</TableHead>
            <TableHead className="w-[180px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {services.map((service, index) => {
            const uniqueKey = `${service.profileId}-${service.type}-${index}`;
            
            return (
              <TableRow key={uniqueKey} className="hover:bg-muted/50">
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full overflow-hidden bg-muted flex-shrink-0">
                      {service.avatar_url ? (
                        <img 
                          src={service.avatar_url} 
                          alt={service.username || 'Provider'} 
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <User className="w-full h-full p-1.5 text-muted-foreground" />
                      )}
                    </div>
                    <div className="min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">{service.username || 'Unknown Provider'}</span>
                        {service.is_premium && (
                          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                            <Crown className="h-3 w-3 mr-1" />
                            Premium
                          </Badge>
                        )}
                      </div>
                      {service.video_count !== undefined && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <VideoIcon className="h-3 w-3" />
                          {service.video_count} videos
                        </div>
                      )}
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant="outline" className="font-medium">
                    {service.type}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="max-w-xs">
                    <p className="text-sm line-clamp-2">
                      {service.description || "No description provided"}
                    </p>
                    {service.contactMethod && service.contactValue && (
                      <div className="flex items-center gap-1 mt-1 text-xs text-muted-foreground">
                        <span className="font-medium">{service.contactMethod}:</span>
                        <span className="truncate">{service.contactValue}</span>
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  {service.region ? (
                    <div className="flex items-center gap-1 text-sm">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      <span className="truncate">{service.region}</span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground text-sm">No region</span>
                  )}
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={service.is_premium || false}
                      onCheckedChange={(checked) => onTogglePremium(service.profileId, checked)}
                    />
                    <span className="text-xs text-muted-foreground">
                      {service.is_premium ? 'Premium' : 'Standard'}
                    </span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex gap-1">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => onEditService(service)}
                      title="Edit Service"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => onViewProfile(service.profileId)}
                      title="View Full Profile"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => onDeleteService(service)}
                      title="Delete Service"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

export default ServiceTable;
