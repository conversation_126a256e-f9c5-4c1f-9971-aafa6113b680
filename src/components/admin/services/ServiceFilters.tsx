
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Search, RefreshCw, X, Filter } from "lucide-react";

interface ServiceFiltersProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  serviceTypeFilter: string;
  setServiceTypeFilter: (type: string) => void;
  regionFilter: string;
  setRegionFilter: (region: string) => void;
  onRefresh: () => void;
  loading: boolean;
  availableServiceTypes: string[];
  availableRegions: string[];
  totalResults: number;
}

const ServiceFilters = ({
  searchQuery,
  setSearchQuery,
  serviceTypeFilter,
  setServiceTypeFilter,
  regionFilter,
  setRegionFilter,
  onRefresh,
  loading,
  availableServiceTypes,
  availableRegions,
  totalResults
}: ServiceFiltersProps) => {
  const clearAllFilters = () => {
    setSearchQuery("");
    setServiceTypeFilter("all");
    setRegionFilter("all");
  };

  const activeFiltersCount = [
    searchQuery,
    serviceTypeFilter !== "all" ? serviceTypeFilter : null,
    regionFilter !== "all" ? regionFilter : null
  ].filter(Boolean).length;

  return (
    <div className="space-y-4">
      {/* Main Filter Controls */}
      <div className="flex flex-col lg:flex-row gap-3">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input 
            placeholder="Search providers, services, or regions..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <Select value={serviceTypeFilter} onValueChange={setServiceTypeFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="Service Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Service Types</SelectItem>
              {availableServiceTypes.map(type => (
                <SelectItem key={type} value={type}>{type}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={regionFilter} onValueChange={setRegionFilter}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="Region" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Regions</SelectItem>
              {availableRegions.map(region => (
                <SelectItem key={region} value={region}>{region}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline"
            onClick={onRefresh}
            disabled={loading}
            className="flex-shrink-0"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
          
          {activeFiltersCount > 0 && (
            <Button 
              variant="outline"
              onClick={clearAllFilters}
              className="flex-shrink-0"
            >
              Clear Filters
              <X className="h-4 w-4 ml-1" />
            </Button>
          )}
        </div>
      </div>

      {/* Results Summary and Active Filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Filter className="h-4 w-4" />
          <span>Showing {totalResults} service{totalResults !== 1 ? 's' : ''}</span>
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFiltersCount} filter{activeFiltersCount !== 1 ? 's' : ''} active
            </Badge>
          )}
        </div>

        {/* Active Filter Tags */}
        {activeFiltersCount > 0 && (
          <div className="flex flex-wrap gap-2">
            {searchQuery && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Search: "{searchQuery}"
                <X 
                  className="h-3 w-3 cursor-pointer hover:bg-muted-foreground/20 rounded" 
                  onClick={() => setSearchQuery("")} 
                />
              </Badge>
            )}
            {serviceTypeFilter !== "all" && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Type: {serviceTypeFilter}
                <X 
                  className="h-3 w-3 cursor-pointer hover:bg-muted-foreground/20 rounded" 
                  onClick={() => setServiceTypeFilter("all")} 
                />
              </Badge>
            )}
            {regionFilter !== "all" && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Region: {regionFilter}
                <X 
                  className="h-3 w-3 cursor-pointer hover:bg-muted-foreground/20 rounded" 
                  onClick={() => setRegionFilter("all")} 
                />
              </Badge>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ServiceFilters;
