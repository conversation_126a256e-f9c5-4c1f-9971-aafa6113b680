import { useState, useEffect, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import TagInput from "@/components/TagInput";
import { Trash, Pencil, Play, Pause, Upload } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SoundItem {
  id: string;
  title: string;
  description: string | null;
  sound_url: string;
  tags: string[];
  created_at: string;
  is_active: boolean;
  category?: string | null;
}

// Available sound categories
const SOUND_CATEGORIES = ["Funny", "Motivational", "Educational"];
// Use this value for "None" category instead of empty string
const NO_CATEGORY = "none";

const AdminSoundLibrary = () => {
  const [sounds, setSounds] = useState<SoundItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentSound, setCurrentSound] = useState<SoundItem | null>(null);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [tags, setTags] = useState("");
  const [category, setCategory] = useState<string>("");
  const [soundFile, setSoundFile] = useState<File | null>(null);
  const [playingId, setPlayingId] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const { user } = useAuth();

  useEffect(() => {
    fetchSounds();
  }, []);

  const fetchSounds = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('sound_library')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      // Ensure category exists in each item
      const soundsWithCategory = data?.map(sound => ({
        ...sound,
        category: sound.category || null
      })) as SoundItem[];
      
      setSounds(soundsWithCategory);
      
    } catch (error) {
      console.error("Error fetching sounds:", error);
      toast.error("Failed to load sounds");
    } finally {
      setLoading(false);
    }
  };

  const handleSoundFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Validate file is audio
    if (!file.type.includes('audio/')) {
      toast.error("Please select an audio file (MP3, WAV, etc.)");
      return;
    }
    
    setSoundFile(file);
  };

  const handleAdd = async () => {
    try {
      if (!title || !soundFile || !user) {
        toast.error("Title and sound file are required");
        return;
      }
      
      setLoading(true);
      
      // Upload the sound file to storage
      const fileExt = soundFile.name.split('.').pop();
      const fileName = `${uuidv4()}.${fileExt}`;
      const filePath = `${fileName}`;
      
      const { error: uploadError } = await supabase.storage
        .from('sounds')
        .upload(filePath, soundFile);
        
      if (uploadError) throw uploadError;
      
      // Get the public URL
      const { data } = supabase.storage
        .from('sounds')
        .getPublicUrl(filePath);
      
      const soundUrl = data.publicUrl;
      
      // Parse tags
      const tagArray = tags.split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);
      
      // Insert into sound library
      const { error: insertError } = await supabase
        .from('sound_library')
        .insert({
          title,
          description: description || null,
          sound_url: soundUrl,
          tags: tagArray,
          category: category || null,
          created_by: user.id,
          is_active: true
        });
      
      if (insertError) throw insertError;
      
      toast.success("Sound added successfully!");
      setIsAddDialogOpen(false);
      resetForm();
      fetchSounds();
      
    } catch (error) {
      console.error("Error adding sound:", error);
      toast.error("Failed to add sound");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = async () => {
    try {
      if (!title || !currentSound || !user) {
        toast.error("Title is required");
        return;
      }
      
      setLoading(true);
      
      let soundUrl = currentSound.sound_url;
      
      // If a new sound file is uploaded, update it
      if (soundFile) {
        // Upload the new sound file
        const fileExt = soundFile.name.split('.').pop();
        const fileName = `${uuidv4()}.${fileExt}`;
        const filePath = `${fileName}`;
        
        const { error: uploadError } = await supabase.storage
          .from('sounds')
          .upload(filePath, soundFile);
          
        if (uploadError) throw uploadError;
        
        // Get the public URL
        const { data } = supabase.storage
          .from('sounds')
          .getPublicUrl(filePath);
        
        soundUrl = data.publicUrl;
      }
      
      // Parse tags
      const tagArray = tags.split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);
      
      // Update the sound library entry - convert "none" to null for category
      const { error: updateError } = await supabase
        .from('sound_library')
        .update({
          title,
          description: description || null,
          sound_url: soundUrl,
          tags: tagArray,
          category: category === NO_CATEGORY ? null : category
        })
        .eq('id', currentSound.id);
      
      if (updateError) throw updateError;
      
      toast.success("Sound updated successfully!");
      setIsEditDialogOpen(false);
      resetForm();
      fetchSounds();
      
    } catch (error) {
      console.error("Error updating sound:", error);
      toast.error("Failed to update sound");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this sound?")) {
      try {
        setLoading(true);
        
        const { error } = await supabase
          .from('sound_library')
          .delete()
          .eq('id', id);
          
        if (error) throw error;
        
        toast.success("Sound deleted successfully");
        fetchSounds();
      } catch (error) {
        console.error("Error deleting sound:", error);
        toast.error("Failed to delete sound");
      } finally {
        setLoading(false);
      }
    }
  };

  const toggleSoundActive = async (id: string, currentActive: boolean) => {
    try {
      const { error } = await supabase
        .from('sound_library')
        .update({ is_active: !currentActive })
        .eq('id', id);
        
      if (error) throw error;
      
      toast.success(`Sound ${!currentActive ? 'activated' : 'deactivated'} successfully`);
      fetchSounds();
    } catch (error) {
      console.error("Error toggling sound active status:", error);
      toast.error("Failed to update sound status");
    }
  };

  const handlePlayPause = (id: string, url: string) => {
    if (playingId === id) {
      // Currently playing this sound, so pause it
      if (audioRef.current) {
        audioRef.current.pause();
        setPlayingId(null);
      }
    } else {
      // Not playing this sound, so play it
      if (audioRef.current) {
        audioRef.current.src = url;
        audioRef.current.play().catch(error => {
          console.error("Error playing audio:", error);
          toast.error("Failed to play audio");
        });
        setPlayingId(id);
      }
    }
  };

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setTags("");
    setCategory("");
    setSoundFile(null);
    setCurrentSound(null);
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const openEditDialog = (sound: SoundItem) => {
    setCurrentSound(sound);
    setTitle(sound.title);
    setDescription(sound.description || "");
    setTags((sound.tags || []).join(", "));
    // Set category to "none" if null/undefined
    setCategory(sound.category || NO_CATEGORY);
    setIsEditDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Sound Library</h2>
        <Button onClick={() => {
          resetForm();
          setIsAddDialogOpen(true);
        }}>
          Add New Sound
        </Button>
      </div>

      {/* Hidden audio element for previewing sounds */}
      <audio
        ref={audioRef}
        onEnded={() => setPlayingId(null)}
        onError={() => {
          setPlayingId(null);
          toast.error("Error playing audio");
        }}
      />

      {/* Sound Library List */}
      {loading && sounds.length === 0 ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : sounds.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">No sounds in library yet. Add your first sound!</p>
          </CardContent>
        </Card>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Tags</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sounds.map((sound) => (
                <TableRow key={sound.id}>
                  <TableCell className="font-medium">{sound.title}</TableCell>
                  <TableCell>
                    {sound.category ? (
                      <span className="px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                        {sound.category}
                      </span>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell className="max-w-[200px] truncate">
                    {sound.description || "-"}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {sound.tags && sound.tags.map((tag, i) => (
                        <span key={i} className="text-xs bg-muted px-2 py-1 rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span 
                      className={`px-2 py-1 rounded-full text-xs ${
                        sound.is_active ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                      }`}
                    >
                      {sound.is_active ? "Active" : "Inactive"}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handlePlayPause(sound.id, sound.sound_url)}
                      >
                        {playingId === sound.id ? <Pause size={16} /> : <Play size={16} />}
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => openEditDialog(sound)}
                      >
                        <Pencil size={16} />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleDelete(sound.id)}
                      >
                        <Trash size={16} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleSoundActive(sound.id, sound.is_active)}
                      >
                        {sound.is_active ? "Deactivate" : "Activate"}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Sound Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Sound</DialogTitle>
            <DialogDescription>
              Upload a sound file to the library for users to use in their videos.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div>
              <label htmlFor="title" className="block text-sm font-medium mb-1">
                Title*
              </label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Sound title"
              />
            </div>
            
            <div>
              <label htmlFor="category" className="block text-sm font-medium mb-1">
                Category
              </label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {SOUND_CATEGORIES.map((cat) => (
                    <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label htmlFor="description" className="block text-sm font-medium mb-1">
                Description
              </label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Sound description (optional)"
              />
            </div>
            
            <div>
              <label htmlFor="tags" className="block text-sm font-medium mb-1">
                Tags
              </label>
              <TagInput
                value={tags}
                onChange={setTags}
                placeholder="Add tags (separate with commas)"
              />
            </div>
            
            <div>
              <label htmlFor="soundFile" className="block text-sm font-medium mb-1">
                Sound File*
              </label>
              <div className="flex items-center gap-2">
                <Input
                  ref={fileInputRef}
                  id="soundFile"
                  type="file"
                  accept="audio/*"
                  onChange={handleSoundFileChange}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="mr-2 h-4 w-4" /> Browse
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                MP3 or WAV format recommended. Max 10MB.
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsAddDialogOpen(false);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button onClick={handleAdd} disabled={loading}>
              {loading ? "Uploading..." : "Add Sound"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Sound Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Sound</DialogTitle>
            <DialogDescription>
              Update the details of this sound in the library.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div>
              <label htmlFor="edit-title" className="block text-sm font-medium mb-1">
                Title*
              </label>
              <Input
                id="edit-title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Sound title"
              />
            </div>
            
            <div>
              <label htmlFor="edit-category" className="block text-sm font-medium mb-1">
                Category
              </label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={NO_CATEGORY}>None</SelectItem>
                  {SOUND_CATEGORIES.map((cat) => (
                    <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label htmlFor="edit-description" className="block text-sm font-medium mb-1">
                Description
              </label>
              <Textarea
                id="edit-description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Sound description (optional)"
              />
            </div>
            
            <div>
              <label htmlFor="edit-tags" className="block text-sm font-medium mb-1">
                Tags
              </label>
              <TagInput
                value={tags}
                onChange={setTags}
                placeholder="Add tags (separate with commas)"
              />
            </div>
            
            <div>
              <label htmlFor="edit-soundFile" className="block text-sm font-medium mb-1">
                Replace Sound File (Optional)
              </label>
              <div className="flex items-center gap-2">
                <Input
                  ref={fileInputRef}
                  id="edit-soundFile"
                  type="file"
                  accept="audio/*"
                  onChange={handleSoundFileChange}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="mr-2 h-4 w-4" /> Browse
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Leave empty to keep the current sound file.
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsEditDialogOpen(false);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button onClick={handleEdit} disabled={loading}>
              {loading ? "Saving..." : "Update Sound"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminSoundLibrary;
