import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Shield, 
  ShieldCheck, 
  Search, 
  Filter, 
  Ban, 
  UserCheck,
  User,
  UserPlus,
  UserCog,
  Mail,
  Shield as ShieldIcon
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useAuth } from "@/contexts/AuthContext";

interface UserProfile {
  id: string;
  username: string;
  avatar_url: string | null;
  region: string | null;
  bio: string | null;
  badges: string[] | null;
  created_at: string;
  email?: string;
}

// Create a schema for adding new users
const createUserSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  username: z.string().min(3, "Username must be at least 3 characters"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  role: z.enum(["user", "admin", "verified", "featured"]).optional(),
});

// Create a schema for editing users
const editUserSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters"),
  region: z.string().nullable().optional(),
  bio: z.string().nullable().optional(),
});

type CreateUserFormValues = z.infer<typeof createUserSchema>;
type EditUserFormValues = z.infer<typeof editUserSchema>;

const AdminUserManagement = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterBy, setFilterBy] = useState("all");
  const [createUserDialogOpen, setCreateUserDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingUser, setEditingUser] = useState<UserProfile | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  // Initialize create form
  const createForm = useForm<CreateUserFormValues>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      email: "",
      username: "",
      password: "",
      role: "user",
    },
  });

  // Initialize edit form
  const editForm = useForm<EditUserFormValues>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      username: "",
      region: "",
      bio: "",
    },
  });

  const fetchUsers = async () => {
    setLoading(true);
    try {
      // Get profiles with proper error handling
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (profilesError) {
        console.error("Error fetching profiles:", profilesError);
        throw profilesError;
      }

      console.log("Fetched profiles:", profilesData);

      // Get user emails through admin function with error handling
      try {
        const { data: userData, error: userError } = await supabase.functions.invoke('get-user-emails');
        
        if (userError) {
          console.error("Error fetching user emails:", userError);
          
          // Check if it's an authorization error
          if (userError.message?.includes('Unauthorized') || userError.message?.includes('Admin privileges required')) {
            toast.error("You don't have admin privileges to view user emails");
          } else {
            toast.error("Failed to load user emails - some features may be limited");
          }
          
          // Continue with profile data only
          setUsers(profilesData || []);
          setFilteredUsers(profilesData || []);
          return;
        }

        // Merge profiles with emails
        const usersWithEmails = profilesData?.map(profile => {
          const userInfo = userData?.find((user: any) => user.id === profile.id);
          return {
            ...profile,
            email: userInfo?.email || 'Email not available'
          };
        }) || [];

        console.log("Users with emails:", usersWithEmails);
        setUsers(usersWithEmails);
        setFilteredUsers(usersWithEmails);
      } catch (emailError) {
        console.error("Exception fetching emails:", emailError);
        toast.error("Failed to load user emails - you may not have admin privileges");
        
        // Continue with profile data only
        setUsers(profilesData || []);
        setFilteredUsers(profilesData || []);
      }
    } catch (error: any) {
      console.error("Error in fetchUsers:", error);
      toast.error(`Failed to load users: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [searchQuery, filterBy, users]);

  const applyFilters = () => {
    let result = [...users];
    
    // Apply search filter
    if (searchQuery) {
      result = result.filter(user => 
        user.username?.toLowerCase().includes(searchQuery.toLowerCase()) || 
        user.region?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // Apply badge filter
    if (filterBy !== "all") {
      if (filterBy === "verified") {
        result = result.filter(user => 
          user.badges && user.badges.includes("verified")
        );
      } else if (filterBy === "admin") {
        result = result.filter(user => 
          user.badges && user.badges.includes("admin")
        );
      } else if (filterBy === "featured") {
        result = result.filter(user => 
          user.badges && user.badges.includes("featured")
        );
      } else if (filterBy === "banned") {
        result = result.filter(user => 
          user.badges && user.badges.includes("banned")
        );
      } else if (filterBy === "no-badges") {
        result = result.filter(user => 
          !user.badges || user.badges.length === 0
        );
      }
    }
    
    setFilteredUsers(result);
  };

  const toggleUserBadge = async (userId: string, badge: string, hasTheBadge: boolean) => {
    if (!user?.id) {
      toast.error("Not authenticated");
      return;
    }

    try {
      const targetUser = users.find(u => u.id === userId);
      if (!targetUser) {
        toast.error("User not found");
        return;
      }
      
      let updatedBadges = [...(targetUser.badges || [])];
      
      if (hasTheBadge) {
        // Remove badge
        updatedBadges = updatedBadges.filter(b => b !== badge);
      } else {
        // Add badge
        if (!updatedBadges.includes(badge)) {
          updatedBadges.push(badge);
        }
      }

      // Special case for "banned" - remove other badges when banned
      if (badge === "banned" && !hasTheBadge) {
        updatedBadges = ["banned"];
      }
      
      // Special case - remove banned when other badges are added
      if (badge !== "banned" && !hasTheBadge && updatedBadges.includes("banned")) {
        updatedBadges = updatedBadges.filter(b => b !== "banned");
      }
      
      console.log(`Updating badges for user ${userId}:`, updatedBadges);
      
      // Use admin function to update badges
      const { data, error } = await supabase.rpc('admin_update_user_badges', {
        target_user_id: userId,
        new_badges: updatedBadges,
        admin_user_id: user.id
      });
      
      if (error) {
        console.error("Error updating badges:", error);
        throw error;
      }
      
      console.log("Badge update successful");
      
      // Update local state immediately
      setUsers(prevUsers => 
        prevUsers.map(u => 
          u.id === userId ? { ...u, badges: updatedBadges } : u
        )
      );
      
      toast.success(`Badge ${hasTheBadge ? "removed" : "added"} successfully`);
      
    } catch (error: any) {
      console.error(`Error updating user badge:`, error);
      toast.error(`Failed to update user status: ${error.message}`);
    }
  };

  const getBadgeColor = (badge: string) => {
    switch (badge) {
      case "admin": return "bg-red-500";
      case "verified": return "bg-blue-500";
      case "featured": return "bg-green-500";
      case "banned": return "bg-gray-500";
      default: return "";
    }
  };

  // Handler for creating a new user
  const handleCreateUser = async (data: CreateUserFormValues) => {
    setIsSubmitting(true);
    try {
      console.log("Creating user with data:", { ...data, password: "[HIDDEN]" });
      
      // Call the Supabase Edge Function to create a user
      const { data: result, error } = await supabase.functions.invoke('create-user', {
        body: {
          email: data.email,
          password: data.password,
          username: data.username,
          role: data.role
        }
      });
      
      if (error) {
        console.error("Function invocation error:", error);
        if (error.message?.includes('Unauthorized') || error.message?.includes('Admin privileges required')) {
          toast.error("You don't have admin privileges to create users");
        } else {
          toast.error(`Failed to create user: ${error.message}`);
        }
        return;
      }
      
      if (result?.error) {
        console.error("Function returned error:", result.error);
        throw new Error(result.error);
      }
      
      console.log("User created successfully:", result);
      toast.success("User created successfully");
      
      // Close the dialog and reset form
      setCreateUserDialogOpen(false);
      createForm.reset();
      
      // Refresh the user list
      await fetchUsers();
    } catch (error: any) {
      console.error("Error creating user:", error);
      toast.error(error.message || "Failed to create user");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handler for editing a user
  const handleEditUser = async (data: EditUserFormValues) => {
    if (!editingUser || !user?.id) {
      toast.error("Missing required information");
      return;
    }
    
    setIsSubmitting(true);
    try {
      console.log(`Updating user ${editingUser.id} with data:`, data);
      
      // Use admin function to update user profile
      const { data: result, error } = await supabase.rpc('admin_update_user_profile', {
        target_user_id: editingUser.id,
        new_username: data.username,
        new_region: data.region || null,
        new_bio: data.bio || null,
        admin_user_id: user.id
      });
      
      if (error) {
        console.error("Error updating user:", error);
        throw error;
      }
      
      console.log("User updated successfully");
      
      // Update local state immediately
      setUsers(prevUsers =>
        prevUsers.map(u =>
          u.id === editingUser.id
            ? {
                ...u,
                username: data.username,
                region: data.region || null,
                bio: data.bio || null,
              }
            : u
        )
      );
      
      toast.success("User information updated successfully");
      
      // Close the dialog
      setEditDialogOpen(false);
      
    } catch (error: any) {
      console.error("Error updating user:", error);
      toast.error(error.message || "Failed to update user");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Initialize edit form with user data
  const openEditDialog = (user: UserProfile) => {
    setEditingUser(user);
    editForm.reset({
      username: user.username || "",
      region: user.region || "",
      bio: user.bio || "",
    });
    setEditDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">User Management</h2>
      
      {/* Search and Filter Controls */}
      <div className="flex flex-col md:flex-row gap-3">
        <div className="flex-1 relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input 
            placeholder="Search users by name, email or region..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="w-full md:w-64">
          <Select value={filterBy} onValueChange={setFilterBy}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Users</SelectItem>
              <SelectItem value="verified">Verified</SelectItem>
              <SelectItem value="featured">Featured</SelectItem>
              <SelectItem value="admin">Admins</SelectItem>
              <SelectItem value="banned">Banned</SelectItem>
              <SelectItem value="no-badges">No Badges</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Dialog open={createUserDialogOpen} onOpenChange={setCreateUserDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex gap-1 items-center">
              <UserPlus size={16} />
              Add User
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create New User</DialogTitle>
              <DialogDescription>
                Create a new user account with optional roles.
              </DialogDescription>
            </DialogHeader>
            
            <Form {...createForm}>
              <form onSubmit={createForm.handleSubmit(handleCreateUser)} className="space-y-4 py-4">
                <FormField
                  control={createForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} autoComplete="off" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={createForm.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input placeholder="johndoe" {...field} autoComplete="off" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={createForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="******" {...field} autoComplete="new-password" />
                      </FormControl>
                      <FormDescription>
                        Must be at least 6 characters.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={createForm.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Initial Role</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="user">Regular User</SelectItem>
                          <SelectItem value="verified">Verified User</SelectItem>
                          <SelectItem value="featured">Featured User</SelectItem>
                          <SelectItem value="admin">Administrator</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Set the initial role for this user.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setCreateUserDialogOpen(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Creating..." : "Create User"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
        <Button 
          variant="outline"
          onClick={fetchUsers}
          disabled={loading}
        >
          {loading ? "Loading..." : "Refresh"}
        </Button>
      </div>
      
      {/* Users Table */}
      <div className="rounded-md border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">User</TableHead>
              <TableHead className="w-[220px]">Email</TableHead>
              <TableHead>Region</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              Array(5).fill(0).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-8 w-[180px]" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-[200px]" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-48" /></TableCell>
                </TableRow>
              ))
            ) : filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  No users found.
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map(user => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full overflow-hidden bg-muted">
                        {user.avatar_url ? (
                          <img 
                            src={user.avatar_url} 
                            alt={user.username || 'User avatar'} 
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <User className="w-full h-full p-1" />
                        )}
                      </div>
                      <span>{user.username || 'Unnamed User'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Mail size={14} className="text-muted-foreground" />
                      <span className="text-sm font-medium">{user.email || 'Email not available'}</span>
                    </div>
                  </TableCell>
                  <TableCell>{user.region || 'Unknown region'}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {user.badges && user.badges.map(badge => (
                        <Badge key={badge} variant="secondary" className={`${getBadgeColor(badge)} text-white`}>
                          {badge}
                        </Badge>
                      ))}
                      {(!user.badges || user.badges.length === 0) && (
                        <span className="text-muted-foreground">No badges</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2 flex-wrap">
                      <Button 
                        size="sm" 
                        variant="outline"
                        title="Edit User"
                        onClick={() => openEditDialog(user)}
                      >
                        <UserCog size={16} />
                      </Button>
                      
                      <Button 
                        size="sm" 
                        variant={user.badges?.includes("admin") ? "default" : "outline"}
                        title="Toggle Admin Status"
                        onClick={() => toggleUserBadge(
                          user.id, 
                          "admin", 
                          user.badges?.includes("admin") || false
                        )}
                        className={user.badges?.includes("admin") ? "bg-red-500 hover:bg-red-600" : ""}
                      >
                        <ShieldIcon size={16} />
                      </Button>
                      
                      <Button 
                        size="sm" 
                        variant="outline"
                        title="Toggle Verified Status"
                        onClick={() => toggleUserBadge(
                          user.id, 
                          "verified", 
                          user.badges?.includes("verified") || false
                        )}
                      >
                        <ShieldCheck size={16} />
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        title="Toggle Featured Status"
                        onClick={() => toggleUserBadge(
                          user.id, 
                          "featured", 
                          user.badges?.includes("featured") || false
                        )}
                      >
                        <UserCheck size={16} />
                      </Button>
                      <Button 
                        size="sm" 
                        variant={user.badges?.includes("banned") ? "destructive" : "outline"}
                        title="Toggle Ban Status"
                        onClick={() => toggleUserBadge(
                          user.id, 
                          "banned", 
                          user.badges?.includes("banned") || false
                        )}
                      >
                        <Ban size={16} />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit User Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information.
            </DialogDescription>
          </DialogHeader>
          
          {editingUser && (
            <Form {...editForm}>
              <form onSubmit={editForm.handleSubmit(handleEditUser)} className="space-y-4 py-4">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-10 h-10 rounded-full overflow-hidden bg-muted">
                    {editingUser.avatar_url ? (
                      <img 
                        src={editingUser.avatar_url} 
                        alt={editingUser.username || 'User avatar'} 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <User className="w-full h-full p-1" />
                    )}
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <Mail size={14} className="text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">{editingUser.email}</span>
                    </div>
                  </div>
                </div>
                
                <FormField
                  control={editForm.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={editForm.control}
                  name="region"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Region</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={editForm.control}
                  name="bio"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bio</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setEditDialogOpen(false)} 
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Saving..." : "Save Changes"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminUserManagement;
