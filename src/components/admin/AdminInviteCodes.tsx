
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { format, parseISO } from "date-fns";
import { Copy, Check, Plus, Trash2, Key } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface InviteCode {
  id: string;
  code: string;
  created_at: string | null;
  created_by: string | null;
  creator_username?: string | null;
  expires_at: string | null;
  is_active: boolean | null;
  is_admin_generated: boolean | null;
  max_uses: number | null;
  remaining_uses: number | null;
  used_count?: number;
}

const AdminInviteCodes = () => {
  const [inviteCodes, setInviteCodes] = useState<InviteCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [usesCount, setUsesCount] = useState(3);
  const [daysValid, setDaysValid] = useState(30);
  const [isGenerating, setIsGenerating] = useState(false);
  
  const fetchInviteCodes = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('invite_codes')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      // In a real app, you'd want to join with the profiles table to get usernames
      // For now, we'll mock this data
      const codesWithExtraData = (data || []).map((code: InviteCode) => ({
        ...code,
        creator_username: code.is_admin_generated ? 'Admin' : 'User',
        used_count: code.max_uses && code.remaining_uses ? code.max_uses - code.remaining_uses : 0
      }));

      setInviteCodes(codesWithExtraData);
    } catch (error: any) {
      console.error("Error fetching invite codes:", error.message);
      toast.error("Failed to load invite codes");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInviteCodes();
  }, []);

  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code)
      .then(() => {
        setCopiedCode(code);
        toast.success("Invite code copied to clipboard");
        setTimeout(() => setCopiedCode(null), 2000);
      })
      .catch(() => toast.error("Failed to copy code"));
  };
  
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "—";
    return format(parseISO(dateString), "MMM dd, yyyy");
  };
  
  const generateInviteCode = async () => {
    setIsGenerating(true);
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        toast.error("Authentication required");
        return;
      }
      
      // Call the generate_invite_code function
      const { data, error } = await supabase.rpc('generate_invite_code', {
        user_id: userData.user.id,
        num_uses: usesCount,
        days_valid: daysValid
      });
      
      if (error) throw error;
      
      toast.success("New invite code generated successfully");
      fetchInviteCodes();
      
      // Return the generated code
      return data;
    } catch (error: any) {
      console.error("Error generating invite code:", error.message);
      toast.error("Failed to generate invite code");
    } finally {
      setIsGenerating(false);
    }
  };
  
  const deactivateInviteCode = async (codeId: string) => {
    try {
      const { error } = await supabase
        .from('invite_codes')
        .update({ 
          is_active: false,
          remaining_uses: 0
        })
        .eq('id', codeId);
      
      if (error) throw error;
      
      // Update local state
      setInviteCodes(prevCodes => 
        prevCodes.map(code => 
          code.id === codeId ? { ...code, is_active: false, remaining_uses: 0 } : code
        )
      );
      
      toast.success("Invite code deactivated");
    } catch (error: any) {
      console.error("Error deactivating invite code:", error.message);
      toast.error("Failed to deactivate invite code");
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4 items-start md:items-center">
        <div>
          <h2 className="text-2xl font-bold">Invite Codes</h2>
          <p className="text-muted-foreground">Manage invite codes for new user registrations</p>
        </div>
        
        <div className="flex gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button className="gap-1">
                <Plus size={16} />
                New Invite Code
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Generate New Invite Code</DialogTitle>
                <DialogDescription>
                  Create a new invite code for user registration
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <label htmlFor="uses" className="text-sm font-medium">
                    Number of Uses
                  </label>
                  <Input
                    id="uses"
                    type="number"
                    value={usesCount}
                    onChange={(e) => setUsesCount(parseInt(e.target.value) || 1)}
                    min={1}
                    max={100}
                  />
                  <p className="text-xs text-muted-foreground">
                    How many times this code can be used
                  </p>
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="days" className="text-sm font-medium">
                    Days Valid
                  </label>
                  <Input
                    id="days"
                    type="number"
                    value={daysValid}
                    onChange={(e) => setDaysValid(parseInt(e.target.value) || 1)}
                    min={1}
                    max={365}
                  />
                  <p className="text-xs text-muted-foreground">
                    Number of days this code will remain valid
                  </p>
                </div>
              </div>
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setUsesCount(3);
                    setDaysValid(30);
                  }}
                >
                  Reset
                </Button>
                <Button onClick={generateInviteCode} disabled={isGenerating}>
                  {isGenerating ? "Generating..." : "Generate Code"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <Button variant="outline" onClick={fetchInviteCodes}>
            Refresh
          </Button>
        </div>
      </div>
      
      {/* Invite Codes Table */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle>Invite Codes</CardTitle>
          <CardDescription>
            Track usage of invite codes and generate new ones
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Code</TableHead>
                  <TableHead className="hidden md:table-cell">Created By</TableHead>
                  <TableHead className="hidden md:table-cell">Created</TableHead>
                  <TableHead className="hidden md:table-cell">Expires</TableHead>
                  <TableHead>Uses</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                      <TableCell className="hidden md:table-cell"><Skeleton className="h-6 w-20" /></TableCell>
                      <TableCell className="hidden md:table-cell"><Skeleton className="h-6 w-24" /></TableCell>
                      <TableCell className="hidden md:table-cell"><Skeleton className="h-6 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-12" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-8 w-20" /></TableCell>
                    </TableRow>
                  ))
                ) : inviteCodes.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No invite codes found.
                    </TableCell>
                  </TableRow>
                ) : (
                  inviteCodes.map(code => (
                    <TableRow key={code.id}>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <span className="font-mono">{code.code}</span>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => copyToClipboard(code.code)}
                          >
                            {copiedCode === code.code ? (
                              <Check size={14} className="text-green-500" />
                            ) : (
                              <Copy size={14} />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {code.is_admin_generated ? (
                          <Badge variant="secondary" className="bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">Admin</Badge>
                        ) : (
                          <span className="text-sm">{code.creator_username || 'Unknown'}</span>
                        )}
                      </TableCell>
                      <TableCell className="hidden md:table-cell text-sm">
                        {formatDate(code.created_at)}
                      </TableCell>
                      <TableCell className="hidden md:table-cell text-sm">
                        {formatDate(code.expires_at)}
                      </TableCell>
                      <TableCell>
                        <span className="font-mono">
                          {code.used_count}/{code.max_uses || '∞'}
                        </span>
                      </TableCell>
                      <TableCell>
                        {code.is_active ? (
                          <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">Active</Badge>
                        ) : (
                          <Badge variant="secondary" className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100">Inactive</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => deactivateInviteCode(code.id)}
                          disabled={!code.is_active}
                        >
                          <Trash2 size={14} className="mr-1" />
                          Deactivate
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      
      {/* Usage Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Active Codes</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-12" />
            ) : (
              <div className="text-2xl font-bold">
                {inviteCodes.filter(code => code.is_active).length}
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Pending Uses</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-12" />
            ) : (
              <div className="text-2xl font-bold">
                {inviteCodes
                  .filter(code => code.is_active)
                  .reduce((sum, code) => sum + (code.remaining_uses || 0), 0)
                }
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Used</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-12" />
            ) : (
              <div className="text-2xl font-bold">
                {inviteCodes.reduce((sum, code) => sum + (code.used_count || 0), 0)}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminInviteCodes;
