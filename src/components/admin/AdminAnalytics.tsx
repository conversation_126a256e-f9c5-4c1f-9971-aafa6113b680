
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { ChartConfig, ChartContainer } from "@/components/ui/chart";
import { 
  ResponsiveContainer,
  Bar<PERSON>hart,
  Bar,
  LineChart, 
  Line,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  <PERSON>hart,
  Pie,
  Cell,
} from "recharts";
import { 
  TrendingUp, 
  Users, 
  Calendar, 
  Map as MapIcon,
  Activity,
  Heart,
  MessageCircle,
  Video,
  Eye
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format, subDays, parseISO } from "date-fns";
import { toast } from "@/hooks/use-toast";

const AdminAnalytics = () => {
  const [timeRange, setTimeRange] = useState("week");
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState({
    userGrowth: [] as any[],
    dailyActivity: [] as any[],
    topRegions: [] as any[],
    popularContent: [] as any[],
    engagementStats: [] as any[],
    totalStats: {
      totalUsers: 0,
      newUsers: 0,
      activeRegions: 0,
      totalPosts: 0,
      totalLikes: 0,
      totalComments: 0,
      totalViews: 0
    }
  });
  
  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);
  
  const fetchAnalyticsData = async () => {
    setLoading(true);
    
    try {
      // Calculate date range
      const days = timeRange === "week" ? 7 : timeRange === "month" ? 30 : 90;
      const startDate = subDays(new Date(), days);
      
      // Fetch total users
      const { data: totalUsersData, error: usersError } = await supabase
        .from('profiles')
        .select('id, created_at, region');
      
      if (usersError) throw usersError;
      
      // Fetch videos data with views
      const { data: videosData, error: videosError } = await supabase
        .from('videos')
        .select('id, created_at, likes, comments, views, user_id, game_type');
      
      if (videosError) throw videosError;
      
      // Fetch likes data
      const { data: likesData, error: likesError } = await supabase
        .from('likes')
        .select('id, created_at');
      
      if (likesError) throw likesError;
      
      // Fetch comments data
      const { data: commentsData, error: commentsError } = await supabase
        .from('comments')
        .select('id, created_at');
      
      if (commentsError) throw commentsError;
      
      // Process total stats
      const totalUsers = totalUsersData?.length || 0;
      const newUsers = totalUsersData?.filter(user => 
        new Date(user.created_at) >= startDate
      ).length || 0;
      
      const regions = new Set(totalUsersData?.map(user => user.region).filter(Boolean));
      const activeRegions = regions.size;
      
      const totalPosts = videosData?.length || 0;
      const totalLikes = likesData?.length || 0;
      const totalComments = commentsData?.length || 0;
      const totalViews = videosData?.reduce((sum, video) => sum + (video.views || 0), 0) || 0;
      
      // Generate dates for charts
      const dates = Array.from({length: days}, (_, i) => {
        const date = subDays(new Date(), days - i - 1);
        return format(date, "MM-dd");
      });
      
      // User Growth Data
      const usersByDate = totalUsersData?.reduce((acc, user) => {
        const date = format(new Date(user.created_at), "MM-dd");
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};
      
      const userGrowth = dates.map(date => ({
        date,
        "New Users": usersByDate[date] || 0,
        "Active Users": Math.floor((usersByDate[date] || 0) * 1.5) // Approximation
      }));
      
      // Daily Activity Data with Views
      const videosByDate = videosData?.reduce((acc, video) => {
        const date = format(new Date(video.created_at), "MM-dd");
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};
      
      const likesByDate = likesData?.reduce((acc, like) => {
        const date = format(new Date(like.created_at), "MM-dd");
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};
      
      const commentsByDate = commentsData?.reduce((acc, comment) => {
        const date = format(new Date(comment.created_at), "MM-dd");
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};
      
      // Calculate daily views by aggregating video views by their creation date
      const viewsByDate = videosData?.reduce((acc, video) => {
        const date = format(new Date(video.created_at), "MM-dd");
        acc[date] = (acc[date] || 0) + (video.views || 0);
        return acc;
      }, {} as Record<string, number>) || {};
      
      const dailyActivity = dates.map(date => ({
        date,
        "Posts": videosByDate[date] || 0,
        "Comments": commentsByDate[date] || 0,
        "Likes": likesByDate[date] || 0,
        "Views": viewsByDate[date] || 0,
      }));
      
      // Top Regions Data
      const regionCounts = totalUsersData?.reduce((acc, user) => {
        if (user.region) {
          acc[user.region] = (acc[user.region] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>) || {};
      
      const topRegions = Object.entries(regionCounts)
        .map(([name, value]) => ({ name, value }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 5);
      
      // Popular Content Types
      const gameTypeCounts = videosData?.reduce((acc, video) => {
        const type = video.game_type || 'Other';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};
      
      const gameTypeEngagement = videosData?.reduce((acc, video) => {
        const type = video.game_type || 'Other';
        const engagement = (video.likes || 0) + (video.comments || 0) + (video.views || 0);
        acc[type] = (acc[type] || 0) + engagement;
        return acc;
      }, {} as Record<string, number>) || {};
      
      const popularContent = Object.keys(gameTypeCounts).map(type => ({
        name: type,
        Posts: gameTypeCounts[type] || 0,
        Engagement: gameTypeEngagement[type] || 0,
      }));
      
      // Engagement Stats with Views
      const engagementStats = [
        { name: "Views", value: totalViews },
        { name: "Likes", value: totalLikes },
        { name: "Comments", value: totalComments },
        { name: "Videos", value: totalPosts },
      ];
      
      setAnalyticsData({
        userGrowth,
        dailyActivity,
        topRegions,
        popularContent,
        engagementStats,
        totalStats: {
          totalUsers,
          newUsers,
          activeRegions,
          totalPosts,
          totalLikes,
          totalComments,
          totalViews
        }
      });
      
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      toast({
        title: "Error",
        description: "Failed to fetch analytics data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Configuration for charts
  const userGrowthConfig: ChartConfig = {
    "New Users": {
      label: "New Users",
      theme: {
        light: "#2563eb",
        dark: "#60a5fa"
      }
    },
    "Active Users": {
      label: "Active Users",
      theme: {
        light: "#059669",
        dark: "#34d399"
      }
    }
  };
  
  const activityConfig: ChartConfig = {
    "Posts": {
      label: "Posts",
      theme: {
        light: "#db2777",
        dark: "#f472b6"
      }
    },
    "Comments": {
      label: "Comments",
      theme: {
        light: "#9333ea",
        dark: "#c084fc"
      }
    },
    "Likes": {
      label: "Likes",
      theme: {
        light: "#ea580c",
        dark: "#fb923c"
      }
    },
    "Views": {
      label: "Views",
      theme: {
        light: "#16a34a",
        dark: "#4ade80"
      }
    }
  };
  
  const regionConfig: ChartConfig = {
    "value": {
      label: "Users",
      theme: {
        light: "#2563eb",
        dark: "#60a5fa"
      }
    }
  };
  
  const contentConfig: ChartConfig = {
    "Posts": {
      label: "Posts",
      theme: {
        light: "#db2777",
        dark: "#f472b6"
      }
    },
    "Engagement": {
      label: "Engagement",
      theme: {
        light: "#9333ea",
        dark: "#c084fc"
      }
    }
  };
  
  const ENGAGEMENT_COLORS = ['#16a34a', '#3B82F6', '#8B5CF6', '#EC4899'];
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-4 justify-between items-start md:items-center">
        <div>
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
          <p className="text-muted-foreground">Track user activity and engagement metrics</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Select range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Last Week</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
              <SelectItem value="quarter">Last Quarter</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={fetchAnalyticsData}>
            Refresh
          </Button>
        </div>
      </div>
      
      {/* Stats Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">
                  {analyticsData.totalStats.totalUsers}
                </div>
                <Users className="h-4 w-4 text-muted-foreground" />
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">New Users ({timeRange})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">
                  {analyticsData.totalStats.newUsers}
                </div>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Active Regions</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">
                  {analyticsData.totalStats.activeRegions}
                </div>
                <MapIcon className="h-4 w-4 text-muted-foreground" />
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Posts</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">
                  {analyticsData.totalStats.totalPosts}
                </div>
                <Video className="h-4 w-4 text-muted-foreground" />
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Views</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">
                  {analyticsData.totalStats.totalViews.toLocaleString()}
                </div>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="growth">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-4">
          <TabsTrigger value="growth">User Growth</TabsTrigger>
          <TabsTrigger value="activity">Daily Activity</TabsTrigger>
          <TabsTrigger value="regions">Top Regions</TabsTrigger>
          <TabsTrigger value="content">Content Analysis</TabsTrigger>
        </TabsList>
        
        {/* User Growth Tab */}
        <TabsContent value="growth">
          <Card>
            <CardHeader>
              <CardTitle>User Growth</CardTitle>
              <CardDescription>New and active users over time</CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              {loading ? (
                <div className="w-full h-80 flex items-center justify-center">
                  <Skeleton className="h-64 w-full" />
                </div>
              ) : (
                <div className="h-80">
                  <ChartContainer config={userGrowthConfig}>
                    <LineChart data={analyticsData.userGrowth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="New Users" 
                        stroke="var(--color-New Users)" 
                        activeDot={{ r: 8 }} 
                      />
                      <Line 
                        type="monotone" 
                        dataKey="Active Users" 
                        stroke="var(--color-Active Users)" 
                      />
                    </LineChart>
                  </ChartContainer>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Daily Activity Tab */}
        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>Daily Activity</CardTitle>
              <CardDescription>Posts, comments, likes, and views by day</CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              {loading ? (
                <div className="w-full h-80 flex items-center justify-center">
                  <Skeleton className="h-64 w-full" />
                </div>
              ) : (
                <div className="h-80">
                  <ChartContainer config={activityConfig}>
                    <BarChart data={analyticsData.dailyActivity}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="Posts" fill="var(--color-Posts)" />
                      <Bar dataKey="Comments" fill="var(--color-Comments)" />
                      <Bar dataKey="Likes" fill="var(--color-Likes)" />
                      <Bar dataKey="Views" fill="var(--color-Views)" />
                    </BarChart>
                  </ChartContainer>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Top Regions Tab */}
        <TabsContent value="regions">
          <Card>
            <CardHeader>
              <CardTitle>Top Regions</CardTitle>
              <CardDescription>Regions with most active users</CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              {loading ? (
                <div className="w-full h-80 flex items-center justify-center">
                  <Skeleton className="h-64 w-full" />
                </div>
              ) : (
                <div className="h-80">
                  <ChartContainer config={regionConfig}>
                    <BarChart
                      layout="vertical"
                      data={analyticsData.topRegions}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" scale="band" />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="value" name="Users" fill="var(--color-value)" />
                    </BarChart>
                  </ChartContainer>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Content Analysis Tab */}
        <TabsContent value="content">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Content by Type</CardTitle>
                <CardDescription>Posts and engagement by content type</CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                {loading ? (
                  <div className="w-full h-80 flex items-center justify-center">
                    <Skeleton className="h-64 w-full" />
                  </div>
                ) : (
                  <div className="h-80">
                    <ChartContainer config={contentConfig}>
                      <BarChart data={analyticsData.popularContent}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="Posts" fill="var(--color-Posts)" />
                        <Bar dataKey="Engagement" fill="var(--color-Engagement)" />
                      </BarChart>
                    </ChartContainer>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Engagement Breakdown</CardTitle>
                <CardDescription>Distribution of user interactions</CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                {loading ? (
                  <div className="w-full h-80 flex items-center justify-center">
                    <Skeleton className="h-64 w-full" />
                  </div>
                ) : (
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={analyticsData.engagementStats}
                          cx="50%"
                          cy="50%"
                          labelLine={true}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {analyticsData.engagementStats.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={ENGAGEMENT_COLORS[index % ENGAGEMENT_COLORS.length]} />
                          ))}
                        </Pie>
                        <Legend />
                        <Tooltip formatter={(value, name) => [value, name]} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminAnalytics;
