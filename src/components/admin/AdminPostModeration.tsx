import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Search, 
  Filter, 
  Flag, 
  Trash2, 
  Star,
  Video,
  MessageCircle,
  Heart,
  Calendar,
  User,
  Eye
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";

interface Post {
  id: string;
  user_id: string;
  username: string;
  user_avatar: string | null;
  video_url: string;
  caption: string | null;
  likes: number | null;
  comments: number | null;
  views: number | null;
  created_at: string | null;
  game_type: string | null;
  tags: string[] | null;
  is_featured?: boolean;
  is_flagged?: boolean;
}

const AdminPostModeration = () => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterBy, setFilterBy] = useState("all");
  const [postTypeFilter, setPostTypeFilter] = useState("all");
  const [viewPost, setViewPost] = useState<Post | null>(null);
  const { user } = useAuth(); // Get the current user for authorization

  const fetchPosts = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('videos')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Add flags for features and flagged content (in a real app, these would be database columns)
      const processedData = (data || []).map(post => ({
        ...post,
        is_featured: false,  // This would come from DB in a real app
        is_flagged: false,   // This would come from DB in a real app
      }));

      setPosts(processedData);
      setFilteredPosts(processedData);
    } catch (error: any) {
      console.error("Error fetching posts:", error.message);
      toast.error("Failed to load posts");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [searchQuery, filterBy, postTypeFilter, posts]);

  const applyFilters = () => {
    let result = [...posts];
    
    // Apply search filter
    if (searchQuery) {
      result = result.filter(post => 
        post.username?.toLowerCase().includes(searchQuery.toLowerCase()) || 
        post.caption?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (post.tags && post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())))
      );
    }
    
    // Apply status filter
    if (filterBy !== "all") {
      if (filterBy === "featured") {
        result = result.filter(post => post.is_featured);
      } else if (filterBy === "flagged") {
        result = result.filter(post => post.is_flagged);
      }
    }
    
    // Apply post type filter
    if (postTypeFilter !== "all") {
      result = result.filter(post => post.game_type === postTypeFilter);
    }
    
    setFilteredPosts(result);
  };

  const handleDeletePost = async (postId: string) => {
    if (!confirm("Are you sure you want to delete this post? This action cannot be undone.")) {
      return;
    }
    
    try {
      if (!user) {
        toast.error("You must be logged in to delete posts");
        return;
      }
      
      // Use the secure Supabase edge function to delete the video
      const { data, error } = await supabase.functions.invoke('delete_video', {
        body: { videoId: postId }
      });
      
      if (error) {
        console.error(`Error deleting post:`, error);
        toast.error("Failed to delete post");
        return;
      }

      // Update local state
      setPosts(prevPosts => prevPosts.filter(post => post.id !== postId));
      toast.success("Post deleted successfully");
    } catch (error: any) {
      console.error(`Error deleting post:`, error.message);
      toast.error("Failed to delete post");
    }
  };

  const handleFlagPost = (postId: string, currentlyFlagged: boolean) => {
    // In a real application, this would update a database field
    setPosts(prevPosts => 
      prevPosts.map(post => 
        post.id === postId ? { ...post, is_flagged: !currentlyFlagged } : post
      )
    );
    toast.success(`Post ${currentlyFlagged ? "unflagged" : "flagged"} successfully`);
  };

  const handleFeaturePost = (postId: string, currentlyFeatured: boolean) => {
    // In a real application, this would update a database field
    setPosts(prevPosts => 
      prevPosts.map(post => 
        post.id === postId ? { ...post, is_featured: !currentlyFeatured } : post
      )
    );
    toast.success(`Post ${currentlyFeatured ? "removed from" : "added to"} featured posts`);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Unknown date";
    return new Date(dateString).toLocaleString();
  };

  const formatNumber = (num: number | null) => {
    if (num === null || num === undefined) return "0";
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Post Moderation</h2>
      
      {/* Search and Filter Controls */}
      <div className="flex flex-col md:flex-row gap-3">
        <div className="flex-1 relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input 
            placeholder="Search by username, caption, or tags..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="w-full md:w-48">
          <Select value={filterBy} onValueChange={setFilterBy}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Posts</SelectItem>
              <SelectItem value="featured">Featured</SelectItem>
              <SelectItem value="flagged">Flagged</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="w-full md:w-48">
          <Select value={postTypeFilter} onValueChange={setPostTypeFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="deer">Deer</SelectItem>
              <SelectItem value="turkey">Turkey</SelectItem>
              <SelectItem value="waterfowl">Waterfowl</SelectItem>
              <SelectItem value="fishing">Fishing</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button 
          variant="outline"
          onClick={fetchPosts}
        >
          Refresh
        </Button>
      </div>
      
      {/* Posts Table */}
      <div className="rounded-md border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px]">Content</TableHead>
              <TableHead>Author</TableHead>
              <TableHead className="hidden md:table-cell">Date</TableHead>
              <TableHead className="hidden md:table-cell">Engagement</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              Array(5).fill(0).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-16 w-[200px]" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-32" /></TableCell>
                  <TableCell className="hidden md:table-cell"><Skeleton className="h-8 w-32" /></TableCell>
                  <TableCell className="hidden md:table-cell"><Skeleton className="h-8 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-32" /></TableCell>
                </TableRow>
              ))
            ) : filteredPosts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No posts found.
                </TableCell>
              </TableRow>
            ) : (
              filteredPosts.map(post => (
                <TableRow key={post.id}>
                  <TableCell>
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => setViewPost(post)}
                          className="p-0 h-auto"
                        >
                          <div className="bg-muted aspect-video w-20 flex items-center justify-center rounded overflow-hidden">
                            <Video className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </Button>
                        <div className="flex-1 truncate">
                          <div className="line-clamp-2 text-xs">
                            {post.caption || "No caption"}
                          </div>
                          <div className="flex gap-1 mt-1 flex-wrap">
                            {post.tags?.map((tag, i) => (
                              <Badge key={i} variant="outline" className="text-[10px] py-0">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {post.game_type || "No game type"}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full overflow-hidden bg-muted flex-shrink-0">
                        {post.user_avatar ? (
                          <img 
                            src={post.user_avatar} 
                            alt={post.username || 'User'} 
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <User className="w-full h-full p-1" />
                        )}
                      </div>
                      <span className="text-sm truncate">{post.username || 'Unknown'}</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell text-sm">
                    {formatDate(post.created_at)}
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <div className="flex items-center gap-3 text-sm">
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3" /> 
                        <span className="font-medium">{formatNumber(post.views)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="w-3 h-3" /> {formatNumber(post.likes)}
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageCircle className="w-3 h-3" /> {formatNumber(post.comments)}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {post.is_featured && (
                        <Badge variant="secondary" className="bg-green-500 text-white">
                          Featured
                        </Badge>
                      )}
                      {post.is_flagged && (
                        <Badge variant="secondary" className="bg-orange-500 text-white">
                          Flagged
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        variant="outline"
                        title={post.is_featured ? "Remove from Featured" : "Mark as Featured"}
                        onClick={() => handleFeaturePost(post.id, post.is_featured || false)}
                      >
                        <Star size={16} />
                      </Button>
                      <Button 
                        size="sm" 
                        variant={post.is_flagged ? "default" : "outline"}
                        title={post.is_flagged ? "Remove Flag" : "Flag Content"}
                        onClick={() => handleFlagPost(post.id, post.is_flagged || false)}
                        className={post.is_flagged ? "bg-orange-500 hover:bg-orange-600" : ""}
                      >
                        <Flag size={16} />
                      </Button>
                      <Button 
                        size="sm" 
                        variant="destructive"
                        title="Delete Post"
                        onClick={() => handleDeletePost(post.id)}
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Post Preview Dialog */}
      <Dialog open={!!viewPost} onOpenChange={(open) => !open && setViewPost(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Post Preview</DialogTitle>
            <DialogDescription>
              {viewPost?.caption || "No caption provided for this post."}
            </DialogDescription>
          </DialogHeader>
          
          <div className="mt-2 space-y-4">
            {/* Video preview */}
            <div className="bg-black rounded overflow-hidden aspect-video flex items-center justify-center">
              {viewPost?.video_url ? (
                <video
                  src={viewPost.video_url}
                  controls
                  className="w-full h-full object-contain"
                />
              ) : (
                <div className="text-white">Video not available</div>
              )}
            </div>
            
            {/* Post details */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium mb-1 flex items-center gap-1">
                  <User size={14} /> Author
                </h4>
                <p className="text-sm">{viewPost?.username || "Unknown"}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium mb-1 flex items-center gap-1">
                  <Calendar size={14} /> Posted
                </h4>
                <p className="text-sm">{viewPost?.created_at ? formatDate(viewPost.created_at) : "Unknown"}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium mb-1">Game Type</h4>
                <p className="text-sm">{viewPost?.game_type || "Not specified"}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium mb-1">Engagement</h4>
                <div className="flex gap-3 text-sm">
                  <span className="flex items-center gap-1">
                    <Eye size={14} /> {formatNumber(viewPost?.views)}
                  </span>
                  <span className="flex items-center gap-1">
                    <Heart size={14} /> {formatNumber(viewPost?.likes)}
                  </span>
                  <span className="flex items-center gap-1">
                    <MessageCircle size={14} /> {formatNumber(viewPost?.comments)}
                  </span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium mb-1">Tags</h4>
              <div className="flex flex-wrap gap-1">
                {viewPost?.tags?.map((tag, index) => (
                  <Badge key={index} variant="outline">{tag}</Badge>
                ))}
                {(!viewPost?.tags || viewPost.tags.length === 0) && (
                  <span className="text-sm text-muted-foreground">No tags</span>
                )}
              </div>
            </div>
            
            {/* Action buttons */}
            <div className="flex justify-end gap-2 pt-4">
              {viewPost && (
                <>
                  <Button 
                    variant="outline"
                    onClick={() => {
                      handleFeaturePost(viewPost.id, viewPost.is_featured || false);
                      setViewPost({...viewPost, is_featured: !viewPost.is_featured});
                    }}
                  >
                    <Star size={16} className="mr-2" />
                    {viewPost.is_featured ? "Remove from Featured" : "Mark as Featured"}
                  </Button>
                  <Button 
                    variant={viewPost.is_flagged ? "default" : "outline"}
                    className={viewPost.is_flagged ? "bg-orange-500 hover:bg-orange-600" : ""}
                    onClick={() => {
                      handleFlagPost(viewPost.id, viewPost.is_flagged || false);
                      setViewPost({...viewPost, is_flagged: !viewPost.is_flagged});
                    }}
                  >
                    <Flag size={16} className="mr-2" />
                    {viewPost.is_flagged ? "Remove Flag" : "Flag Content"}
                  </Button>
                  <Button 
                    variant="destructive"
                    onClick={() => {
                      handleDeletePost(viewPost.id);
                      setViewPost(null);
                    }}
                  >
                    <Trash2 size={16} className="mr-2" />
                    Delete Post
                  </Button>
                </>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminPostModeration;
