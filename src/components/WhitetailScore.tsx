
import React from 'react';
import { Badge } from '@/components/ui/badge';

export interface WhitetailScoreProps {
  videoCount: number;
  size?: 'small' | 'medium' | 'large';
  showCount?: boolean;
}

export const getWhitetailTier = (videoCount: number): string => {
  if (videoCount >= 31) return 'Trophy';
  if (videoCount >= 16) return '8-Pointer';
  if (videoCount >= 6) return 'Spike';
  return 'Fawn';
};

export const getTierConfig = (tier: string) => {
  const configs = {
    'Fawn': {
      emoji: '🟢',
      color: 'bg-green-100 text-green-800 border-green-200',
      description: '0-5 videos'
    },
    'Spike': {
      emoji: '🟡',
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      description: '6-15 videos'
    },
    '8-Pointer': {
      emoji: '🔵',
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      description: '16-30 videos'
    },
    'Trophy': {
      emoji: '🟣',
      color: 'bg-purple-100 text-purple-800 border-purple-200',
      description: '31+ videos'
    }
  };
  
  return configs[tier as keyof typeof configs] || configs['Fawn'];
};

const WhitetailScore: React.FC<WhitetailScoreProps> = ({ 
  videoCount, 
  size = 'medium', 
  showCount = false 
}) => {
  const tier = getWhitetailTier(videoCount);
  const config = getTierConfig(tier);
  
  const sizeClasses = {
    small: 'text-xs px-2 py-0.5',
    medium: 'text-sm px-3 py-1',
    large: 'text-base px-4 py-2'
  };

  return (
    <div className="flex items-center gap-2">
      <Badge className={`${config.color} ${sizeClasses[size]} font-semibold border`}>
        <span className="mr-1">{config.emoji}</span>
        {tier}
      </Badge>
      {showCount && (
        <span className="text-xs text-muted-foreground">
          {videoCount} video{videoCount !== 1 ? 's' : ''}
        </span>
      )}
    </div>
  );
};

export default WhitetailScore;
