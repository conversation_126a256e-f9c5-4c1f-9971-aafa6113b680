
import { useState } from "react";
import { Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useAuth } from "@/contexts/AuthContext";

interface DeleteVideoButtonProps {
  videoId: string;
  userId: string;
  onDeleteSuccess?: () => void;
  variant?: "profile" | "feed" | "compact";
}

const DeleteVideoButton = ({ 
  videoId, 
  userId, 
  onDeleteSuccess,
  variant = "profile" 
}: DeleteVideoButtonProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const { user } = useAuth();

  const handleDelete = async () => {
    if (!videoId || !userId) {
      console.error("Missing videoId or userId for deletion");
      return;
    }

    // Security check: Only allow users to delete their own videos
    if (!user || user.id !== userId) {
      toast.error("You don't have permission to delete this video");
      return;
    }

    try {
      setIsDeleting(true);
      console.log(`Attempting to delete video with ID: ${videoId} for user ID: ${userId}`);

      // Use the database function directly through the supabase functions invoke endpoint
      const { data, error } = await supabase.functions.invoke('delete_video', {
        body: { videoId }
      });
        
      if (error) {
        console.error("Error deleting video:", error);
        toast.error("Failed to delete video");
        return;
      }

      console.log(`Successfully deleted video with ID: ${videoId}`);
      toast.success("Video deleted successfully");

      // Force refresh local data if callback provided
      if (onDeleteSuccess) {
        onDeleteSuccess();
      }
      
      setDialogOpen(false);
    } catch (error) {
      console.error("Unexpected error deleting video:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsDeleting(false);
    }
  };

  // Determine button size and style based on variant
  const getButtonProps = () => {
    switch(variant) {
      case "compact":
        return { 
          variant: "ghost" as const, 
          size: "icon" as const,
          className: "h-7 w-7 action-icon text-white" 
        };
      case "feed":
        return { 
          variant: "ghost" as const, 
          size: "icon" as const,
          className: "action-icon text-white" 
        };
      case "profile":
      default:
        return { 
          variant: "destructive" as const, 
          size: "sm" as const,
          className: "flex items-center gap-1" 
        };
    }
  };

  // Don't render button if not user's video
  if (!user || user.id !== userId) {
    return null;
  }

  const buttonProps = getButtonProps();

  return (
    <AlertDialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <AlertDialogTrigger asChild>
        <Button 
          {...buttonProps}
        >
          <Trash2 size={variant === "compact" ? 14 : variant === "profile" ? 16 : 24} />
          {variant === "profile" && <span className="text-xs">Delete</span>}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete your video.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction 
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-500 hover:bg-red-600"
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteVideoButton;
