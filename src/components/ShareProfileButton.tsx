
import { useState } from "react";
import { Share2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

interface ShareProfileButtonProps {
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  showPopover?: boolean;
  userId?: string; // Add userId prop to generate proper profile links
}

const ShareProfileButton = ({ 
  variant = "outline", 
  size = "icon",
  showPopover = true,
  userId 
}: ShareProfileButtonProps) => {
  const [isSharing, setIsSharing] = useState(false);
  const { user } = useAuth();

  const shareProfile = async () => {
    try {
      setIsSharing(true);
      
      // Generate the proper profile URL
      const profileUrl = userId 
        ? `${window.location.origin}/user/${userId}` 
        : `${window.location.origin}/user/${user?.id}`;
      
      if (navigator.share) {
        await navigator.share({
          title: 'Join me on <PERSON><PERSON><PERSON>!',
          text: 'Check out my profile on <PERSON><PERSON><PERSON>!',
          url: profileUrl
        });
        toast.success('Profile shared successfully');
      } else {
        await navigator.clipboard.writeText(profileUrl);
        toast.success('Profile link copied to clipboard');
      }
    } catch (error) {
      console.error('Error sharing profile:', error);
      
      // Fallback to copying to clipboard
      try {
        const profileUrl = userId 
          ? `${window.location.origin}/user/${userId}` 
          : `${window.location.origin}/user/${user?.id}`;
        await navigator.clipboard.writeText(profileUrl);
        toast.success('Profile link copied to clipboard');
      } catch (clipboardError) {
        toast.error('Unable to share or copy link');
      }
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <Button 
      variant={variant} 
      size={size} 
      onClick={shareProfile}
      disabled={isSharing}
      title="Share Profile"
    >
      <Share2 className="h-4 w-4" />
      {size !== "icon" && <span>{isSharing ? 'Sharing...' : 'Share Profile'}</span>}
    </Button>
  );
};

export default ShareProfileButton;
