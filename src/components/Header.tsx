
import { <PERSON>, <PERSON>u, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, MessageSquare } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/contexts/AuthContext";
import { useState, useEffect } from "react";

const Header = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isScrolled, setIsScrolled] = useState(false);
  
  const initials = user?.email ? user.email.slice(0, 2).toUpperCase() : "OU";
  
  const handleSearchClick = () => {
    navigate("/discover");
  };

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      if (scrollPosition > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header 
      className={`fixed top-0 left-1/2 -translate-x-1/2  max-w-screen-md w-full transition-all duration-300 pt-safe ${
        isScrolled 
          ? "bg-background/95 backdrop-blur-lg shadow-md" 
          : "bg-gradient-to-r from-background/70 via-background/80 to-background/70 backdrop-blur-md"
      } border-b border-border/40 flex items-center justify-between px-6 z-40`}
    >
      <Link 
        to="/" 
        className="relative group overflow-hidden flex items-center"
      >
        <div className="relative">
          <img 
            src="/lovable-uploads/e18f5e4c-02b9-4baf-8847-b9b9f15b5689.png" 
            alt="Whitetail LIVN" 
            className="h-12 w-18 object-contain transform transition-all group-hover:scale-110 duration-300"
          />
          <div className="absolute -inset-0.5 bg-gradient-to-r from-red-600/30 to-red-700/30 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        </div>
        <div className="absolute -bottom-1 -left-2 -right-2 h-1 rounded-full bg-gradient-to-r from-red-700 via-red-500 to-transparent opacity-0 group-hover:opacity-80 transition-all duration-500"></div>
      </Link>
      
      <div className="flex items-center space-x-2">
        <button 
          className="relative p-2.5 text-muted-foreground hover:text-forest transition-all duration-300 rounded-lg hover:bg-forest/10 border border-transparent hover:border-forest/20 shadow-sm hover:shadow-md backdrop-blur-sm bg-background/40"
          onClick={handleSearchClick}
          aria-label="Search"
        >
          <Search className="transition-all duration-300 hover:scale-110" size={20} />
          <span className="absolute inset-0 rounded-lg bg-gradient-to-br from-forest/5 to-forest-light/5 transform scale-0 transition-transform duration-300 hover:scale-100"></span>
        </button>

        <Link 
          to="/shot-analyzer" 
          className="relative p-2.5 text-muted-foreground hover:text-forest transition-all duration-300 rounded-lg hover:bg-forest/10 border border-transparent hover:border-forest/20 shadow-sm hover:shadow-md backdrop-blur-sm bg-background/40"
          aria-label="Shot Analyzer"
        >
          <Crosshair className="transition-all duration-300 hover:scale-110" size={20} />
          <span className="absolute inset-0 rounded-lg bg-gradient-to-br from-forest/5 to-forest-light/5 transform scale-0 transition-transform duration-300 hover:scale-100"></span>
        </Link>

        <Link
          to="/hunting-ai"
          className="relative p-2.5 text-muted-foreground hover:text-forest transition-all duration-300 rounded-lg hover:bg-forest/10 border border-transparent hover:border-forest/20 shadow-sm hover:shadow-md backdrop-blur-sm bg-background/40"
          aria-label="Hunting AI Assistant"
        >
          <div className="w-5 h-5 bg-gradient-to-br from-forest to-forest-light rounded-full flex items-center justify-center shadow-sm">
            <span className="text-white text-xs font-bold">AI</span>
          </div>
          <span className="absolute inset-0 rounded-lg bg-gradient-to-br from-forest/5 to-forest-light/5 transform scale-0 transition-transform duration-300 hover:scale-100"></span>
        </Link>
        
        <Link 
          to="/inbox" 
          className="relative p-2.5 text-muted-foreground hover:text-forest transition-all duration-300 rounded-lg hover:bg-forest/10 border border-transparent hover:border-forest/20 shadow-sm hover:shadow-md backdrop-blur-sm bg-background/40"
          aria-label="Messages"
        >
          <MessageSquare className="transition-all duration-300 hover:scale-110" size={20} />
          <span className="absolute inset-0 rounded-lg bg-gradient-to-br from-forest/5 to-forest-light/5 transform scale-0 transition-transform duration-300 hover:scale-100"></span>
        </Link>
      </div>
      
      <div className="absolute bottom-0 left-[6%] h-0.5 w-[15%] bg-gradient-to-r from-red-700 via-red-500 to-transparent rounded-full"></div>
    </header>
  );
};

export default Header;
