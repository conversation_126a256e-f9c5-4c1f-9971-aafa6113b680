
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

interface RutFormProps {
  location: { state: string; county: string } | null;
}

const RutForm: React.FC<RutFormProps> = ({ location }) => {
  const [reportType, setReportType] = useState<string>("");
  const [notes, setNotes] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!location) {
      toast.error("Please enable location to submit a report");
      return;
    }
    
    if (!reportType) {
      toast.error("Please select a report type");
      return;
    }
    
    // Submit the form
    setIsSubmitting(true);
    
    try {
      // Save the report to the database
      const { error } = await supabase
        .from('deer_reports')
        .insert({
          report_type: reportType,
          notes: notes,
          state: location.state,
          county: location.county,
        });
        
      if (error) throw error;
      
      toast.success("Report submitted successfully");
      
      // Reset form
      setReportType("");
      setNotes("");
    } catch (error) {
      console.error('Error submitting report:', error);
      toast.error("Failed to submit report");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="bg-white border border-gray-200 rounded-lg shadow-sm p-4">
      <div className="mb-4">
        <h4 className="font-medium text-gray-800 mb-2">What are you seeing?</h4>
        <RadioGroup
          value={reportType}
          onValueChange={setReportType}
          className="grid grid-cols-2 gap-2"
        >
          <div className="flex items-center space-x-2 rounded-lg border bg-card p-3 hover:bg-accent transition-colors cursor-pointer">
            <RadioGroupItem value="rub" id="rub" className="border-forest text-forest" />
            <Label htmlFor="rub" className="cursor-pointer">Rubs</Label>
          </div>
          <div className="flex items-center space-x-2 rounded-lg border bg-card p-3 hover:bg-accent transition-colors cursor-pointer">
            <RadioGroupItem value="scrape" id="scrape" className="border-forest text-forest" />
            <Label htmlFor="scrape" className="cursor-pointer">Scrapes</Label>
          </div>
          <div className="flex items-center space-x-2 rounded-lg border bg-card p-3 hover:bg-accent transition-colors cursor-pointer">
            <RadioGroupItem value="chase" id="chase" className="border-forest text-forest" />
            <Label htmlFor="chase" className="cursor-pointer">Chasing</Label>
          </div>
          <div className="flex items-center space-x-2 rounded-lg border bg-card p-3 hover:bg-accent transition-colors cursor-pointer">
            <RadioGroupItem value="matureBuck" id="matureBuck" className="border-forest text-forest" />
            <Label htmlFor="matureBuck" className="cursor-pointer whitespace-nowrap text-sm">Lockdown</Label>
          </div>
        </RadioGroup>
      </div>
      
      <div className="mb-4">
        <Label htmlFor="notes" className="mb-2 block">Additional Notes</Label>
        <Textarea
          id="notes"
          placeholder="Add any details about what you observed..."
          rows={3}
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          className="resize-none"
        />
      </div>
      
      <Button 
        type="submit" 
        className="w-full bg-forest hover:bg-forest/90"
        disabled={!location || isSubmitting}
      >
        {isSubmitting ? "Submitting..." : "Submit Report"}
      </Button>
      
      {!location && (
        <p className="text-xs text-red-500 mt-2">
          Enable location services to submit reports
        </p>
      )}
    </form>
  );
};

export default RutForm;
