import React, { useEffect, useState, useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend 
} from "recharts";
import { format, addDays } from "date-fns";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { 
  generateOptimizedPredictions, 
  getCurrentRutPhase,
  getConfidenceLabel,
  type WeatherData,
  type LocationData,
  type ActivityPrediction
} from "@/utils/deerActivityPredictor";

interface RutPhase {
  name: string;
  dates: string;
  description: string;
  icon: string;
  slug: string;
}

interface RutPredictionsProps {
  data: any;
  weatherData: WeatherData | undefined;
  isLoading: boolean;
  location?: LocationData | null;
}

const RutPredictions: React.FC<RutPredictionsProps> = ({ data, weatherData, isLoading, location }) => {
  const [locationChanged, setLocationChanged] = useState(false);
  const [resetKey, setResetKey] = useState<number>(1);

  // Reset internal state when location changes
  useEffect(() => {
    console.log("Location changed in RutPredictions:", location?.county, location?.state);
    setLocationChanged(true);
    setResetKey(prev => prev + 1);
    const timer = setTimeout(() => setLocationChanged(false), 100);
    return () => clearTimeout(timer);
  }, [location?.state, location?.county]);

  // Memoized predictions for performance
  const optimizedPredictions = useMemo(() => {
    if (!weatherData || !location || !data?.activity) return null;
    
    // Get the base score from existing data (today's score)
    const todayScore = data.activity.find((act: any) => !act.isPrediction)?.score || 3;
    
    return generateOptimizedPredictions(weatherData, location, todayScore);
  }, [weatherData, location, data?.activity, resetKey]);

  if (isLoading || locationChanged) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-40 bg-gray-200 rounded mb-4"></div>
          <div className="h-6 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-28 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data || !weatherData || !optimizedPredictions) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4 flex items-center justify-center h-48">
        <p className="text-gray-500">{location ? `No prediction data available for ${location.county} County, ${location.state}` : "No prediction data available"}</p>
      </div>
    );
  }

  // Get star rating labels (keep existing implementation)
  const getStarRatingLabel = (score: number): string => {
    if (score >= 4.5) return "GET IN THE WOODS";
    if (score >= 3.5) return "Call In Sick";
    if (score >= 2.5) return "Good";
    if (score >= 1.5) return "Ok";
    return "Sleep In";
  };

  // Transform predictions for chart
  const combinedData = optimizedPredictions.map((prediction: ActivityPrediction, index: number) => {
    const weatherDay = weatherData.forecast && weatherData.forecast[index - 5]; // Adjust index for forecast array
    return {
      date: prediction.date,
      rutScore: prediction.score,
      confidence: prediction.confidence,
      temperature: weatherDay ? (weatherDay.high + weatherDay.low) / 2 : null,
      pressure: weatherDay ? weatherDay.pressure * 10 - 300 : null, // Scale for visualization
      prediction: prediction.isPrediction,
      ratingLabel: getStarRatingLabel(prediction.score),
      factors: prediction.factors
    };
  });

  // Current phase calculation
  const currentPhase = getCurrentRutPhase();
  const getNextPhase = (currentPhase: string) => {
    const phases = ["Pre-Rut", "Seeking Phase", "Chasing Phase", "Peak Breeding", "Post-Rut", "Second Rut", "Off Season"];
    const currentIndex = phases.indexOf(currentPhase);
    return currentIndex < phases.length - 1 ? phases[currentIndex + 1] : phases[0];
  };

  const getDaysToNextPhase = (currentPhase: string) => {
    const today = new Date();
    const month = today.getMonth();
    const date = today.getDate();
    
    switch(currentPhase) {
      case "Off Season": {
        if (month < 9 || (month === 9 && date < 15)) {
          const target = new Date(today.getFullYear(), 9, 15);
          return Math.ceil((target.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        }
        return 0;
      }
      case "Pre-Rut": {
        const target = new Date(today.getFullYear(), 9, 25);
        return Math.max(0, Math.ceil((target.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));
      }
      case "Seeking Phase": {
        const target = new Date(today.getFullYear(), 10, 5);
        return Math.max(0, Math.ceil((target.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));
      }
      case "Chasing Phase": {
        const target = new Date(today.getFullYear(), 10, 10);
        return Math.max(0, Math.ceil((target.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));
      }
      case "Peak Breeding": {
        const target = new Date(today.getFullYear(), 10, 19);
        return Math.max(0, Math.ceil((target.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));
      }
      case "Post-Rut": {
        const target = new Date(today.getFullYear(), 11, 5);
        return Math.max(0, Math.ceil((target.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));
      }
      case "Second Rut": {
        const target = new Date(today.getFullYear(), 11, 16);
        return Math.max(0, Math.ceil((target.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));
      }
      default: return 0;
    }
  };

  const nextPhase = getNextPhase(currentPhase);
  const daysToNext = getDaysToNextPhase(currentPhase);

  // Updated rut phases with new timeline and slug for navigation
  const rutPhases: RutPhase[] = [
    { 
      name: "Pre-Rut", 
      dates: "Oct 15 - Oct 24", 
      description: "Bucks establishing territories with scrapes and rubs", 
      icon: "calendar-check",
      slug: "pre-rut"
    },
    { 
      name: "Seeking Phase", 
      dates: "Oct 25 - Nov 4", 
      description: "Bucks actively seeking does, increasing home range", 
      icon: "search",
      slug: "seeking-phase"
    },
    { 
      name: "Chasing Phase", 
      dates: "Nov 5 - Nov 9", 
      description: "Intense chasing activity, does beginning to come into estrus", 
      icon: "arrow-up-right",
      slug: "chasing-phase"
    },
    { 
      name: "Peak Breeding", 
      dates: "Nov 10 - Nov 18", 
      description: "Maximum breeding activity, bucks locked down with does", 
      icon: "target",
      slug: "peak-breeding"
    },
    { 
      name: "Post-Rut", 
      dates: "Nov 19 - Nov 30", 
      description: "Breeding activity declining, bucks recovering", 
      icon: "arrow-down-right",
      slug: "post-rut"
    },
    { 
      name: "Second Rut", 
      dates: "Dec 5 - Dec 15", 
      description: "Unfertilized does return to estrus, secondary breeding period", 
      icon: "repeat",
      slug: "second-rut"
    }
  ];

  // Generate AI insights based on weather and rut data
  const generateInsights = () => {
    let insights = "";
    
    if (currentPhase === "Pre-Rut") {
      insights = "Bucks are establishing territories with increased scraping and rubbing. ";
    } else if (currentPhase === "Seeking Phase") {
      insights = "Bucks are actively seeking does and expanding their home range. Focus on rub lines and fresh scrapes. ";
    } else if (currentPhase === "Chasing Phase") {
      insights = "Intense chasing activity as bucks pursue does coming into estrus. Movement peaks during daylight hours. ";
    } else if (currentPhase === "Peak Breeding") {
      insights = "Many bucks locked down with does. Look for lone does or check doe bedding areas. ";
    } else if (currentPhase === "Post-Rut") {
      insights = "Breeding activity declining. Bucks are recovering and returning to feeding patterns. ";
    } else if (currentPhase === "Second Rut") {
      insights = "Unfertilized does and doe fawns coming into estrus. Secondary breeding activity occurring. ";
    } else {
      insights = "Off-season. Focus on scouting and preparation for next rut. ";
    }
    
    if (weatherData.nextColdFront && weatherData.nextColdFront.tempDrop > 8) {
      insights += `The significant cold front arriving on ${weatherData.nextColdFront.date} with a ${weatherData.nextColdFront.tempDrop}°F drop will trigger increased deer movement. `;
    } else if (weatherData.pressureTrend === "rising") {
      insights += "Rising pressure indicates favorable movement conditions. ";
    } else if (weatherData.pressureTrend === "falling") {
      insights += "Falling pressure may reduce movement until stabilized. ";
    }
    
    if (weatherData.currentTemp < 30) {
      insights += "In these cold conditions, midday movement will increase. ";
    } else if (weatherData.currentTemp > 55) {
      insights += "Warmer temperatures suggest focusing on dawn and dusk periods. ";
    }
    
    if (currentPhase === "Pre-Rut" || currentPhase === "Seeking Phase") {
      insights += "Focus on travel corridors between bedding and feeding areas.";
    } else if (currentPhase === "Chasing Phase" || currentPhase === "Peak Breeding") {
      insights += "Doe bedding areas and pinch points will be most productive.";
    } else {
      insights += "Food sources will be the key attraction areas now.";
    }
    
    return insights;
  };

  const starRatingLegend = [
    { score: 1, label: "Sleep In" },
    { score: 2, label: "Ok" },
    { score: 3, label: "Good" },
    { score: 4, label: "Call In Sick" },
    { score: 5, label: "GET IN THE WOODS" },
  ];

  return (
    <div className="space-y-4" key={resetKey}>
      {/* Location display */}
      {location && (
        <div className="bg-gray-50 px-3 py-2 rounded-md border border-gray-100 mb-2">
          <p className="text-sm text-gray-700">Optimized AI predictions for <span className="font-medium">{location.county} County, {location.state}</span></p>
        </div>
      )}

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-md flex items-center justify-between">
            <span>Enhanced AI Activity Prediction</span>
            <div className="text-xs text-green-600 font-normal bg-green-50 px-2 py-1 rounded-full">
              Accuracy: {getConfidenceLabel(optimizedPredictions[5]?.confidence || 0.8)}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-xs text-gray-500 mb-3">
            Advanced forecasting using weather patterns, lunar cycles, rut phases, and location-specific data
          </div>
          
          {/* Star Rating Legend */}
          <div className="mb-3 bg-gray-50 p-2 rounded-md">
            <div className="text-xs font-medium mb-1">Activity Rating Legend</div>
            <div className="flex flex-wrap gap-2">
              {starRatingLegend.map((rating) => (
                <div key={rating.score} className="flex items-center gap-1">
                  <span className="inline-flex items-center justify-center w-5 h-5 bg-forest text-white rounded-full text-xs font-medium">
                    {rating.score}
                  </span>
                  <span className="text-xs">
                    {rating.label}
                  </span>
                </div>
              ))}
            </div>
          </div>
          
          <ResponsiveContainer width="100%" height={220}>
            <LineChart data={combinedData} margin={{ top: 5, right: 5, left: -20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#eee" />
              <XAxis dataKey="date" tick={{ fontSize: 11 }} />
              <YAxis yAxisId="left" domain={[0, 5]} ticks={[0, 1, 2, 3, 4, 5]} tick={{ fontSize: 11 }} />
              <YAxis yAxisId="right" orientation="right" hide={true} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.9)', 
                  borderRadius: '4px', 
                  borderColor: '#eee' 
                }}
                formatter={(value, name, props) => {
                  if (name === "Deer Activity") {
                    const dataPoint = props?.payload;
                    const label = dataPoint ? dataPoint.ratingLabel : getStarRatingLabel(Number(value));
                    const confidence = dataPoint ? `${Math.round(dataPoint.confidence * 100)}%` : '';
                    return [`${value} - ${label} (${confidence})`, "Activity Rating"];
                  }
                  if (name === "Temp (°F)") return [`${value}°F`, "Temperature"];
                  if (name === "Pressure") {
                    const numericValue = typeof value === 'number' ? value : parseFloat(String(value));
                    return [(((numericValue + 300) / 10).toFixed(2)), "Pressure (inHg)"];
                  }
                  return [value, name];
                }}
                labelFormatter={label => `Date: ${label}`}
              />
              <Legend iconType="circle" iconSize={8} wrapperStyle={{ fontSize: '11px' }} />
              <Line 
                yAxisId="left"
                type="monotone" 
                dataKey="rutScore" 
                name="Deer Activity" 
                stroke="#2C5F2D" 
                strokeWidth={2}
                dot={{ r: 3 }} 
                activeDot={{ r: 5 }}
              />
              <Line 
                yAxisId="right"
                type="monotone" 
                dataKey="temperature" 
                name="Temp (°F)" 
                stroke="#95B8D1"
                strokeWidth={1.5}
                dot={{ r: 2 }}
              />
              <Line 
                yAxisId="right"
                type="monotone" 
                dataKey="pressure" 
                name="Pressure" 
                stroke="#B07156"
                strokeWidth={1.5}
                strokeDasharray="5 5"
                dot={{ r: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
          <div className="text-xs text-center mt-1 text-gray-500">
            Enhanced predictions with confidence scoring and multi-factor analysis
          </div>
        </CardContent>
      </Card>
      
      {/* MOVED: Impact Factors Card - now appears before Rut Phase Forecast */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-md">Enhanced Impact Factors</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Today's prediction details */}
          {optimizedPredictions[5] && (
            <div className="mb-3 p-3 bg-blue-50 rounded-md border border-blue-100">
              <div className="text-xs font-medium mb-2">Today's Prediction Breakdown</div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>Rut Phase: <span className="font-medium">+{optimizedPredictions[5].factors.rut.toFixed(1)}</span></div>
                <div>Weather: <span className="font-medium">{optimizedPredictions[5].factors.weather >= 0 ? '+' : ''}{optimizedPredictions[5].factors.weather.toFixed(1)}</span></div>
                <div>Moon Phase: <span className="font-medium">{optimizedPredictions[5].factors.moon >= 0 ? '+' : ''}{optimizedPredictions[5].factors.moon.toFixed(1)}</span></div>
                <div>Confidence: <span className="font-medium">{Math.round(optimizedPredictions[5].confidence * 100)}%</span></div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-3 mb-3">
            <div className="bg-gray-50 p-3 rounded-md">
              <div className="text-xs font-medium mb-1">Weather Impact</div>
              <div className="flex items-center text-xs text-gray-600">
                <span className={`inline-block w-2 h-2 rounded-full mr-1 ${
                  weatherData.pressureTrend === "rising" ? "bg-green-500" : 
                  weatherData.pressureTrend === "falling" ? "bg-red-500" : "bg-yellow-500"
                }`}></span>
                {weatherData.pressureTrend === "rising" ? 
                  "Rising pressure (positive)" : 
                  weatherData.pressureTrend === "falling" ? 
                  "Falling pressure (negative)" : 
                  "Stable pressure (neutral)"}
              </div>
              <div className={`text-xs mt-2 ${weatherData.nextColdFront && weatherData.nextColdFront.tempDrop > 8 ? 'text-forest font-medium' : 'text-gray-600'}`}>
                {weatherData.nextColdFront && weatherData.nextColdFront.tempDrop > 8 ? 
                  `Strong cold front on ${weatherData.nextColdFront.date} (+${Math.min(0.5, weatherData.nextColdFront.tempDrop/30).toFixed(1)} rating)` : 
                  "No significant fronts coming"}
              </div>
            </div>
            
            <div className="bg-gray-50 p-3 rounded-md">
              <div className="text-xs font-medium mb-1">Moon Phase Impact</div>
              <div className="text-xs text-gray-600">
                {weatherData.moonPhase?.phase || "Unknown phase"}
              </div>
              {weatherData.moonPhase && (
                <div className="mt-2 text-xs">
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div className="bg-blue-600 h-1.5 rounded-full" style={{ width: `${weatherData.moonPhase.illumination}%` }}></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">{weatherData.moonPhase.illumination}% illumination</div>
                </div>
              )}
            </div>
          </div>
          
          {/* Rut Strategy Tips section - all updated to new phase names */}
          <div className="mt-3 p-3 bg-amber-50 rounded-md border border-amber-100">
            <div className="text-xs font-medium mb-1">Hunting Strategy for {currentPhase}</div>
            <ul className="text-xs text-gray-700 space-y-1 list-disc pl-4">
              {currentPhase === "Pre-Rut" && (
                <>
                  <li>Focus on fresh scrapes and rub lines</li>
                  <li>Set up near food sources during evening</li>
                  <li>Use buck grunt calls sparingly</li>
                </>
              )}
              {currentPhase === "Seeking Phase" && (
                <>
                  <li>Hunt travel corridors between bedding/feeding</li>
                  <li>Use grunt calls and light rattling</li>
                  <li>All-day sits can be productive</li>
                </>
              )}
              {currentPhase === "Chasing Phase" && (
                <>
                  <li>Focus on pinch points and funnels</li>
                  <li>Use aggressive calling tactics</li>
                  <li>Hunt all day - midday movement increases</li>
                </>
              )}
              {currentPhase === "Peak Breeding" && (
                <>
                  <li>Focus on doe bedding areas</li>
                  <li>Use doe bleats to attract locked-down bucks</li>
                  <li>Hunt near areas with good visibility</li>
                </>
              )}
              {currentPhase === "Post-Rut" && (
                <>
                  <li>Focus on high-protein food sources</li>
                  <li>Pattern recovered bucks</li>
                  <li>Hunt during prime weather days</li>
                </>
              )}
              {currentPhase === "Second Rut" && (
                <>
                  <li>Watch for new scraping activity</li>
                  <li>Focus on areas with doe fawns</li>
                  <li>Use light grunt calls and rattling</li>
                </>
              )}
              {currentPhase === "Off Season" && (
                <>
                  <li>Scout and prepare for next season</li>
                  <li>Place trail cameras to pattern deer</li>
                  <li>Identify buck bedding areas</li>
                </>
              )}
            </ul>
          </div>
        </CardContent>
      </Card>
      
      {/* Rut Phase Forecast card - now appears after Impact Factors */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-md flex items-center justify-between">
            <span>Rut Phase Forecast</span>
            <span className="text-xs text-green-600 font-normal bg-green-50 px-2 py-1 rounded-full">
              {format(new Date(), "MMM d, yyyy")}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-4">
            <div className="text-sm mr-2">Current Phase:</div>
            <div className="bg-forest/10 text-forest px-3 py-1 rounded-full text-sm font-medium">
              {currentPhase}
            </div>
            <div className="text-xs text-gray-500 ml-auto">
              {nextPhase} {daysToNext > 0 ? `in ${daysToNext} days` : "in progress"}
            </div>
          </div>
          
          {/* Updated mobile-friendly grid for the rut phase buttons */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2 mb-4">
            {rutPhases.map((phase, i) => (
              <Link 
                key={i} 
                to={`/rut-tracker/phase/${phase.slug}`}
                className="block"
              >
                <Button
                  variant={phase.name === currentPhase ? "default" : "outline"}
                  className={`w-full h-auto py-2 px-1 text-center flex flex-col items-center justify-center ${
                    phase.name === currentPhase
                      ? 'bg-forest hover:bg-forest/90' 
                      : 'bg-gray-100 hover:bg-gray-200 border-0'
                  }`}
                >
                  <div className="text-xs font-medium">{phase.name}</div>
                  <div className={`text-[10px] mt-1 ${phase.name === currentPhase ? 'text-white/80' : 'text-gray-500'}`}>
                    {phase.dates}
                  </div>
                </Button>
              </Link>
            ))}
          </div>
          
          {/* Enhanced phase details */}
          {rutPhases.filter(phase => phase.name === currentPhase).map((phase, i) => (
            <div key={i} className="bg-forest/5 p-3 rounded-md mb-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-forest">Current Phase: {phase.name}</h4>
                <Link to={`/rut-tracker/phase/${phase.slug}`}>
                  <Button size="sm" variant="link" className="text-forest p-0 h-auto">
                    Details
                  </Button>
                </Link>
              </div>
              <p className="text-xs text-gray-700 mt-2">{phase.description}</p>
              
              <div className="mt-3 grid grid-cols-2 gap-2">
                <div className="bg-white p-2 rounded border border-gray-100">
                  <div className="text-xs font-medium">Buck Behavior</div>
                  <div className="text-xs text-gray-600 mt-1">
                    {currentPhase === "Pre-Rut" && "Making rubs and scrapes, establishing territory"}
                    {currentPhase === "Seeking Phase" && "Expanding home range, searching for does"}
                    {currentPhase === "Chasing Phase" && "Actively chasing does, reduced feeding"}
                    {currentPhase === "Peak Breeding" && "Locked down with does, breeding activity"}
                    {currentPhase === "Post-Rut" && "Recovering energy, returning to feeding patterns"}
                    {currentPhase === "Second Rut" && "Renewed searching for unbred does"}
                    {currentPhase === "Off Season" && "Focused on feeding, growing antlers, in bachelor groups"}
                  </div>
                </div>
                <div className="bg-white p-2 rounded border border-gray-100">
                  <div className="text-xs font-medium">Doe Behavior</div>
                  <div className="text-xs text-gray-600 mt-1">
                    {currentPhase === "Pre-Rut" && "Normal feeding patterns, becoming more alert"}
                    {currentPhase === "Seeking Phase" && "Still in groups, becoming restless"}
                    {currentPhase === "Chasing Phase" && "First does entering estrus, breaking from groups"}
                    {currentPhase === "Peak Breeding" && "Many does in estrus, isolated with bucks"}
                    {currentPhase === "Post-Rut" && "Most does bred, returning to normal patterns"}
                    {currentPhase === "Second Rut" && "Unbred does and doe fawns entering estrus"}
                    {currentPhase === "Off Season" && "Raising fawns or in family groups"}
                  </div>
                </div>
              </div>
              
              <div className="mt-3">
                <Link to={`/rut-tracker/phase/${phase.slug}`} className="w-full">
                  <Button className="w-full bg-forest hover:bg-forest/90">
                    View Full Phase Details
                  </Button>
                </Link>
              </div>
            </div>
          ))}
          
          <div className="mt-2">
            <h4 className="text-sm font-medium mb-2">Enhanced AI Insights</h4>
            <p className="text-xs text-gray-600 bg-gray-50 p-3 rounded-md">
              {generateInsights()}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RutPredictions;
