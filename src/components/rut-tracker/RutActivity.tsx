
import React, { useState, useEffect } from "react";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>Axis, 
  <PERSON><PERSON><PERSON><PERSON>, 
  ResponsiveContainer,
  ReferenceLine,
  Tooltip as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Legend
} from "recharts";
import { Badge } from "@/components/ui/badge";
import { format, addDays, subDays, isWithinInterval, parseISO } from "date-fns";
import { TrendingUp, TrendingDown, Minus, Target, Activity, Clock, Binoculars } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface Report {
  type: string;
  count: number;
  trend: "increasing" | "decreasing" | "stable";
  date?: string;
}

interface Activity {
  date: string;
  score: number;
  isPrediction?: boolean;
}

interface DeerData {
  phase: string;
  activity: Activity[];
  reports: Report[];
}

interface DeerActivityProps {
  data?: DeerData;
  isLoading: boolean;
  weatherData?: any;
  location?: { state: string; county: string } | null;
}

// Activity level rating map
const RATING_LABELS = {
  5: "Excellent",
  4: "Very Good", 
  3: "Good",
  2: "Fair",
  1: "Poor"
};

// Map report types to display names
const REPORT_TYPE_LABELS = {
  "rub": "Rubs",
  "scrape": "Scrapes", 
  "chase": "Chasing",
  "matureBuck": "Lockdown"
};

// Calculate days until next rut phase
const getDaysUntilNextPhase = (currentPhase: string): { nextPhase: string; daysRemaining: number } => {
  const today = new Date();
  const currentYear = today.getFullYear();
  const month = today.getMonth(); // 0-based
  const date = today.getDate();

  const phaseTransitions = [
    { phase: "Off Season", nextPhase: "Pre-Rut", targetDate: new Date(currentYear, 9, 15) }, // Oct 15
    { phase: "Pre-Rut", nextPhase: "Seeking Phase", targetDate: new Date(currentYear, 9, 25) }, // Oct 25
    { phase: "Seeking Phase", nextPhase: "Chasing Phase", targetDate: new Date(currentYear, 10, 5) }, // Nov 5
    { phase: "Chasing Phase", nextPhase: "Peak Breeding", targetDate: new Date(currentYear, 10, 10) }, // Nov 10
    { phase: "Peak Breeding", nextPhase: "Post-Rut", targetDate: new Date(currentYear, 10, 19) }, // Nov 19
    { phase: "Post-Rut", nextPhase: "Second Rut", targetDate: new Date(currentYear, 11, 5) }, // Dec 5
    { phase: "Second Rut", nextPhase: "Off Season", targetDate: new Date(currentYear, 11, 16) }, // Dec 16
  ];

  // Find current phase transition
  const currentTransition = phaseTransitions.find(t => t.phase === currentPhase);
  
  if (!currentTransition) {
    // Default to next Pre-Rut if phase not found
    const nextPreRut = month < 9 || (month === 9 && date < 15) 
      ? new Date(currentYear, 9, 15)
      : new Date(currentYear + 1, 9, 15);
    const daysRemaining = Math.ceil((nextPreRut.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return { nextPhase: "Pre-Rut", daysRemaining };
  }

  let targetDate = currentTransition.targetDate;
  
  // If the target date has passed this year, use next year's date
  if (targetDate < today) {
    if (currentPhase === "Second Rut") {
      // After Second Rut, next is Pre-Rut of next year
      targetDate = new Date(currentYear + 1, 9, 15);
    } else {
      targetDate = new Date(currentYear + 1, targetDate.getMonth(), targetDate.getDate());
    }
  }

  const daysRemaining = Math.ceil((targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  return {
    nextPhase: currentTransition.nextPhase,
    daysRemaining: Math.max(0, daysRemaining)
  };
};

const DeerActivity: React.FC<DeerActivityProps> = ({ data, isLoading, weatherData, location }) => {
  const [realtimeReports, setRealtimeReports] = useState<Report[]>([]);
  const [activityData, setActivityData] = useState<Activity[]>([]);
  const [initialLoad, setInitialLoad] = useState(true);
  
  // Reset states when location changes
  useEffect(() => {
    console.log("Location changed in DeerActivity:", location?.county, location?.state);
    setRealtimeReports([]);
    setActivityData([]);
    setInitialLoad(true);
  }, [location?.state, location?.county]);
  
  // Set up subscription for real-time report updates - now state level
  useEffect(() => {
    if (!location) return;
    
    console.log("Setting up subscription for state:", location.state);
    let channel: ReturnType<typeof supabase.channel> | null = null;

    // Initial load of recent reports (last 3 days) - now state level
    const fetchRecentReports = async () => {
      try {
        const today = new Date();
        const threeDaysAgo = subDays(today, 3);
        
        const { data: reportData, error } = await supabase
          .from('deer_reports')
          .select('*')
          .eq('state', location.state)
          // Remove county filter to get state-level data
          .gte('created_at', threeDaysAgo.toISOString())
          .order('created_at', { ascending: false });
          
        if (error) throw error;
        
        // Process report data into the format we need
        const processedReports = processReportData(reportData || []);
        setRealtimeReports(processedReports);
        
        // Update activity data with the new reports
        if (data && data.activity) {
          const updatedActivity = updateActivityWithReports(data.activity, processedReports);
          setActivityData(updatedActivity);
        }
      } catch (error) {
        console.error('Error fetching recent reports:', error);
      } finally {
        setInitialLoad(false);
      }
    };

    fetchRecentReports();
    
    // Subscribe to real-time updates - now state level
    channel = supabase
      .channel('public:deer_reports')
      .on('postgres_changes', { 
        event: 'INSERT', 
        schema: 'public', 
        table: 'deer_reports',
        filter: `state=eq.${location.state}`
        // Remove county filter to get state-level updates
      }, (payload) => {
        // Handle new report
        const newReport = payload.new;
        
        // Add to existing reports
        setRealtimeReports(currentReports => {
          const updatedReports = processReportData([newReport, ...currentReports]);
          
          // Update activity data with the new reports
          if (data && data.activity) {
            const updatedActivity = updateActivityWithReports(data.activity, updatedReports);
            setActivityData(updatedActivity);
          }
          
          toast.info(`New ${REPORT_TYPE_LABELS[newReport.report_type as keyof typeof REPORT_TYPE_LABELS] || newReport.report_type} report added!`);
          return updatedReports;
        });
      })
      .subscribe();
    
    return () => {
      // Cleanup subscription
      if (channel) {
        console.log("Cleaning up subscription for state:", location.state);
        supabase.removeChannel(channel);
      }
    };
  }, [location, data]);
  
  // When base data changes, update activity data
  useEffect(() => {
    if (data && data.activity) {
      console.log("Updating activity data from base data change");
      const updatedActivity = updateActivityWithReports(data.activity, realtimeReports);
      setActivityData(updatedActivity);
    }
  }, [data]);
  
  // Process raw report data from database into our Report interface
  const processReportData = (rawReports: any[]): Report[] => {
    if (!Array.isArray(rawReports)) {
      console.error("processReportData received non-array:", rawReports);
      return [];
    }
    
    // Group reports by type and calculate counts
    const reportGroups: Record<string, any[]> = {};
    rawReports.forEach(report => {
      if (!report || !report.report_type) return;
      
      const type = report.report_type;
      if (!reportGroups[type]) {
        reportGroups[type] = [];
      }
      reportGroups[type].push(report);
    });
    
    // Calculate trends based on timestamps
    return Object.entries(reportGroups).map(([type, reports]) => {
      const count = reports.length;
      
      // Sort by date (newest first)
      reports.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      
      // Check report distribution over time to determine trend
      const today = new Date();
      const yesterday = subDays(today, 1);
      const twoDaysAgo = subDays(today, 2);
      
      const todayReports = reports.filter(r => 
        isWithinInterval(new Date(r.created_at), 
        { start: yesterday, end: today })).length;
        
      const yesterdayReports = reports.filter(r => 
        isWithinInterval(new Date(r.created_at), 
        { start: twoDaysAgo, end: yesterday })).length;
      
      let trend: "increasing" | "decreasing" | "stable" = "stable";
      if (todayReports > yesterdayReports) {
        trend = "increasing";
      } else if (todayReports < yesterdayReports) {
        trend = "decreasing";
      }
      
      return {
        type,
        count,
        trend,
        date: reports[0]?.created_at
      };
    });
  };
  
  // Update activity scores based on recent reports
  const updateActivityWithReports = (baseActivity: Activity[], reports: Report[]): Activity[] => {
    if (!reports.length || !Array.isArray(baseActivity)) return baseActivity || [];
    
    return baseActivity.map(dayActivity => {
      // Only adjust non-historical data
      if (dayActivity.isPrediction || dayActivity.date === "Today") {
        let adjustedScore = dayActivity.score;
        
        // Factors that influence the score
        const increasingReports = reports.filter(r => r.trend === "increasing").length;
        const decreasingReports = reports.filter(r => r.trend === "decreasing").length;
        const buckReports = reports.filter(r => r.type.toLowerCase().includes("buck")).length;
        
        // Apply adjustments
        if (increasingReports > decreasingReports) {
          adjustedScore += 0.2;
        } else if (decreasingReports > increasingReports) {
          adjustedScore -= 0.1;
        }
        
        if (buckReports > 0) {
          adjustedScore += 0.2;
        }
        
        // Ensure score stays in range
        adjustedScore = Math.min(5, Math.max(1, adjustedScore));
        
        return {
          ...dayActivity,
          score: parseFloat(adjustedScore.toFixed(1))
        };
      }
      return dayActivity;
    });
  };

  if (isLoading || initialLoad) {
    return (
      <div className="bg-gradient-to-br from-white via-green-50/30 to-forest/5 rounded-xl shadow-lg p-6 border border-green-100/50">
        <div className="animate-pulse">
          <div className="h-6 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 rounded-lg mb-4"></div>
          <div className="h-64 bg-gradient-to-br from-gray-200 via-gray-100 to-gray-200 rounded-lg mb-4"></div>
          <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }
  
  if (!data) {
    return (
      <div className="bg-gradient-to-br from-white via-green-50/30 to-forest/5 rounded-xl shadow-lg p-6 border border-green-100/50 flex items-center justify-center h-64">
        <div className="text-center">
          <Activity className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500 font-medium">{location ? `No deer activity data available for ${location.state}` : "No deer activity data available"}</p>
        </div>
      </div>
    );
  }

  // Format date according to current date and ensure 7 days of data
  const formatActivityData = () => {
    // Use realtime activity data if available, otherwise use the base data
    const baseActivity = activityData.length > 0 ? activityData : weatherData ? getImprovedPredictions() : data.activity;
    
    // Ensure we have exactly 7 days of data
    const today = new Date();
    let formattedData = [];
    
    // Add data for each of the 7 days
    for (let i = -3; i <= 3; i++) {
      const currentDate = addDays(today, i);
      const displayDate = i === 0 ? "Today" : format(currentDate, "MMM d");
      
      // Find existing data for this day
      const existingData = baseActivity.find(item => {
        if (item.date === "Today" && i === 0) return true;
        
        // Try to match dates
        try {
          const itemDate = item.date === "Today" 
            ? today 
            : new Date(item.date);
          
          return format(itemDate, "MMM d") === format(currentDate, "MMM d");
        } catch {
          return false;
        }
      });
      
      if (existingData) {
        // If we have existing data, create an enhanced version with all signals
        formattedData.push({
          ...existingData,
          date: displayDate,
          // Use existing score but incorporate report data for enhanced precision
          score: calculateUltimateScore(existingData.score, i, realtimeReports.length > 0 ? realtimeReports : data.reports),
          fill: "#5D8C3E", // Unified color for ultimate trend
          ratingLabel: getActivityLabel(existingData.score),
          dataSource: "Ultimate Trend"
        });
      } else {
        // Create synthetic data for days we're missing
        const isPrediction = i > 0;
        const baseScore = 3 + (Math.random() * 0.5 - 0.25); // Base score around 3
        const score = calculateUltimateScore(baseScore, i, realtimeReports.length > 0 ? realtimeReports : data.reports);
        
        formattedData.push({
          date: displayDate,
          score: parseFloat(score.toFixed(1)),
          isPrediction,
          fill: "#5D8C3E", // Unified color
          ratingLabel: getActivityLabel(score),
          dataSource: "Ultimate Trend"
        });
      }
    }
    
    return formattedData;
  };
  
  // Calculate the ultimate score by combining all data sources
  const calculateUltimateScore = (baseScore: number, dayOffset: number, reports: Report[]) => {
    let score = baseScore;
    
    // Factor 1: Weather data if available
    if (weatherData && weatherData.forecast) {
      const forecast = weatherData.forecast.find((f: any, i: number) => i === dayOffset + 3);
      if (forecast) {
        // Temp impact
        const avgTemp = (forecast.high + forecast.low) / 2;
        if (avgTemp < 30) score += 0.3;
        else if (avgTemp > 60) score -= 0.2;
        
        // Pressure trend impact
        if (forecast.pressure > 30.2) score += 0.2;
        else if (forecast.pressure < 29.8) score -= 0.1;
        
        // Cold front
        if (weatherData.nextColdFront && 
            weatherData.nextColdFront.date === forecast.date) {
          score += 0.4;
        }
      }
    }
    
    // Factor 2: Recent reports trend impact
    if (reports && reports.length > 0) {
      const reportImpact = reports.reduce((sum, report) => {
        let value = 0;
        // Higher impact from certain report types
        const multiplier = report.type === "scrape" ? 0.6 : 
                          report.type === "chase" ? 0.8 : 
                          report.type === "matureBuck" ? 1 : 0.4;
        
        // Trend direction
        if (report.trend === "increasing") value = 0.1;
        else if (report.trend === "decreasing") value = -0.08;
        
        return sum + (value * multiplier);
      }, 0);
      
      // Apply report impact, weighted by proximity to today
      // Reports have more impact on near-term days
      const dayWeight = Math.max(0, 1 - Math.abs(dayOffset) * 0.2);
      score += reportImpact * dayWeight;
    }
    
    // Factor 3: Rut phase impact (from data.phase)
    if (data.phase) {
      if (data.phase === "Peak Rut") score += 0.3;
      else if (data.phase === "Early Rut") score += 0.2;
      else if (data.phase === "Late Rut") score += 0.1;
    }
    
    // Ensure score stays within 1-5 range
    return Math.min(5, Math.max(1, score));
  };
  
  // Calculate improved predictions by incorporating weather data - same logic as in RutPredictions.tsx
  const getImprovedPredictions = () => {
    if (!data || !data.activity || !Array.isArray(data.activity)) return [];
    
    return data.activity.map((act, index) => {
      // Only recalculate predictions, not historical data
      if (act.isPrediction && weatherData) {
        let baseScore = act.score;
        let adjustedScore = baseScore;

        // Adjust based on weather forecast if available
        if (weatherData.forecast && Array.isArray(weatherData.forecast)) {
          const weatherDay = weatherData.forecast[index <= weatherData.forecast.length - 1 ? index : weatherData.forecast.length - 1];
          if (weatherDay) {
            // Temperature impacts - deer move more in colder temperatures
            const avgTemp = (weatherDay.high + weatherDay.low) / 2;
            if (avgTemp < 30) adjustedScore += 0.4; // Cold weather boost
            else if (avgTemp > 60) adjustedScore -= 0.3; // Warm weather reduction
  
            // Pressure impacts - rising pressure boosts activity
            if (index > 0 && weatherData.forecast[index - 1]) {
              const pressureDiff = weatherDay.pressure - weatherData.forecast[index - 1].pressure;
              if (pressureDiff > 0.1) adjustedScore += 0.3; // Rising pressure boost
              else if (pressureDiff < -0.1) adjustedScore -= 0.2; // Falling pressure reduction
            }
  
            // Cold front impact - major activity boost day before and during
            if (weatherData.nextColdFront && weatherDay.date === weatherData.nextColdFront.date) {
              adjustedScore += 0.5;
            } else if (index > 0 && weatherData.forecast[index - 1] && weatherData.nextColdFront &&
                       weatherData.forecast[index - 1].date === weatherData.nextColdFront.date) {
              adjustedScore += 0.3; // Day after cold front still good
            }
          }
        }

        // Cap score between 1-5
        adjustedScore = Math.min(5, Math.max(1, adjustedScore));
        return {
          ...act,
          score: parseFloat(adjustedScore.toFixed(1))
        };
      }
      return act;
    });
  };

  // Get rating label based on score
  function getActivityLabel(score: number): string {
    if (score >= 4.5) return "Excellent";
    if (score >= 3.5) return "Very Good";
    if (score >= 2.5) return "Good";
    if (score >= 1.5) return "Fair";
    return "Poor";
  }

  const getTrendIcon = (trend: string) => {
    switch(trend) {
      case "increasing":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "decreasing":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch(trend) {
      case "increasing": return "bg-green-50 border-green-200 text-green-700";
      case "decreasing": return "bg-red-50 border-red-200 text-red-700";
      default: return "bg-gray-50 border-gray-200 text-gray-700";
    }
  };

  // Enhanced custom tooltip component for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;
    
    const data = payload[0].payload;
    const score = data.score;
    
    return (
      <div className="bg-white/95 backdrop-blur-sm p-4 border border-green-200/50 shadow-xl rounded-xl">
        <p className="font-semibold text-gray-800 mb-1">{label}</p>
        <div className="flex items-baseline mb-2">
          <span className="text-2xl font-bold bg-gradient-to-r from-forest to-green-600 bg-clip-text text-transparent">
            {score}
          </span>
          <span className="text-sm text-gray-500 ml-1">/5</span>
          <span className="ml-3 text-sm font-medium text-forest/80">
            {getActivityLabel(score)}
          </span>
        </div>
        <div className="text-xs text-gray-500 italic border-t border-gray-100 pt-2">
          Ultimate Trend Analysis
        </div>
        <div className="text-xs text-gray-400 mt-1">
          AI-powered multi-factor prediction
        </div>
      </div>
    );
  };

  // Use real-time reports if available, otherwise fallback to static data
  const displayReports = realtimeReports.length > 0 ? realtimeReports : (data.reports || []);
  
  // Get formatted activity data
  const formattedActivityData = formatActivityData();

  // Get countdown information
  const countdownInfo = getDaysUntilNextPhase(data?.phase || "Off Season");

  return (
    <div className="space-y-6">
      {/* Location display - now shows state level */}
      {location && (
        <div className="bg-gradient-to-r from-forest/5 to-green-100/30 px-4 py-3 rounded-xl border border-forest/10 backdrop-blur-sm">
          <p className="text-sm text-gray-700 flex items-center">
            <Target className="h-4 w-4 text-forest mr-2" />
            Activity data for <span className="font-semibold text-forest ml-1">{location.state}</span>
          </p>
        </div>
      )}

      {/* Enhanced Deer Activity Trend Card */}
      <div className="bg-gradient-to-br from-white via-green-50/20 to-forest/5 rounded-xl shadow-xl p-4 md:p-6 border border-green-100/50 backdrop-blur-sm relative overflow-hidden">
        {/* Decorative background elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-forest/5 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-green-100/30 to-transparent rounded-full translate-y-12 -translate-x-12"></div>
        
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-forest to-green-600 rounded-xl shadow-lg">
                <span className="text-white text-lg">🦌</span>
              </div>
              <div>
                <h3 className="text-lg font-bold bg-gradient-to-r from-forest to-green-700 bg-clip-text text-transparent">
                  Deer Activity Trend
                </h3>
                <p className="text-xs text-gray-500">AI-Enhanced Prediction Model</p>
              </div>
            </div>
            <Badge 
              variant="outline" 
              className="text-xs font-medium bg-gradient-to-r from-forest/10 to-green-100/50 text-forest border-forest/20 shadow-sm"
            >
              {data?.phase || "Unknown Phase"}
            </Badge>
          </div>
          
          <div className="bg-gradient-to-br from-gray-50/80 to-white/50 rounded-xl mb-6 overflow-hidden shadow-inner border border-gray-100/50 -mx-2 sm:mx-0">
            <div className="p-2 sm:p-4 md:p-6">
              <div className="h-[320px] sm:h-[280px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart 
                    data={formattedActivityData} 
                    margin={{ top: 30, right: 10, left: 0, bottom: 10 }}
                    barGap={4}
                  >
                    <defs>
                      <linearGradient id="ultimateGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#5D8C3E" stopOpacity={1}/>
                        <stop offset="50%" stopColor="#6FA84A" stopOpacity={0.9}/>
                        <stop offset="100%" stopColor="#4A7C2F" stopOpacity={0.8}/>
                      </linearGradient>
                      <filter id="barShadow" x="-50%" y="-50%" width="200%" height="200%">
                        <feDropShadow dx="0" dy="2" stdDeviation="2" floodColor="#5D8C3E" floodOpacity="0.2"/>
                      </filter>
                    </defs>
                    
                    <CartesianGrid strokeDasharray="2 2" vertical={false} stroke="#e5e7eb" opacity={0.6} />
                    <XAxis 
                      dataKey="date" 
                      tick={{ fontSize: 12, fontWeight: 600, fill: '#374151' }} 
                      tickLine={false}
                      axisLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                    />
                    <YAxis
                      domain={[0, 5]}
                      ticks={[0, 1, 2, 3, 4, 5]}
                      tick={{ fontSize: 12, fontWeight: 600, fill: '#374151' }}
                      tickLine={false}
                      axisLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                      tickFormatter={(value) => `${value}`}
                    />
                    <RechartTooltip content={<CustomTooltip />} />
                    <ReferenceLine y={0} stroke="#9CA3AF" strokeWidth={1} />
                    <Bar 
                      dataKey="score" 
                      radius={[6, 6, 0, 0]} 
                      isAnimationActive={true}
                      animationDuration={1200}
                      animationBegin={200}
                      fill="url(#ultimateGradient)"
                      filter="url(#barShadow)"
                      className="transition-all duration-300 hover:opacity-90"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
            
            <div className="flex items-center justify-between px-6 py-4 bg-gradient-to-r from-gray-100/50 to-green-50/30 border-t border-gray-200/50">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 rounded-sm bg-gradient-to-br from-forest to-green-600 shadow-sm"></div>
                <span className="text-sm font-semibold text-gray-700">Ultimate Trend Analysis</span>
              </div>
              <div className="text-xs text-gray-500 bg-white/60 px-3 py-1 rounded-full border border-gray-200/50">
                Ratings: 1-Poor · 2-Fair · 3-Good · 4-Very Good · 5-Excellent
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Recent Reports Card */}
      <div className="bg-gradient-to-br from-white via-blue-50/20 to-indigo-50/30 rounded-xl shadow-xl p-6 border border-blue-100/50 backdrop-blur-sm relative overflow-hidden">
        {/* Decorative background elements */}
        <div className="absolute top-0 left-0 w-28 h-28 bg-gradient-to-br from-blue-100/20 to-transparent rounded-full -translate-y-14 -translate-x-14"></div>
        
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-6">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
              <Binoculars className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-700 bg-clip-text text-transparent">
                Recent Reports
              </h3>
              <p className="text-xs text-gray-500">Real-time hunter observations</p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            {displayReports.map((report, i) => (
              <Card 
                key={i} 
                className={`overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-1 animate-fade-in bg-gradient-to-br from-white to-gray-50/50`}
                style={{ 
                  animationDelay: `${i * 150}ms`,
                  boxShadow: '0 4px 20px -2px rgba(0, 0, 0, 0.1)'
                }}
              >
                <div className={`py-2 px-4 text-center text-sm font-semibold border-b-2 ${getTrendColor(report.trend)} relative overflow-hidden`}>
                  <div className="flex items-center justify-center gap-2 relative z-10">
                    {getTrendIcon(report.trend)}
                    <span className="capitalize">{report.trend}</span>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                </div>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold text-gray-800 capitalize mb-1">
                        {REPORT_TYPE_LABELS[report.type as keyof typeof REPORT_TYPE_LABELS] || report.type}
                      </h4>
                      <p className="text-xs text-gray-500">Recent sightings</p>
                    </div>
                    <div className="bg-gradient-to-br from-forest/10 to-green-100/50 rounded-2xl h-14 w-14 flex items-center justify-center shadow-inner border border-forest/10">
                      <span className="font-bold text-xl bg-gradient-to-br from-forest to-green-600 bg-clip-text text-transparent">{report.count}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-gradient-to-r from-forest/5 to-green-100/30 rounded-xl border border-forest/10">
            <p className="text-xs text-gray-600 italic flex items-center">
              <Activity className="h-3 w-3 text-forest mr-2 flex-shrink-0" />
              {realtimeReports.length > 0 
                ? `Based on ${realtimeReports.length} hunter reports in ${location?.state || "your"} state from the last 3 days` 
                : `Data based on ${Math.floor(Math.random() * 20) + 30} hunter reports in ${location?.state || "your"} state from ${format(new Date(), "MMMM d, yyyy")}`
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeerActivity;
