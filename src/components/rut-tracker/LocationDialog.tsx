
import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";
import { Search, ChevronDown } from "lucide-react";
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover";
import { usStates } from "@/lib/us-states";

// Define the form schema with Zod
const locationFormSchema = z.object({
  state: z.string().min(2, {
    message: "State must be at least 2 characters.",
  }),
  county: z.string().min(2, {
    message: "County must be at least 2 characters.",
  }),
});

type LocationFormValues = z.infer<typeof locationFormSchema>;

interface LocationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onLocationChange: (location: { state: string; county: string }) => void;
  defaultValues?: { state: string; county: string };
  isAddingLocation?: boolean;
}

const LocationDialog = ({ 
  open, 
  onOpenChange, 
  onLocationChange, 
  defaultValues,
  isAddingLocation = true 
}: LocationDialogProps) => {
  const form = useForm<LocationFormValues>({
    resolver: zodResolver(locationFormSchema),
    defaultValues: defaultValues || {
      state: "",
      county: "",
    },
  });
  
  const [statePopoverOpen, setStatePopoverOpen] = useState(false);
  const [countyPopoverOpen, setCountyPopoverOpen] = useState(false);
  const [filteredCounties, setFilteredCounties] = useState<string[]>([]);
  const selectedState = form.watch("state");
  const formInitialized = useRef(false);

  // Reset form when dialog opens or when defaultValues change
  useEffect(() => {
    if (open) {
      console.log("LocationDialog opened with defaultValues:", defaultValues);
      // Reset to default values when dialog opens
      form.reset(defaultValues || {
        state: "",
        county: "",
      });
      formInitialized.current = true;
    }
  }, [open, form, defaultValues]);

  // Reset county field and filter counties when state changes
  useEffect(() => {
    if (!formInitialized.current) return;
    
    // Reset county field when state changes
    form.setValue("county", "");
    
    // Filter counties based on selected state
    if (selectedState) {
      const state = usStates.find(s => s.name === selectedState);
      if (state) {
        setFilteredCounties(state.counties);
      } else {
        setFilteredCounties([]);
      }
    } else {
      setFilteredCounties([]);
    }
  }, [selectedState, form]);

  const onSubmit = (data: LocationFormValues) => {
    console.log("LocationDialog submitting:", data);
    
    // Ensure data is properly formatted
    const locationData = {
      state: data.state.trim(),
      county: data.county.trim()
    };
    
    // Close dialog first to prevent any UI issues during updates
    onOpenChange(false);
    
    // Pass location data to parent component
    setTimeout(() => {
      onLocationChange(locationData);
      
      // Show success message
      toast.success(isAddingLocation ? "Location added successfully" : "Location updated successfully");
    }, 10);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{isAddingLocation ? "Add Location" : "Change Location"}</DialogTitle>
          <DialogDescription>
            {isAddingLocation 
              ? "Add a new location to see deer activity and reports from another area." 
              : "Enter a new location to see deer activity and reports from another area."}
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="state"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>State</FormLabel>
                  <Popover open={statePopoverOpen} onOpenChange={setStatePopoverOpen}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          className={`w-full justify-between ${!field.value && "text-muted-foreground"}`}
                        >
                          {field.value || "Select state"}
                          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Search state..." />
                        <CommandEmpty>No state found.</CommandEmpty>
                        <CommandList>
                          <CommandGroup>
                            {usStates.map((state) => (
                              <CommandItem
                                key={state.name}
                                value={state.name}
                                onSelect={() => {
                                  form.setValue("state", state.name);
                                  setStatePopoverOpen(false);
                                }}
                              >
                                {state.name}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="county"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>County</FormLabel>
                  <Popover 
                    open={countyPopoverOpen && selectedState !== ""} 
                    onOpenChange={(open) => {
                      if (selectedState === "") return;
                      setCountyPopoverOpen(open);
                    }}
                  >
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          disabled={!selectedState}
                          className={`w-full justify-between ${(!field.value || !selectedState) && "text-muted-foreground"}`}
                        >
                          {field.value || (selectedState ? "Select county" : "Select state first")}
                          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput placeholder="Search county..." />
                        <CommandEmpty>No county found.</CommandEmpty>
                        <CommandList>
                          <CommandGroup>
                            {filteredCounties.map((county) => (
                              <CommandItem
                                key={county}
                                value={county}
                                onSelect={() => {
                                  form.setValue("county", county);
                                  setCountyPopoverOpen(false);
                                }}
                              >
                                {county}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter className="pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                className="bg-forest hover:bg-forest/90 flex items-center gap-2"
              >
                <Search size={16} />
                {isAddingLocation ? "Add Location" : "Update Location"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default LocationDialog;
