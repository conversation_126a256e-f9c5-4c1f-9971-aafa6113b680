import React, { useEffect, useState, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Cloud, CloudDrizzle, CloudLightning, CloudRain, CloudSnow, CloudSun, Moon, Snowflake, Sun, Wind, Thermometer, TrendingUp, Target } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format, addDays } from "date-fns";

interface WindData {
  speed: number;
  direction: string;
}

interface ForecastDay {
  date: string;
  high: number;
  low: number;
  pressure: number;
  icon: string;
  wind?: {
    am: WindData;
    pm: WindData;
    maxGust: number;
  };
}

interface WeatherAlert {
  id: number;
  type: string;
  date: string;
  message: string;
  severity: "low" | "medium" | "high";
}

interface WeatherData {
  currentTemp: number;
  pressureTrend: "rising" | "falling" | "stable";
  nextColdFront: {
    date: string;
    tempDrop: number;
    pressureSpike: boolean;
  };
  forecast: ForecastDay[];
  alerts: WeatherAlert[];
  moonPhase?: {
    phase: string;
    illumination: number;
    isGood: boolean;
    message: string;
  };
}

interface WeatherAlertsProps {
  data?: WeatherData;
  isLoading: boolean;
  location?: { state: string; county: string } | null;
}

const WeatherAlerts: React.FC<WeatherAlertsProps> = ({ data, isLoading, location }) => {
  // Reset state when location changes
  const [resetKey, setResetKey] = useState<number>(1);
  
  // More robust location change detection
  const locationString = location ? `${location.county} ${location.state}` : 'none';
  
  useEffect(() => {
    console.log("Location changed in WeatherAlerts:", locationString);
    console.log("Weather data alerts:", data?.alerts);
    // Reset state when location changes
    setResetKey(prevKey => prevKey + 1);
  }, [locationString, data?.alerts]);
  
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-28 bg-gray-200 rounded mb-4"></div>
          <div className="h-6 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-28 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4 flex items-center justify-center h-48">
        <p className="text-gray-500">{location ? `No weather data available for ${location.county} County, ${location.state}` : "No weather data available"}</p>
      </div>
    );
  }

  const getWeatherIcon = (iconName: string) => {
    switch (iconName) {
      case "sun": return <Sun className="text-amber-500" />;
      case "cloud-sun": return <CloudSun className="text-sky-400" />;
      case "cloud": return <Cloud className="text-gray-400" />;
      case "cloud-drizzle": return <CloudDrizzle className="text-sky-300" />;
      case "cloud-rain": return <CloudRain className="text-sky-500" />;
      case "cloud-snow": return <CloudSnow className="text-blue-200" />;
      case "snowflake": return <Snowflake className="text-blue-200" />;
      case "cloud-lightning": return <CloudLightning className="text-amber-400" />;
      default: return <CloudSun className="text-sky-400" />;
    }
  };

  const getSeverityColor = (severity: "low" | "medium" | "high") => {
    switch (severity) {
      case "low": return "bg-blue-50 border-blue-200 text-blue-700";
      case "medium": return "bg-amber-50 border-amber-200 text-amber-700";
      case "high": return "bg-forest/10 border-forest/30 text-forest";
      default: return "bg-forest/10 border-forest/30 text-forest";
    }
  };

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case "cold-front": return <CloudSun className="h-4 w-4" />;
      case "wind": return <Wind className="h-4 w-4" />;
      case "precipitation": return <CloudRain className="h-4 w-4" />;
      case "pressure": return <TrendingUp className="h-4 w-4" />;
      case "temperature": return <Thermometer className="h-4 w-4" />;
      case "optimal": return <Target className="h-4 w-4" />;
      case "storm-front": return <CloudLightning className="h-4 w-4" />;
      default: return <CloudRain className="h-4 w-4" />;
    }
  };

  const getAlertTitle = (alertType: string) => {
    switch (alertType) {
      case "cold-front": return "Cold Front Alert";
      case "wind": return "Wind Alert";
      case "precipitation": return "Precipitation Alert";
      case "pressure": return "Pressure Alert";
      case "temperature": return "Temperature Alert";
      case "optimal": return "Optimal Conditions";
      case "storm-front": return "Storm Front Alert";
      default: return "Weather Alert";
    }
  };

  // Format date according to current date
  const formatDateForDisplay = (dayOffset: number): string => {
    if (dayOffset === 0) return "Today";
    return format(addDays(new Date(), dayOffset), "MMM d");
  };

  // Get top 3 alerts by severity priority
  const getTopAlerts = (alerts: WeatherAlert[]) => {
    if (!alerts || !Array.isArray(alerts)) return [];
    
    // Sort alerts by severity priority: high > medium > low
    const severityOrder = { high: 3, medium: 2, low: 1 };
    const sortedAlerts = [...alerts].sort((a, b) => {
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
    
    // Return only top 3
    return sortedAlerts.slice(0, 3);
  };

  // Use key prop to force component to re-render when location changes
  return (
    <div className="space-y-4" key={`weather-alerts-${resetKey}-${locationString}`}>
      {/* Location display */}
      {location && (
        <div className="bg-gray-50 px-3 py-2 rounded-md border border-gray-100 mb-2">
          <p className="text-sm text-gray-700">Weather alerts for <span className="font-medium">{location.county} County, {location.state}</span></p>
        </div>
      )}
      
      {/* Weather Alerts Section */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-md">Active Weather Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          {data?.alerts && Array.isArray(data.alerts) && data.alerts.length > 0 ? (
            <div className="space-y-3">
              {getTopAlerts(data.alerts).map((alert, index) => (
                <Alert key={`alert-${alert.id || index}-${resetKey}`} className={getSeverityColor(alert.severity)}>
                  {getAlertIcon(alert.type)}
                  <AlertTitle className="font-medium">
                    {getAlertTitle(alert.type)}
                    {alert.date !== "Today" && (
                      <Badge variant="outline" className="ml-2 text-xs">
                        {alert.date}
                      </Badge>
                    )}
                  </AlertTitle>
                  <AlertDescription className="text-sm">
                    {alert.message}
                  </AlertDescription>
                </Alert>
              ))}
              {data.alerts.length > 3 && (
                <div className="text-xs text-gray-500 text-center py-2 border-t">
                  Showing top 3 of {data.alerts.length} alerts
                </div>
              )}
            </div>
          ) : (
            <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
              <p className="text-sm text-gray-700">No active weather alerts for this location</p>
              {data?.alerts && (
                <p className="text-xs text-gray-500 mt-1">
                  Debug: Found {Array.isArray(data.alerts) ? data.alerts.length : 'invalid'} alerts
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Enhanced Weather Forecast Card */}
      <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader className="pb-3 bg-gradient-to-r from-sky-50 to-blue-50 rounded-t-lg">
          <CardTitle className="text-lg font-bold text-gray-800 flex items-center">
            <Cloud className="h-5 w-5 mr-2 text-sky-600" />
            7-Day Weather Forecast
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {data?.forecast && Array.isArray(data.forecast) ? (
            <div className="space-y-0">
              {data.forecast.map((day, i) => (
                <div 
                  key={`forecast-${i}-${resetKey}`} 
                  className={`relative transition-all duration-300 hover:scale-[1.02] hover:z-10 ${
                    i !== data.forecast.length - 1 ? 'border-b border-gray-100' : ''
                  } ${
                    // Enhanced highlight for cold fronts
                    data?.nextColdFront?.date === day.date && data?.nextColdFront?.tempDrop >= 10
                      ? 'bg-gradient-to-r from-forest/5 via-forest/8 to-forest/5 shadow-md hover:shadow-lg border-l-4 border-l-forest' 
                      : 'bg-white hover:bg-gradient-to-r hover:from-gray-50 hover:to-white hover:shadow-md'
                  }`}
                >
                  <div className="p-5">
                    {/* Enhanced top row: Date, Icon, Temperature */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        {/* Enhanced Date Section */}
                        <div className="flex flex-col items-start min-w-[60px]">
                          <span className={`text-sm font-bold leading-tight ${
                            i === 0 ? 'text-forest' : 'text-gray-800'
                          }`}>
                            {formatDateForDisplay(i)}
                          </span>
                          {/* Enhanced Cold Front Badge */}
                          {data?.nextColdFront?.date === day.date && data?.nextColdFront?.tempDrop >= 10 && (
                            <Badge variant="outline" className="text-[10px] px-2 py-0.5 border-forest bg-forest/10 text-forest font-semibold mt-1.5 leading-none shadow-sm">
                              ❄️ Cold Front
                            </Badge>
                          )}
                        </div>

                        {/* Enhanced Weather Icon with background */}
                        <div className="bg-gradient-to-br from-sky-100 to-blue-100 rounded-full p-3 shadow-sm">
                          <div className="text-xl">
                            {getWeatherIcon(day.icon)}
                          </div>
                        </div>

                        {/* Enhanced Temperature Display */}
                        <div className="flex flex-col items-center">
                          <div className="text-lg font-bold text-gray-800 leading-tight">{day.high}°</div>
                          <div className="text-sm text-gray-500 leading-tight">{day.low}°</div>
                          <div className="w-8 h-1 bg-gradient-to-r from-blue-300 to-red-300 rounded-full mt-1"></div>
                        </div>
                      </div>

                      {/* Enhanced Pressure Display */}
                      <div className="flex flex-col items-end bg-gray-50 rounded-lg p-2 min-w-[70px]">
                        <div className="text-[10px] text-gray-500 mb-1 leading-none font-medium">PRESSURE</div>
                        <div className="text-sm font-bold text-gray-700 leading-tight">{day.pressure.toFixed(2)}"</div>
                        <div className="text-[8px] text-gray-400 leading-none mt-0.5">inHg</div>
                      </div>
                    </div>

                    {/* Enhanced Wind Information */}
                    {day.wind && (
                      <div className="bg-gradient-to-r from-blue-50 to-amber-50 rounded-lg p-3 shadow-sm">
                        <div className="flex items-center justify-between text-xs mb-2">
                          <div className="flex items-center text-blue-700 bg-blue-100 rounded-full px-2 py-1">
                            <Wind className="h-3 w-3 mr-1 flex-shrink-0" />
                            <span className="font-semibold mr-1">AM:</span>
                            <span className="font-medium">{day.wind.am.direction} {day.wind.am.speed}</span>
                          </div>
                          <div className="flex items-center text-amber-700 bg-amber-100 rounded-full px-2 py-1">
                            <Wind className="h-3 w-3 mr-1 flex-shrink-0" />
                            <span className="font-semibold mr-1">PM:</span>
                            <span className="font-medium">{day.wind.pm.direction} {day.wind.pm.speed}</span>
                          </div>
                        </div>
                        {day.wind.maxGust > 20 && (
                          <div className="text-[10px] text-red-600 bg-red-50 rounded-full px-2 py-1 text-center leading-none font-medium">
                            ⚠️ Max Gust: {day.wind.maxGust} mph
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center text-gray-500">
              <Cloud className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p>Forecast data unavailable</p>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Moon Phase Alert Card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-md">Moon Phase Alert</CardTitle>
        </CardHeader>
        <CardContent>
          {data?.moonPhase ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Moon className="h-6 w-6 text-sky-700 mr-2" />
                  <h3 className="font-medium">{data.moonPhase.phase}</h3>
                </div>
                <Badge variant={data.moonPhase.isGood ? "default" : "outline"} className={data.moonPhase.isGood ? "bg-forest text-white" : ""}>
                  {data.moonPhase.isGood ? "Favorable" : "Less Favorable"}
                </Badge>
              </div>
              
              <div className="bg-gray-50 p-3 rounded-md">
                <div className="mb-2">
                  <span className="text-sm font-medium">Moon Illumination:</span>
                  <div className="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                    <div 
                      className="bg-sky-700 h-2.5 rounded-full" 
                      style={{ width: `${data.moonPhase.illumination}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between mt-1 text-xs text-gray-500">
                    <span>New Moon</span>
                    <span>{data.moonPhase.illumination}%</span>
                    <span>Full Moon</span>
                  </div>
                </div>
              </div>
              
              <div className="text-sm text-gray-600 mb-2">
                {data.moonPhase.message}
              </div>
              
              {/* Enhanced Moon Phase Predictions */}
              <div className="border-t pt-2">
                <h4 className="text-xs font-medium text-gray-700 mb-1">Hunting Recommendations</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  {data.moonPhase.phase === "New Moon" && (
                    <>
                      <li className="flex items-start">
                        <span className="text-forest font-medium mr-1">•</span>
                        <span>Focus on food sources during daylight hours</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-forest font-medium mr-1">•</span>
                        <span>Deer movement peaks at dawn and dusk with minimal moonlight</span>
                      </li>
                    </>
                  )}
                  {data.moonPhase.phase === "Waxing Crescent" && (
                    <>
                      <li className="flex items-start">
                        <span className="text-forest font-medium mr-1">•</span>
                        <span>Morning hunts most productive as deer move overnight</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-forest font-medium mr-1">•</span>
                        <span>Afternoon feeding patterns becoming more predictable</span>
                      </li>
                    </>
                  )}
                  {data.moonPhase.phase === "Half Moon" && (
                    <>
                      <li className="flex items-start">
                        <span className="text-gray-500 font-medium mr-1">•</span>
                        <span>Transitional movement patterns - less predictable</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-forest font-medium mr-1">•</span>
                        <span>Focus on funnels between bedding and feeding areas</span>
                      </li>
                    </>
                  )}
                  {data.moonPhase.phase === "Waxing Gibbous" && (
                    <>
                      <li className="flex items-start">
                        <span className="text-gray-500 font-medium mr-1">•</span>
                        <span>Increased nighttime visibility shifts movement later</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-forest font-medium mr-1">•</span>
                        <span>Morning hunts recommended - deer active pre-dawn</span>
                      </li>
                    </>
                  )}
                  {data.moonPhase.phase === "Full Moon" && (
                    <>
                      <li className="flex items-start">
                        <span className="text-gray-500 font-medium mr-1">•</span>
                        <span>Primarily nocturnal movement due to bright nights</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-forest font-medium mr-1">•</span>
                        <span>Target thick cover near feeding areas for midday movement</span>
                      </li>
                    </>
                  )}
                </ul>
                
                <div className="mt-2 text-xs bg-gray-50 rounded p-2 flex items-center">
                  <div className="w-2 h-2 rounded-full bg-forest mr-1.5"></div>
                  <span>
                    {data.moonPhase.isGood 
                      ? "Peak deer activity expected during legal hunting hours" 
                      : "Consider adjusting hunt times for peak movement"}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="py-6 text-center text-gray-500">
              <Moon className="h-6 w-6 mx-auto mb-2 text-gray-400" />
              <p>Moon phase data unavailable</p>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Buck Movement Conditions Card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-md">Buck Movement Conditions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="border rounded-md p-3">
              <div className="text-xs text-gray-500">Current Temperature</div>
              <div className="text-2xl font-medium">{data?.currentTemp}°F</div>
              {data?.nextColdFront?.tempDrop > 0 && (
                <div className="text-xs text-forest mt-1">
                  {data.nextColdFront.tempDrop}° drop coming {data.nextColdFront.date === "Today" ? "today" : `on ${data.nextColdFront.date}`}
                </div>
              )}
            </div>
            
            <div className="border rounded-md p-3">
              <div className="text-xs text-gray-500">Barometric Pressure</div>
              <div className="text-2xl font-medium">{data?.forecast && data.forecast[0]?.pressure.toFixed(2)}"</div>
              <div className={`text-xs ${
                data?.pressureTrend === "rising" ? "text-forest" : 
                data?.pressureTrend === "falling" ? "text-orange-500" : "text-gray-500"
              } mt-1`}>
                {data?.pressureTrend === "rising" ? "Rising (Good for movement)" : 
                 data?.pressureTrend === "falling" ? "Falling" : "Stable"}
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mt-4">
            {data?.forecast && data.forecast[0]?.wind && (
              <>
                <div className="border rounded-md p-3">
                  <div className="text-xs text-gray-500">Morning Wind</div>
                  <div className="flex items-center">
                    <Wind className="h-4 w-4 text-blue-600 mr-1" />
                    <div className="text-lg font-medium">
                      {data.forecast[0].wind.am.direction} {data.forecast[0].wind.am.speed} mph
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {data.forecast[0].wind.am.speed < 8 
                      ? "Light (Good for hunting)" 
                      : data.forecast[0].wind.am.speed < 15
                        ? "Moderate" 
                        : "Strong (May limit movement)"}
                  </div>
                </div>
                
                <div className="border rounded-md p-3">
                  <div className="text-xs text-gray-500">Evening Wind</div>
                  <div className="flex items-center">
                    <Wind className="h-4 w-4 text-amber-600 mr-1" />
                    <div className="text-lg font-medium">
                      {data.forecast[0].wind.pm.direction} {data.forecast[0].wind.pm.speed} mph
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {data.forecast[0].wind.pm.speed < 8 
                      ? "Light (Good for hunting)" 
                      : data.forecast[0].wind.pm.speed < 15
                        ? "Moderate" 
                        : "Strong (May limit movement)"}
                  </div>
                </div>
              </>
            )}
          </div>
          
          <div className="mt-4 bg-gray-50 rounded-md p-3">
            <h4 className="font-medium text-sm mb-1">Movement Analysis</h4>
            <p className="text-sm text-gray-600">
              {data?.nextColdFront?.date === "Today" 
                ? "Cold front arriving today with significant temperature drop. Expect increased mature buck movement, especially in the afternoon."
                : `${data?.pressureTrend === "rising" 
                    ? "Rising pressure indicates favorable movement conditions. " 
                    : data?.pressureTrend === "falling" 
                      ? "Falling pressure may reduce movement until stabilized. " 
                      : "Stable pressure provides consistent movement patterns. "}
                   ${data?.forecast && data?.forecast[0]?.wind 
                     ? `Wind is coming from the ${data.forecast[0].wind.am.direction} in the morning and ${data.forecast[0].wind.pm.direction} in the evening.` 
                     : "Wind data unavailable."}
                   ${data?.forecast && data?.forecast[0]?.wind?.maxGust > 20 ? "Strong gusts may affect deer movement in open areas. " : ""}
                   ${data?.nextColdFront ? `Next significant improvement expected on ${data.nextColdFront.date} with the cold front.` : ""}`
              }
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WeatherAlerts;
