
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { ClipboardCopy, Calendar, Users, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface InviteCode {
  id: string;
  code: string;
  created_at: string;
  expires_at: string | null;
  max_uses: number;
  remaining_uses: number;
  is_active: boolean;
}

const InviteCodeManager = () => {
  const [inviteCodes, setInviteCodes] = useState<InviteCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [generatingCode, setGeneratingCode] = useState(false);
  const [deletingCode, setDeletingCode] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      fetchInviteCodes();
    }
  }, [user]);

  const fetchInviteCodes = async () => {
    if (!user) return;

    try {
      setLoading(true);
      console.log('Fetching invite codes for user:', user.id);
      
      const { data, error } = await supabase
        .from('invite_codes')
        .select('*')
        .eq('created_by', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching invite codes:', error);
        toast.error('Failed to load invite codes');
        return;
      }

      console.log('Fetched invite codes:', data);
      setInviteCodes(data || []);
    } catch (error) {
      console.error('Error fetching invite codes:', error);
      toast.error('Failed to load invite codes');
    } finally {
      setLoading(false);
    }
  };

  const generateInviteCode = async () => {
    if (!user) return;

    try {
      setGeneratingCode(true);
      // Use the RPC function to generate a code with 5 uses instead of 1
      const { data: code, error } = await supabase.rpc('generate_invite_code', {
        user_id: user.id,
        num_uses: 5,
        days_valid: 7
      });

      if (error) {
        console.error('Error generating invite code:', error);
        toast.error('Failed to generate invite code');
        return;
      }

      toast.success('Invite code generated successfully!');
      // Refresh the list of codes
      await fetchInviteCodes();
    } catch (error) {
      console.error('Error generating invite code:', error);
      toast.error('Failed to generate invite code');
    } finally {
      setGeneratingCode(false);
    }
  };

  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code)
      .then(() => toast.success('Invite code copied to clipboard!'))
      .catch(() => toast.error('Failed to copy code'));
  };

  const deleteInviteCode = async (codeId: string) => {
    if (!user) return;

    try {
      setDeletingCode(codeId);
      
      console.log('Deleting invite code:', codeId, 'for user:', user.id);
      
      const { error } = await supabase
        .from('invite_codes')
        .delete()
        .eq('id', codeId)
        .eq('created_by', user.id);

      if (error) {
        console.error('Error deleting invite code:', error);
        toast.error(`Failed to delete invite code: ${error.message}`);
        return;
      }

      console.log('Invite code deleted successfully');
      
      // Update local state immediately
      setInviteCodes(prevCodes => prevCodes.filter(code => code.id !== codeId));
      toast.success('Invite code deleted successfully');
      
    } catch (error) {
      console.error('Error deleting invite code:', error);
      toast.error('Failed to delete invite code');
    } finally {
      setDeletingCode(null);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl">Invite Codes</CardTitle>
        <CardDescription>
          Generate and manage invite codes for friends
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          <div className="flex justify-between items-center">
            <Button 
              onClick={generateInviteCode} 
              disabled={generatingCode}
              className="bg-forest hover:bg-forest/80"
            >
              {generatingCode ? 'Generating...' : 'Generate New Invite Code'}
            </Button>
          </div>

          <div className="mt-4">
            <h3 className="font-medium mb-2">Your Invite Codes</h3>
            
            {loading ? (
              <div className="text-center py-4">Loading codes...</div>
            ) : inviteCodes.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                You haven't generated any invite codes yet.
              </div>
            ) : (
              <div className="space-y-3">
                {inviteCodes.map((invite) => (
                  <div key={invite.id} className="border rounded-md p-3 bg-background/50">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <div className="font-mono text-lg">{invite.code}</div>
                        <Badge variant={invite.is_active ? "default" : "outline"}>
                          {invite.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => copyToClipboard(invite.code)}
                        >
                          <ClipboardCopy size={16} />
                        </Button>
                        
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="text-destructive hover:text-destructive/90"
                              disabled={deletingCode === invite.id}
                            >
                              <Trash2 size={16} />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Invite Code</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete this invite code? This action cannot be undone and the code will no longer work.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction 
                                onClick={() => deleteInviteCode(invite.id)}
                                className="bg-destructive hover:bg-destructive/90"
                                disabled={deletingCode === invite.id}
                              >
                                {deletingCode === invite.id ? 'Deleting...' : 'Delete'}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap text-sm text-muted-foreground gap-x-4 gap-y-1">
                      <div className="flex items-center gap-1">
                        <Calendar size={14} />
                        Created: {format(new Date(invite.created_at), 'MMM d, yyyy')}
                      </div>
                      {invite.expires_at && (
                        <div className="flex items-center gap-1">
                          <Calendar size={14} />
                          Expires: {format(new Date(invite.expires_at), 'MMM d, yyyy')}
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <Users size={14} />
                        Uses: {invite.max_uses - invite.remaining_uses}/{invite.max_uses}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default InviteCodeManager;
