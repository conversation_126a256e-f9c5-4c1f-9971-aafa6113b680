
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Get Supabase URL and anon key from environment
// These values are automatically injected by Vite during build
const SUPABASE_URL = "https://fisvmdyzaeedwnfnrhpi.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpc3ZtZHl6YWVlZHduZm5yaHBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMzNTAwMDgsImV4cCI6MjA1ODkyNjAwOH0.OZ8qhgQtfB1N9DU8gN_7pLbf4znbOyFYK3kw3g4Q22E";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    storage: localStorage
  }
});
