export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      comment_likes: {
        Row: {
          comment_id: string | null
          created_at: string | null
          id: string
          user_id: string
        }
        Insert: {
          comment_id?: string | null
          created_at?: string | null
          id?: string
          user_id: string
        }
        Update: {
          comment_id?: string | null
          created_at?: string | null
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comment_likes_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
        ]
      }
      comments: {
        Row: {
          created_at: string | null
          id: string
          like_count: number
          text: string
          user_avatar: string | null
          user_id: string
          username: string
          video_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          like_count?: number
          text: string
          user_avatar?: string | null
          user_id: string
          username: string
          video_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          like_count?: number
          text?: string
          user_avatar?: string | null
          user_id?: string
          username?: string
          video_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comments_video_id_fkey"
            columns: ["video_id"]
            isOneToOne: false
            referencedRelation: "videos"
            referencedColumns: ["id"]
          },
        ]
      }
      deer_reports: {
        Row: {
          county: string
          created_at: string
          id: string
          notes: string | null
          report_type: string
          state: string
        }
        Insert: {
          county: string
          created_at?: string
          id?: string
          notes?: string | null
          report_type: string
          state: string
        }
        Update: {
          county?: string
          created_at?: string
          id?: string
          notes?: string | null
          report_type?: string
          state?: string
        }
        Relationships: []
      }
      group_chat_members: {
        Row: {
          group_chat_id: string
          id: string
          joined_at: string
          role: string
          user_id: string
        }
        Insert: {
          group_chat_id: string
          id?: string
          joined_at?: string
          role?: string
          user_id: string
        }
        Update: {
          group_chat_id?: string
          id?: string
          joined_at?: string
          role?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "group_chat_members_group_chat_id_fkey"
            columns: ["group_chat_id"]
            isOneToOne: false
            referencedRelation: "group_chats"
            referencedColumns: ["id"]
          },
        ]
      }
      group_chats: {
        Row: {
          avatar_url: string | null
          created_at: string
          created_by: string
          description: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          created_by: string
          description?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          created_by?: string
          description?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      invite_codes: {
        Row: {
          code: string
          created_at: string | null
          created_by: string | null
          expires_at: string | null
          id: string
          is_active: boolean | null
          is_admin_generated: boolean | null
          max_uses: number | null
          remaining_uses: number | null
        }
        Insert: {
          code: string
          created_at?: string | null
          created_by?: string | null
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          is_admin_generated?: boolean | null
          max_uses?: number | null
          remaining_uses?: number | null
        }
        Update: {
          code?: string
          created_at?: string | null
          created_by?: string | null
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          is_admin_generated?: boolean | null
          max_uses?: number | null
          remaining_uses?: number | null
        }
        Relationships: []
      }
      likes: {
        Row: {
          created_at: string | null
          id: string
          user_id: string
          video_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          user_id: string
          video_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          user_id?: string
          video_id?: string
        }
        Relationships: []
      }
      message_delivery_status: {
        Row: {
          id: string
          message_id: string
          status: string
          timestamp: string
          user_id: string
        }
        Insert: {
          id?: string
          message_id: string
          status?: string
          timestamp?: string
          user_id: string
        }
        Update: {
          id?: string
          message_id?: string
          status?: string
          timestamp?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "message_delivery_status_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
        ]
      }
      message_reactions: {
        Row: {
          created_at: string
          emoji: string
          id: string
          message_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          emoji: string
          id?: string
          message_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          emoji?: string
          id?: string
          message_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "message_reactions_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          attachments: Json | null
          content: string
          created_at: string
          edited: boolean
          edited_at: string | null
          group_chat_id: string | null
          id: string
          message_type: string
          read: boolean
          receiver_id: string | null
          reply_to_message_id: string | null
          sender_id: string
          updated_at: string
        }
        Insert: {
          attachments?: Json | null
          content: string
          created_at?: string
          edited?: boolean
          edited_at?: string | null
          group_chat_id?: string | null
          id?: string
          message_type?: string
          read?: boolean
          receiver_id?: string | null
          reply_to_message_id?: string | null
          sender_id: string
          updated_at?: string
        }
        Update: {
          attachments?: Json | null
          content?: string
          created_at?: string
          edited?: boolean
          edited_at?: string | null
          group_chat_id?: string | null
          id?: string
          message_type?: string
          read?: boolean
          receiver_id?: string | null
          reply_to_message_id?: string | null
          sender_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_messages_group_chat"
            columns: ["group_chat_id"]
            isOneToOne: false
            referencedRelation: "group_chats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_reply_to_message_id_fkey"
            columns: ["reply_to_message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          badges: string[] | null
          bio: string | null
          created_at: string
          featured_until: string | null
          full_name: string | null
          id: string
          is_featured: boolean | null
          is_premier_partner: boolean | null
          is_premium: boolean | null
          is_premium_service: boolean | null
          is_promoted: boolean | null
          premier_partner_until: string | null
          premium_expires_at: string | null
          premium_service_expires_at: string | null
          premium_until: string | null
          promoted_until: string | null
          region: string | null
          services: Json | null
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          updated_at: string
          username: string | null
          video_count: number | null
          website: string | null
        }
        Insert: {
          avatar_url?: string | null
          badges?: string[] | null
          bio?: string | null
          created_at?: string
          featured_until?: string | null
          full_name?: string | null
          id: string
          is_featured?: boolean | null
          is_premier_partner?: boolean | null
          is_premium?: boolean | null
          is_premium_service?: boolean | null
          is_promoted?: boolean | null
          premier_partner_until?: string | null
          premium_expires_at?: string | null
          premium_service_expires_at?: string | null
          premium_until?: string | null
          promoted_until?: string | null
          region?: string | null
          services?: Json | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string
          username?: string | null
          video_count?: number | null
          website?: string | null
        }
        Update: {
          avatar_url?: string | null
          badges?: string[] | null
          bio?: string | null
          created_at?: string
          featured_until?: string | null
          full_name?: string | null
          id?: string
          is_featured?: boolean | null
          is_premier_partner?: boolean | null
          is_premium?: boolean | null
          is_premium_service?: boolean | null
          is_promoted?: boolean | null
          premier_partner_until?: string | null
          premium_expires_at?: string | null
          premium_service_expires_at?: string | null
          premium_until?: string | null
          promoted_until?: string | null
          region?: string | null
          services?: Json | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string
          username?: string | null
          video_count?: number | null
          website?: string | null
        }
        Relationships: []
      }
      sound_library: {
        Row: {
          category: string | null
          created_at: string
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          sound_url: string
          tags: string[] | null
          title: string
        }
        Insert: {
          category?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          sound_url: string
          tags?: string[] | null
          title: string
        }
        Update: {
          category?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          sound_url?: string
          tags?: string[] | null
          title?: string
        }
        Relationships: []
      }
      subscription_plans: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          price_monthly: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          price_monthly: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          price_monthly?: number
          updated_at?: string
        }
        Relationships: []
      }
      trophies: {
        Row: {
          created_at: string
          description: string | null
          id: string
          image_url: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          image_url: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string
          user_id?: string
        }
        Relationships: []
      }
      user_invitations: {
        Row: {
          created_at: string | null
          id: string
          invite_code_id: string
          invited_user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          invite_code_id: string
          invited_user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          invite_code_id?: string
          invited_user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_invitations_invite_code_id_fkey"
            columns: ["invite_code_id"]
            isOneToOne: false
            referencedRelation: "invite_codes"
            referencedColumns: ["id"]
          },
        ]
      }
      user_shots: {
        Row: {
          created_at: string | null
          deer_view: string
          id: string
          is_dark_mode: boolean | null
          notes: string | null
          shot_analysis: Json
          shot_points: Json
          user_id: string
        }
        Insert: {
          created_at?: string | null
          deer_view: string
          id?: string
          is_dark_mode?: boolean | null
          notes?: string | null
          shot_analysis: Json
          shot_points: Json
          user_id: string
        }
        Update: {
          created_at?: string | null
          deer_view?: string
          id?: string
          is_dark_mode?: boolean | null
          notes?: string | null
          shot_analysis?: Json
          shot_points?: Json
          user_id?: string
        }
        Relationships: []
      }
      user_subscriptions: {
        Row: {
          created_at: string
          current_period_end: string | null
          current_period_start: string | null
          id: string
          plan_id: string
          status: string
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          plan_id: string
          status: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          plan_id?: string
          status?: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_subscriptions_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "subscription_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      video_reports: {
        Row: {
          created_at: string
          details: string | null
          id: string
          reason: string
          reporter_id: string
          status: string | null
          updated_at: string
          video_id: string
        }
        Insert: {
          created_at?: string
          details?: string | null
          id?: string
          reason: string
          reporter_id: string
          status?: string | null
          updated_at?: string
          video_id: string
        }
        Update: {
          created_at?: string
          details?: string | null
          id?: string
          reason?: string
          reporter_id?: string
          status?: string | null
          updated_at?: string
          video_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "video_reports_video_id_fkey"
            columns: ["video_id"]
            isOneToOne: false
            referencedRelation: "videos"
            referencedColumns: ["id"]
          },
        ]
      }
      videos: {
        Row: {
          additional_text: string | null
          caption: string | null
          color_filter: Json | null
          comments: number | null
          created_at: string | null
          game_type: string | null
          id: string
          is_promoted_service: boolean | null
          likes: number | null
          shares: number | null
          sound_library_id: string | null
          soundtrack: string | null
          soundtrack_data: Json | null
          tags: string[] | null
          user_avatar: string | null
          user_id: string
          username: string
          video_url: string
          views: number
        }
        Insert: {
          additional_text?: string | null
          caption?: string | null
          color_filter?: Json | null
          comments?: number | null
          created_at?: string | null
          game_type?: string | null
          id?: string
          is_promoted_service?: boolean | null
          likes?: number | null
          shares?: number | null
          sound_library_id?: string | null
          soundtrack?: string | null
          soundtrack_data?: Json | null
          tags?: string[] | null
          user_avatar?: string | null
          user_id: string
          username: string
          video_url: string
          views?: number
        }
        Update: {
          additional_text?: string | null
          caption?: string | null
          color_filter?: Json | null
          comments?: number | null
          created_at?: string | null
          game_type?: string | null
          id?: string
          is_promoted_service?: boolean | null
          likes?: number | null
          shares?: number | null
          sound_library_id?: string | null
          soundtrack?: string | null
          soundtrack_data?: Json | null
          tags?: string[] | null
          user_avatar?: string | null
          user_id?: string
          username?: string
          video_url?: string
          views?: number
        }
        Relationships: [
          {
            foreignKeyName: "videos_sound_library_id_fkey"
            columns: ["sound_library_id"]
            isOneToOne: false
            referencedRelation: "sound_library"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_comment_like: {
        Args: { comment_id_param: string; user_id_param: string }
        Returns: undefined
      }
      add_like: {
        Args: { video_id_param: string; user_id_param: string }
        Returns: undefined
      }
      admin_update_premium_status: {
        Args: {
          target_user_id: string
          is_premium: boolean
          admin_user_id: string
        }
        Returns: boolean
      }
      admin_update_user_badges: {
        Args: {
          target_user_id: string
          new_badges: string[]
          admin_user_id: string
        }
        Returns: boolean
      }
      admin_update_user_profile: {
        Args: {
          target_user_id: string
          new_username: string
          new_region: string
          new_bio: string
          admin_user_id: string
        }
        Returns: boolean
      }
      admin_update_user_services: {
        Args: {
          target_user_id: string
          new_services: Json
          admin_user_id: string
        }
        Returns: boolean
      }
      can_access_message: {
        Args: { message_id_param: string; user_id_param: string }
        Returns: boolean
      }
      check_comment_like: {
        Args: { comment_id_param: string; user_id_param: string }
        Returns: boolean
      }
      check_group_membership: {
        Args: { group_id: string; user_id: string }
        Returns: boolean
      }
      check_group_ownership: {
        Args: { group_id: string; user_id: string }
        Returns: boolean
      }
      check_user_like: {
        Args: { video_id_param: string; user_id_param: string }
        Returns: boolean
      }
      delete_video: {
        Args: { video_id_param: string; user_id_param: string }
        Returns: boolean
      }
      generate_invite_code: {
        Args: { user_id: string; num_uses?: number; days_valid?: number }
        Returns: string
      }
      get_whitetail_tier: {
        Args: { video_count: number }
        Returns: string
      }
      handle_promotion_subscription_update: {
        Args: {
          user_email: string
          stripe_customer_id: string
          subscription_status: string
          current_period_end: number
        }
        Returns: undefined
      }
      increment_video_views: {
        Args: { video_id_param: string }
        Returns: undefined
      }
      is_admin: {
        Args: { user_id: string }
        Returns: boolean
      }
      remove_comment_like: {
        Args: { comment_id_param: string; user_id_param: string }
        Returns: undefined
      }
      remove_like: {
        Args: { video_id_param: string; user_id_param: string }
        Returns: undefined
      }
      update_expired_premium_services: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      validate_and_use_invite_code: {
        Args: { code_text: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
