
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 120 15% 96%;
    --foreground: 120 10% 10%;

    --card: 0 0% 100%;
    --card-foreground: 120 15% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 120 15% 10%;

    --primary: 120 40% 28%;
    --primary-foreground: 120 5% 90%;

    --secondary: 30 25% 45%;
    --secondary-foreground: 30 5% 90%;

    --muted: 120 5% 90%;
    --muted-foreground: 120 5% 40%;

    --accent: 210 30% 53%;
    --accent-foreground: 210 5% 95%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 120 10% 85%;
    --input: 120 10% 85%;
    --ring: 120 40% 30%;

    --radius: 0.5rem;

    --sidebar-background: 120 15% 20%;
    --sidebar-foreground: 120 5% 90%;
    --sidebar-primary: 120 40% 40%;
    --sidebar-primary-foreground: 120 5% 90%;
    --sidebar-accent: 120 10% 25%;
    --sidebar-accent-foreground: 120 5% 90%;
    --sidebar-border: 120 10% 25%;
    --sidebar-ring: 120 40% 40%;
  }

  .dark {
    --background: 120 15% 12%;
    --foreground: 120 5% 90%;

    --card: 120 15% 15%;
    --card-foreground: 120 5% 90%;

    --popover: 120 15% 15%;
    --popover-foreground: 120 5% 90%;

    --primary: 120 40% 40%;
    --primary-foreground: 120 5% 10%;

    --secondary: 30 25% 45%;
    --secondary-foreground: 30 5% 10%;

    --muted: 120 15% 25%;
    --muted-foreground: 120 5% 70%;

    --accent: 210 30% 43%;
    --accent-foreground: 210 5% 95%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 120 15% 25%;
    --input: 120 15% 25%;
    --ring: 120 40% 40%;

    --sidebar-background: 120 15% 10%;
    --sidebar-foreground: 120 5% 90%;
    --sidebar-primary: 120 40% 40%;
    --sidebar-primary-foreground: 120 5% 10%;
    --sidebar-accent: 120 10% 15%;
    --sidebar-accent-foreground: 120 5% 90%;
    --sidebar-border: 120 10% 15%;
    --sidebar-ring: 120 40% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    min-height: -webkit-fill-available;
    height: 100%;
  }
  
  /* Home page special handling */
  .home-page {
    height: 100%;
    overflow: hidden;
    position: fixed;
    width: 100%;
  }
  
  /* Regular page scrolling - all pages except home */
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    min-height: -webkit-fill-available;
    overscroll-behavior-y: none;
  }
}

@layer components {
  .video-container {
    @apply relative w-full h-full overflow-hidden;
  }
  
  .action-button {
    @apply flex flex-col items-center justify-center mb-4 text-white;
  }
  
  .action-icon {
    @apply bg-black/30 rounded-full p-2 mb-1 backdrop-blur-sm hover:bg-black/40 border border-white/10;
  }
  
  .profile-circle {
    @apply w-12 h-12 rounded-full border-2 border-white overflow-hidden;
  }
  
  .plus-button {
    @apply absolute -bottom-5 left-1/2 -translate-x-1/2 text-white rounded-full w-5 h-5 flex items-center justify-center;
  }
  
  .nav-item {
    @apply flex flex-col items-center justify-center;
  }
  
  .nav-text {
    @apply text-xs mt-1;
  }

  .video-overlay-gradient {
    @apply absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-black/60 to-transparent;
  }
  
  /* New enhanced styles for username and profile photo */
  .profile-circle img {
    @apply transition-transform duration-300;
  }
  
  .profile-circle:hover img {
    @apply scale-110;
  }
  
  @keyframes pulse-border {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(255,255,255, 0.4);
    }
    50% {
      box-shadow: 0 0 0 5px rgba(255,255,255, 0.2);
    }
  }
  
  .pulse-border {
    animation: pulse-border 2s infinite;
  }
  
  /* Mobile improvements */
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  /* Page content container */
  .page-content {
    @apply pb-20 min-h-screen;
    height: auto;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}
