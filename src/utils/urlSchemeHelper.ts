/**
 * Utility functions for handling URL schemes and deep linking
 */

export interface DeepLinkData {
  type: 'video' | 'profile' | 'discover' | 'create' | 'activity' | 'home';
  id?: string;
  params?: Record<string, string>;
}

/**
 * Parse a URL scheme into structured data
 */
export const parseUrlScheme = (url: string): DeepLinkData | null => {
  try {
    // Check for empty or invalid URL
    if (!url || url.trim() === '') {
      console.warn('Empty URL provided to parseUrlScheme');
      return { type: 'home', params: {} };
    }

    const urlObj = new URL(url);
    const path = urlObj.pathname;
    const searchParams = urlObj.searchParams;

    // Handle empty or root paths
    if (!path || path === '/' || path.trim() === '') {
      return {
        type: 'home',
        params: Object.fromEntries(searchParams.entries())
      };
    }

    // Handle different URL patterns
    if (path.startsWith('/video/')) {
      const videoId = path.split('/video/')[1];
      return {
        type: 'video',
        id: videoId,
        params: Object.fromEntries(searchParams.entries())
      };
    }

    if (path.startsWith('/profile/')) {
      const userId = path.split('/profile/')[1];
      return {
        type: 'profile',
        id: userId,
        params: Object.fromEntries(searchParams.entries())
      };
    }

    if (path === '/discover') {
      return {
        type: 'discover',
        params: Object.fromEntries(searchParams.entries())
      };
    }

    if (path === '/create') {
      return {
        type: 'create',
        params: Object.fromEntries(searchParams.entries())
      };
    }

    if (path === '/activity') {
      return {
        type: 'activity',
        params: Object.fromEntries(searchParams.entries())
      };
    }

    // Handle query parameter based routing
    const action = searchParams.get('action');
    const videoId = searchParams.get('video');
    const userId = searchParams.get('user');

    if (action === 'share' && videoId) {
      return {
        type: 'video',
        id: videoId,
        params: Object.fromEntries(searchParams.entries())
      };
    }

    if (action === 'profile' && userId) {
      return {
        type: 'profile',
        id: userId,
        params: Object.fromEntries(searchParams.entries())
      };
    }

    // Default to home
    return {
      type: 'home',
      params: Object.fromEntries(searchParams.entries())
    };
  } catch (error) {
    console.error('Error parsing URL scheme:', error);
    return null;
  }
};

/**
 * Generate URL schemes for sharing
 */
export const generateUrlSchemes = (data: DeepLinkData) => {
  const baseCustomScheme = 'whitetaillivn://';
  const baseWebUrl = 'https://whitetaillivn.app';

  let path = '';
  let queryParams = new URLSearchParams();

  // Add custom parameters
  if (data.params) {
    Object.entries(data.params).forEach(([key, value]) => {
      queryParams.append(key, value);
    });
  }

  switch (data.type) {
    case 'video':
      path = data.id ? `/video/${data.id}` : '/';
      break;
    case 'profile':
      path = data.id ? `/profile/${data.id}` : '/profile';
      break;
    case 'discover':
      path = '/discover';
      break;
    case 'create':
      path = '/create';
      break;
    case 'activity':
      path = '/activity';
      break;
    default:
      path = '/';
  }

  const queryString = queryParams.toString();
  const pathWithQuery = queryString ? `${path}?${queryString}` : path;

  return {
    custom: `${baseCustomScheme}${pathWithQuery}`,
    web: `${baseWebUrl}${pathWithQuery}`,
    universal: `${baseWebUrl}${pathWithQuery}` // For universal links
  };
};

/**
 * Test URL schemes (development only)
 */
export const testUrlSchemes = () => {
  const testCases: DeepLinkData[] = [
    { type: 'video', id: 'test-video-123' },
    { type: 'profile', id: 'test-user-456' },
    { type: 'discover' },
    { type: 'create' },
    { type: 'activity' },
    { type: 'video', id: 'shared-video', params: { action: 'share', source: 'social' } },
    { type: 'home' }
  ];

  console.group('URL Scheme Test Cases');
  testCases.forEach((testCase, index) => {
    const urls = generateUrlSchemes(testCase);
    console.group(`Test Case ${index + 1}: ${testCase.type}${testCase.id ? ` (${testCase.id})` : ''}`);
    console.log('Custom Scheme:', urls.custom);
    console.log('Web URL:', urls.web);
    console.log('Universal Link:', urls.universal);
    console.groupEnd();
  });
  console.groupEnd();

  return testCases.map(testCase => generateUrlSchemes(testCase));
};

/**
 * Validate URL scheme format
 */
export const validateUrlScheme = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const validSchemes = ['whitetaillivn', 'https', 'http'];
    return validSchemes.includes(urlObj.protocol.replace(':', ''));
  } catch {
    return false;
  }
};

/**
 * Get navigation path from deep link data
 */
export const getNavigationPath = (data: DeepLinkData): string => {
  if (!data) {
    return '/';
  }

  switch (data.type) {
    case 'video':
      // Ensure video ID is not empty
      return data.id && data.id.trim() !== '' ? `/video/${data.id}` : '/';
    case 'profile':
      // Ensure user ID is not empty, fallback to general profile page
      return data.id && data.id.trim() !== '' ? `/profile/${data.id}` : '/profile';
    case 'discover':
      return '/discover';
    case 'create':
      return '/create';
    case 'activity':
      return '/activity';
    case 'home':
    default:
      return '/';
  }
};

/**
 * Log URL scheme handling for debugging
 */
export const logUrlSchemeHandling = (url: string, success: boolean, error?: string) => {
  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    url,
    success,
    error,
    parsed: parseUrlScheme(url)
  };

  console.log('URL Scheme Handling:', logData);

  // Store in sessionStorage for debugging (limit to last 10 entries)
  try {
    const existingLogs = JSON.parse(sessionStorage.getItem('urlSchemeLogs') || '[]');
    const updatedLogs = [logData, ...existingLogs].slice(0, 10);
    sessionStorage.setItem('urlSchemeLogs', JSON.stringify(updatedLogs));
  } catch (error) {
    console.warn('Could not store URL scheme log:', error);
  }
};

/**
 * Get URL scheme handling logs (for debugging)
 */
export const getUrlSchemeLogs = (): any[] => {
  try {
    return JSON.parse(sessionStorage.getItem('urlSchemeLogs') || '[]');
  } catch {
    return [];
  }
};

/**
 * Clear URL scheme handling logs
 */
export const clearUrlSchemeLogs = () => {
  try {
    sessionStorage.removeItem('urlSchemeLogs');
  } catch (error) {
    console.warn('Could not clear URL scheme logs:', error);
  }
};
