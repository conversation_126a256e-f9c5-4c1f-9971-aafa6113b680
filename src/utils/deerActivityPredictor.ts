
import { format, addDays, getMonth, getDate } from "date-fns";

export interface WeatherData {
  currentTemp: number;
  pressureTrend: "rising" | "falling" | "stable";
  nextColdFront: {
    date: string;
    tempDrop: number;
    pressureSpike: boolean;
  };
  forecast: Array<{
    date: string;
    high: number;
    low: number;
    pressure: number;
    icon: string;
    wind: {
      am: { speed: number; direction: string };
      pm: { speed: number; direction: string };
      maxGust: number;
    };
  }>;
  moonPhase?: {
    phase: string;
    illumination: number;
    isGood: boolean;
    message: string;
  };
}

export interface LocationData {
  state: string;
  county: string;
}

export interface ActivityPrediction {
  date: string;
  score: number;
  isPrediction?: boolean;
  confidence: number;
  factors: {
    weather: number;
    moon: number;
    rut: number;
    pressure: number;
    temperature: number;
  };
}

// Optimized rut phase calculator with more precise dates
export function getCurrentRutPhase(): string {
  const today = new Date();
  const month = getMonth(today); // 0-based
  const date = getDate(today);
  
  // More precise phase boundaries
  if (month === 9) { // October
    if (date >= 15 && date <= 24) return "Pre-Rut";
    if (date >= 25) return "Seeking Phase";
  } else if (month === 10) { // November
    if (date <= 4) return "Seeking Phase";
    if (date >= 5 && date <= 9) return "Chasing Phase";
    if (date >= 10 && date <= 18) return "Peak Breeding";
    if (date >= 19) return "Post-Rut";
  } else if (month === 11) { // December
    if (date <= 4) return "Post-Rut";
    if (date >= 5 && date <= 15) return "Second Rut";
  }
  
  return "Off Season";
}

// Enhanced rut scoring with location-specific adjustments
export function calculateRutScore(phase: string, location: LocationData): number {
  const today = new Date();
  const month = getMonth(today);
  const date = getDate(today);
  
  // Base scores for each phase with day-specific adjustments
  let baseScore = 1;
  
  switch (phase) {
    case "Pre-Rut":
      baseScore = 2.2 + (date - 15) * 0.1; // Gradually increasing
      break;
    case "Seeking Phase":
      baseScore = 3.0 + Math.sin((date - 25) * 0.5) * 0.3;
      break;
    case "Chasing Phase":
      baseScore = 4.2 + Math.sin((date - 5) * 0.8) * 0.4;
      break;
    case "Peak Breeding":
      baseScore = 4.8 - Math.abs(date - 14) * 0.05; // Peak around Nov 14
      break;
    case "Post-Rut":
      baseScore = 3.5 - (date - 19) * 0.08;
      break;
    case "Second Rut":
      baseScore = 3.8 - Math.abs(date - 10) * 0.1; // Peak around Dec 10
      break;
    default:
      baseScore = 1.2;
  }
  
  // Location-specific adjustments (latitude-based)
  const stateModifiers: Record<string, number> = {
    'Minnesota': 0.2, 'Wisconsin': 0.15, 'Michigan': 0.1,
    'Iowa': 0.05, 'Illinois': 0.0, 'Indiana': -0.05,
    'Missouri': -0.1, 'Kentucky': -0.15, 'Tennessee': -0.2,
    'Alabama': -0.3, 'Georgia': -0.35, 'Florida': -0.5
  };
  
  const locationModifier = stateModifiers[location.state] || 0;
  
  return Math.max(1, Math.min(5, baseScore + locationModifier));
}

// Enhanced weather impact calculator
export function calculateWeatherImpact(weatherData: WeatherData, dayIndex: number): number {
  if (!weatherData.forecast || dayIndex >= weatherData.forecast.length) return 0;
  
  const day = weatherData.forecast[dayIndex];
  let impact = 0;
  
  // Temperature impact (optimal range 35-55°F)
  const avgTemp = (day.high + day.low) / 2;
  if (avgTemp >= 35 && avgTemp <= 55) {
    impact += 0.4;
  } else if (avgTemp < 25 || avgTemp > 70) {
    impact -= 0.3;
  } else if (avgTemp < 35 || avgTemp > 55) {
    impact -= 0.1;
  }
  
  // Pressure impact
  if (day.pressure > 30.1) {
    impact += 0.3;
  } else if (day.pressure < 29.8) {
    impact -= 0.2;
  }
  
  // Pressure trend impact
  if (dayIndex > 0) {
    const prevPressure = weatherData.forecast[dayIndex - 1].pressure;
    const pressureChange = day.pressure - prevPressure;
    if (pressureChange > 0.05) impact += 0.2; // Rising pressure
    if (pressureChange < -0.05) impact -= 0.15; // Falling pressure
  }
  
  // Wind impact
  const maxWind = Math.max(day.wind.am.speed, day.wind.pm.speed);
  if (maxWind > 20) {
    impact -= 0.4;
  } else if (maxWind > 12) {
    impact -= 0.2;
  } else if (maxWind < 8) {
    impact += 0.1;
  }
  
  // Cold front impact
  if (weatherData.nextColdFront.date === day.date) {
    impact += Math.min(0.8, weatherData.nextColdFront.tempDrop * 0.05);
  }
  
  return Math.max(-1, Math.min(1, impact));
}

// Moon phase impact calculator
export function calculateMoonImpact(moonPhase?: WeatherData['moonPhase']): number {
  if (!moonPhase) return 0;
  
  const illumination = moonPhase.illumination;
  
  // Optimal illumination ranges for deer movement
  if (illumination < 15 || (illumination > 85 && illumination < 95)) {
    return 0.2; // New moon or very bright but not full
  } else if (illumination > 60 && illumination < 85) {
    return -0.3; // Too bright, pushes movement to night
  } else if (illumination >= 25 && illumination <= 40) {
    return 0.1; // Moderate illumination is slightly beneficial
  }
  
  return 0;
}

// Performance-optimized prediction generator with caching
const predictionCache = new Map<string, ActivityPrediction[]>();

export function generateOptimizedPredictions(
  weatherData: WeatherData,
  location: LocationData,
  historicalScore: number
): ActivityPrediction[] {
  const cacheKey = `${location.state}-${location.county}-${new Date().toDateString()}`;
  
  // Check cache first
  if (predictionCache.has(cacheKey)) {
    return predictionCache.get(cacheKey)!;
  }
  
  const predictions: ActivityPrediction[] = [];
  const currentPhase = getCurrentRutPhase();
  const baseRutScore = calculateRutScore(currentPhase, location);
  
  // Generate predictions for 11 days (5 past + today + 5 future)
  for (let i = -5; i <= 5; i++) {
    const date = addDays(new Date(), i);
    const dateStr = i === 0 ? "Today" : format(date, "MMM d");
    const isPrediction = i > 0;
    
    let score = baseRutScore;
    let confidence = 0.9;
    
    const factors = {
      weather: 0,
      moon: 0,
      rut: baseRutScore - 1, // Base rut contribution
      pressure: 0,
      temperature: 0
    };
    
    if (isPrediction && weatherData.forecast && i <= weatherData.forecast.length) {
      // Apply weather impacts for predictions
      const weatherImpact = calculateWeatherImpact(weatherData, i - 1);
      const moonImpact = calculateMoonImpact(weatherData.moonPhase);
      
      factors.weather = weatherImpact;
      factors.moon = moonImpact;
      
      // Detailed pressure and temperature factors
      const day = weatherData.forecast[i - 1];
      if (day) {
        factors.pressure = day.pressure > 30.1 ? 0.3 : day.pressure < 29.8 ? -0.2 : 0;
        const avgTemp = (day.high + day.low) / 2;
        factors.temperature = avgTemp >= 35 && avgTemp <= 55 ? 0.2 : -0.1;
      }
      
      score += weatherImpact + moonImpact;
      confidence = Math.max(0.6, confidence - Math.abs(i) * 0.05);
    } else if (!isPrediction) {
      // For historical data, add some variation based on the day
      const dayVariation = Math.sin(i + location.county.length) * 0.3;
      score += dayVariation;
      factors.weather = dayVariation * 0.5;
      confidence = 0.95; // High confidence for "historical" data
    }
    
    // Ensure score stays within bounds
    score = Math.max(1, Math.min(5, score));
    
    predictions.push({
      date: dateStr,
      score: Math.round(score * 10) / 10, // Round to 1 decimal
      isPrediction,
      confidence: Math.round(confidence * 100) / 100,
      factors
    });
  }
  
  // Cache the results for 5 minutes
  predictionCache.set(cacheKey, predictions);
  setTimeout(() => predictionCache.delete(cacheKey), 5 * 60 * 1000);
  
  return predictions;
}

// Performance utility to clear cache when needed
export function clearPredictionCache(): void {
  predictionCache.clear();
}

// Confidence calculator for UI display
export function getConfidenceLabel(confidence: number): string {
  if (confidence >= 0.9) return "Very High";
  if (confidence >= 0.8) return "High";
  if (confidence >= 0.7) return "Good";
  if (confidence >= 0.6) return "Moderate";
  return "Low";
}
