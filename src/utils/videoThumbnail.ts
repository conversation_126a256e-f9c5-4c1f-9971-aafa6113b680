/**
 * Utility functions for generating video thumbnails on mobile devices
 */

export interface ThumbnailOptions {
  quality?: number;
  timeOffset?: number;
  maxWidth?: number;
  maxHeight?: number;
}

/**
 * Generate a thumbnail from a video URL
 */
export const generateVideoThumbnail = async (
  videoUrl: string,
  options: ThumbnailOptions = {}
): Promise<string | null> => {
  const {
    quality = 0.8,
    timeOffset = 0.5,
    maxWidth = 400,
    maxHeight = 600
  } = options;

  console.log('Generating thumbnail for:', videoUrl, 'with options:', options);

  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.crossOrigin = 'anonymous';
    video.muted = true;
    video.playsInline = true;
    video.preload = 'metadata';
    
    // Set a timeout to prevent hanging
    const timeout = setTimeout(() => {
      console.warn('Thumbnail generation timed out');
      resolve(null);
    }, 10000);
    
    video.onloadedmetadata = () => {
      console.log('Video metadata loaded, duration:', video.duration);
      // Seek to specified time offset or percentage of video duration
      const seekTime = timeOffset < 1 ? video.duration * timeOffset : timeOffset;
      const finalSeekTime = Math.min(seekTime, video.duration - 0.1);
      console.log('Seeking to time:', finalSeekTime);
      video.currentTime = finalSeekTime;
    };
    
    video.onseeked = () => {
      try {
        clearTimeout(timeout);
        
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          resolve(null);
          return;
        }
        
        // Calculate dimensions maintaining aspect ratio
        let { videoWidth, videoHeight } = video;
        
        if (videoWidth > maxWidth || videoHeight > maxHeight) {
          const aspectRatio = videoWidth / videoHeight;
          
          if (videoWidth > videoHeight) {
            videoWidth = maxWidth;
            videoHeight = maxWidth / aspectRatio;
          } else {
            videoHeight = maxHeight;
            videoWidth = maxHeight * aspectRatio;
          }
        }
        
        canvas.width = videoWidth;
        canvas.height = videoHeight;
        
        // Draw video frame to canvas
        ctx.drawImage(video, 0, 0, videoWidth, videoHeight);
        
        // Convert to data URL
        const dataUrl = canvas.toDataURL('image/jpeg', quality);
        resolve(dataUrl);
      } catch (error) {
        clearTimeout(timeout);
        console.error('Error generating thumbnail:', error);
        resolve(null);
      }
    };
    
    video.onerror = () => {
      clearTimeout(timeout);
      console.error('Error loading video for thumbnail generation');
      resolve(null);
    };
    
    video.src = videoUrl;
  });
};

/**
 * Check if the current device is mobile
 */
export const isMobileDevice = (): boolean => {
  return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         (window as any).Capacitor !== undefined;
};

/**
 * Get cached thumbnail or generate new one
 */
export const getCachedOrGenerateThumbnail = async (
  videoId: string,
  videoUrl: string,
  options: ThumbnailOptions = {}
): Promise<string | null> => {
  const cacheKey = `thumbnail-${videoId}`;
  
  try {
    // Check cache first
    const cachedThumbnail = sessionStorage.getItem(cacheKey);
    if (cachedThumbnail) {
      return cachedThumbnail;
    }
    
    // Generate new thumbnail
    const thumbnail = await generateVideoThumbnail(videoUrl, options);
    
    if (thumbnail) {
      // Cache the thumbnail
      try {
        sessionStorage.setItem(cacheKey, thumbnail);
      } catch (error) {
        console.warn('Could not cache thumbnail:', error);
      }
    }
    
    return thumbnail;
  } catch (error) {
    console.error('Error in getCachedOrGenerateThumbnail:', error);
    return null;
  }
};

/**
 * Clear thumbnail cache for a specific video or all videos
 */
export const clearThumbnailCache = (videoId?: string): void => {
  if (videoId) {
    sessionStorage.removeItem(`thumbnail-${videoId}`);
  } else {
    // Clear all thumbnail cache
    const keys = Object.keys(sessionStorage);
    keys.forEach(key => {
      if (key.startsWith('thumbnail-')) {
        sessionStorage.removeItem(key);
      }
    });
  }
};
